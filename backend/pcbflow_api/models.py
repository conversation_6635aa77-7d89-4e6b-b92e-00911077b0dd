import uuid
import os
import json
import re
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
from decimal import Decimal


def upload_board_avatar(instance, filename):
    """Upload path for board avatar images"""
    return f'pcbflow/boards/{instance.id}/avatar/{filename}'


def upload_board_files(instance, filename):
    """Upload path for board files (BOM, iBOM, Gerber)"""
    return f'pcbflow/boards/{instance.id}/files/{filename}'


def upload_gerber_files(instance, filename):
    """Upload path for gerber files"""
    return f'pcbflow/boards/{instance.id}/gerber/{filename}'


def slugify_part_number(part_number):
    """Convert part number to URL-safe slug format"""
    if not part_number:
        return ''

    # Convert to lowercase and replace special characters with hyphens
    slug = re.sub(r'[^a-zA-Z0-9\-_]', '-', part_number.lower())
    # Remove multiple consecutive hyphens
    slug = re.sub(r'-+', '-', slug)
    # Remove leading and trailing hyphens
    slug = slug.strip('-')
    return slug


class Component(models.Model):
    """PCB Component/Part model"""
    id = models.CharField(max_length=150, unique=True, primary_key=True, editable=False)
    part_number = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=200)
    package = models.CharField(max_length=50, blank=True, null=True)
    footprint = models.CharField(max_length=100, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    stock_quantity = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    min_stock = models.IntegerField(default=0, validators=[MinValueValidator(0)])

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['part_number']
        verbose_name = 'Component'
        verbose_name_plural = 'Components'

    def __str__(self):
        return f"{self.part_number} - {self.name}"

    def save(self, *args, **kwargs):
        # Auto-generate id from part_number if not set
        if not self.id:
            self.id = slugify_part_number(self.part_number)
        super().save(*args, **kwargs)

    @property
    def is_low_stock(self):
        """Check if component is below minimum stock level"""
        return self.stock_quantity <= self.min_stock

    @property
    def preferred_vendor(self):
        """Get the preferred vendor for this component"""
        return self.vendors.filter(is_preferred=True).first()


class Vendor(models.Model):
    """Vendor/Supplier model"""
    name = models.CharField(max_length=200, unique=True)
    website = models.URLField(blank=True, null=True)
    contact_info = models.TextField(blank=True, null=True)
    active = models.BooleanField(default=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = 'Vendor'
        verbose_name_plural = 'Vendors'

    def __str__(self):
        return self.name


class ComponentVendor(models.Model):
    """Many-to-many relationship between Component and Vendor with pricing"""
    CURRENCY_CHOICES = [
        ('USD', 'US Dollar'),
        ('VND', 'Vietnamese Dong'),
        ('EUR', 'Euro'),
        ('CNY', 'Chinese Yuan'),
    ]

    component = models.ForeignKey(Component, on_delete=models.CASCADE, related_name='vendors')
    vendor = models.ForeignKey(Vendor, on_delete=models.CASCADE, related_name='components')
    price = models.DecimalField(max_digits=10, decimal_places=2, validators=[MinValueValidator(Decimal('0.01'))])
    currency = models.CharField(max_length=3, choices=CURRENCY_CHOICES, default='USD')
    link = models.URLField(blank=True, null=True)
    is_preferred = models.BooleanField(default=False)
    moq = models.IntegerField(default=1, validators=[MinValueValidator(1)])
    active = models.BooleanField(default=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['component', 'vendor']
        ordering = ['price']
        verbose_name = 'Component Vendor'
        verbose_name_plural = 'Component Vendors'

    def __str__(self):
        return f"{self.component.part_number} - {self.vendor.name} ({self.price} {self.currency})"

    def save(self, *args, **kwargs):
        # If this is set as preferred, unset other preferred vendors for this component
        if self.is_preferred:
            ComponentVendor.objects.filter(
                component=self.component,
                is_preferred=True
            ).exclude(pk=self.pk).update(is_preferred=False)
        super().save(*args, **kwargs)


class Board(models.Model):
    """PCB Board model"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True, null=True)
    version = models.CharField(max_length=100, blank=True, null=True)

    # Files
    avatar = models.ImageField(upload_to=upload_board_avatar, blank=True, null=True)
    bom_file = models.FileField(upload_to=upload_board_files, blank=True, null=True)
    ibom_file = models.FileField(upload_to=upload_board_files, blank=True, null=True)
    gerber_path = models.CharField(max_length=500, blank=True, null=True)  # Path to gerber files directory

    # Original uploaded file info
    original_filename = models.CharField(max_length=255, blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    uploaded_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Board'
        verbose_name_plural = 'Boards'

    def __str__(self):
        return f"{self.name}-v{self.version}"

    @property
    def total_components(self):
        """Get total number of unique components in BOM"""
        return self.bom_items.count()

    @property
    def total_parts_per_board(self):
        """Get total number of parts per board (sum of all quantities)"""
        return sum(item.quantity_per_board for item in self.bom_items.all())


class BoardBOMItem(models.Model):
    """BOM (Bill of Materials) item for a board"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    board = models.ForeignKey(Board, on_delete=models.CASCADE, related_name='bom_items')
    component = models.ForeignKey(Component, on_delete=models.SET_NULL, null=True, blank=True)

    # BOM data from CSV
    reference_list = models.JSONField(default=list)  # List of reference designators (e.g., ["R1", "R2", "R3"])
    quantity_per_board = models.IntegerField(validators=[MinValueValidator(1)])

    # Additional BOM fields that might come from CSV
    value = models.CharField(max_length=100, blank=True, null=True)  # Component value (e.g., "10k", "100nF")
    footprint = models.CharField(max_length=100, blank=True, null=True)
    part_number = models.CharField(max_length=100, blank=True, null=True)  # From BOM CSV
    description = models.CharField(max_length=500, blank=True, null=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['board', 'part_number']
        ordering = ['part_number']
        verbose_name = 'Board BOM Item'
        verbose_name_plural = 'Board BOM Items'

    def __str__(self):
        component_name = self.component.name if self.component else self.part_number
        return f"{self.board.name} - {component_name} (x{self.quantity_per_board})"

    @property
    def reference_designators(self):
        """Get comma-separated list of reference designators"""
        return ", ".join(self.reference_list) if self.reference_list else ""


class InventoryTransaction(models.Model):
    """Track inventory movements"""
    TRANSACTION_TYPES = [
        ('in', 'Stock In'),
        ('out', 'Stock Out'),
        ('adjustment', 'Adjustment'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    component = models.ForeignKey(Component, on_delete=models.CASCADE, related_name='transactions')
    quantity = models.IntegerField()  # Can be negative for stock out
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    note = models.TextField(blank=True, null=True)
    ref_link = models.URLField(blank=True, null=True)

    # Related board if this transaction is for board assembly
    related_board = models.ForeignKey(Board, on_delete=models.SET_NULL, null=True, blank=True)
    board_quantity = models.IntegerField(null=True, blank=True)  # Number of boards assembled

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Inventory Transaction'
        verbose_name_plural = 'Inventory Transactions'

    def __str__(self):
        return f"{self.component.part_number} - {self.transaction_type} ({self.quantity})"

    def save(self, *args, **kwargs):
        # Update component stock quantity
        if self.pk is None:  # Only on creation
            if self.transaction_type == 'in' or (self.transaction_type == 'adjustment' and self.quantity > 0):
                self.component.stock_quantity += abs(self.quantity)
            elif self.transaction_type == 'out' or (self.transaction_type == 'adjustment' and self.quantity < 0):
                self.component.stock_quantity -= abs(self.quantity)
                # Ensure stock doesn't go negative
                if self.component.stock_quantity < 0:
                    self.component.stock_quantity = 0

            self.component.save()

        super().save(*args, **kwargs)
