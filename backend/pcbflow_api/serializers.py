from rest_framework import serializers
from django.contrib.auth.models import User
from .models import Component, Vendor, ComponentVendor, Board, BoardBOMItem, InventoryTransaction


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'username', 'first_name', 'last_name', 'email']


class VendorSerializer(serializers.ModelSerializer):
    component_count = serializers.SerializerMethodField()

    class Meta:
        model = Vendor
        fields = ['id', 'name', 'website', 'contact_info', 'active', 'created_at', 'updated_at', 'component_count']
        read_only_fields = ['created_at', 'updated_at']

    def get_component_count(self, obj):
        return obj.components.count()


class ComponentVendorSerializer(serializers.ModelSerializer):
    vendor = VendorSerializer(read_only=True)
    vendor_id = serializers.IntegerField(write_only=True)
    component = serializers.CharField(source='component.part_number', read_only=True)

    class Meta:
        model = ComponentVendor
        fields = [
            'id', 'component', 'vendor', 'vendor_id',
            'price', 'currency', 'link', 'is_preferred', 'moq', 'active',
            'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']


class ComponentSerializer(serializers.ModelSerializer):
    vendors = ComponentVendorSerializer(many=True, read_only=True)
    created_by = UserSerializer(read_only=True)
    is_low_stock = serializers.ReadOnlyField()
    preferred_vendor = ComponentVendorSerializer(read_only=True)
    
    class Meta:
        model = Component
        fields = [
            'id', 'part_number', 'name', 'package', 'footprint', 'description',
            'stock_quantity', 'min_stock', 'is_low_stock', 'vendors', 'preferred_vendor',
            'created_at', 'updated_at', 'created_by'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'is_low_stock']


class ComponentListSerializer(serializers.ModelSerializer):
    """Simplified serializer for component lists"""
    is_low_stock = serializers.ReadOnlyField()
    vendor_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Component
        fields = ['id', 'part_number', 'name', 'package', 'stock_quantity', 'min_stock', 'is_low_stock', 'vendor_count']
    
    def get_vendor_count(self, obj):
        return obj.vendors.filter(active=True).count()


class BoardBOMItemSerializer(serializers.ModelSerializer):
    component = ComponentSerializer(read_only=True)
    component_part_number = serializers.CharField(write_only=True, required=False, allow_blank=True)
    reference_designators = serializers.ReadOnlyField()
    
    class Meta:
        model = BoardBOMItem
        fields = [
            'id', 'component', 'component_part_number', 'reference_list', 'quantity_per_board',
            'value', 'footprint', 'part_number', 'description', 'reference_designators', 'created_at'
        ]
        read_only_fields = ['id', 'created_at', 'reference_designators']
    
    def create(self, validated_data):
        component_part_number = validated_data.pop('component_part_number', None)
        if component_part_number:
            try:
                component = Component.objects.get(part_number=component_part_number)
                validated_data['component'] = component
            except Component.DoesNotExist:
                pass
        return super().create(validated_data)


class BoardSerializer(serializers.ModelSerializer):
    bom_items = BoardBOMItemSerializer(many=True, read_only=True)
    uploaded_by = UserSerializer(read_only=True)
    total_components = serializers.ReadOnlyField()
    total_parts_per_board = serializers.ReadOnlyField()
    
    class Meta:
        model = Board
        fields = [
            'id', 'name', 'description', 'version', 'avatar', 'bom_file', 'ibom_file', 'gerber_path',
            'original_filename', 'bom_items', 'total_components', 'total_parts_per_board',
            'created_at', 'updated_at', 'uploaded_by'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'total_components', 'total_parts_per_board']


class BoardListSerializer(serializers.ModelSerializer):
    """Simplified serializer for board lists"""
    uploaded_by = UserSerializer(read_only=True)
    total_components = serializers.ReadOnlyField()
    total_parts_per_board = serializers.ReadOnlyField()
    
    class Meta:
        model = Board
        fields = [
            'id', 'name', 'description', 'avatar', 'total_components', 'total_parts_per_board',
            'created_at', 'uploaded_by'
        ]


class InventoryTransactionSerializer(serializers.ModelSerializer):
    component = ComponentSerializer(read_only=True)
    component_part_number = serializers.CharField(write_only=True)
    related_board = BoardSerializer(read_only=True)
    related_board_id = serializers.UUIDField(write_only=True, required=False, allow_null=True)
    created_by = UserSerializer(read_only=True)
    
    class Meta:
        model = InventoryTransaction
        fields = [
            'id', 'component', 'component_part_number', 'quantity', 'transaction_type', 'note', 'ref_link',
            'related_board', 'related_board_id', 'board_quantity', 'created_at', 'created_by'
        ]
        read_only_fields = ['id', 'created_at']
    
    def create(self, validated_data):
        component_part_number = validated_data.pop('component_part_number')
        related_board_id = validated_data.pop('related_board_id', None)
        
        try:
            component = Component.objects.get(part_number=component_part_number)
            validated_data['component'] = component
        except Component.DoesNotExist:
            raise serializers.ValidationError({'component_part_number': 'Component not found'})
        
        if related_board_id:
            try:
                board = Board.objects.get(id=related_board_id)
                validated_data['related_board'] = board
            except Board.DoesNotExist:
                raise serializers.ValidationError({'related_board_id': 'Board not found'})
        
        return super().create(validated_data)


# Specialized serializers for specific use cases
class ComponentStockSerializer(serializers.ModelSerializer):
    """Serializer for stock adjustment operations"""
    class Meta:
        model = Component
        fields = ['id', 'part_number', 'name', 'stock_quantity', 'min_stock']
        read_only_fields = ['id', 'part_number', 'name']


class BoardCalculationSerializer(serializers.Serializer):
    """Serializer for board quantity calculations"""
    board_quantity = serializers.IntegerField(min_value=1)
    
    def validate_board_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("Board quantity must be greater than 0")
        return value


class ComponentShortageSerializer(serializers.Serializer):
    """Serializer for component shortage information"""
    component = ComponentSerializer(read_only=True)
    required_quantity = serializers.IntegerField()
    available_quantity = serializers.IntegerField()
    shortage_quantity = serializers.IntegerField()
    bom_item = BoardBOMItemSerializer(read_only=True)


class VendorInfoSerializer(serializers.Serializer):
    """Serializer for adding vendor information to components"""
    component_part_number = serializers.CharField()
    vendor_name = serializers.CharField()
    vendor_website = serializers.URLField(required=False, allow_blank=True)
    vendor_contact_info = serializers.CharField(required=False, allow_blank=True)
    price = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=0.01)
    currency = serializers.ChoiceField(choices=ComponentVendor.CURRENCY_CHOICES, default='USD')
    link = serializers.URLField(required=False, allow_blank=True)
    moq = serializers.IntegerField(min_value=1, default=1)
    is_preferred = serializers.BooleanField(default=False)
