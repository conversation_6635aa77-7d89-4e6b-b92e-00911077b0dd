# Giới thiệu phần cứng

<p align="center">
  <img 
    src="../_static/bee-board-v2/board.png" 
    alt="BeE Board V2 tổng quan" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

## 1. Tổng quan

**BeE Board V2** là bo mạch học lập trình và robot thông minh được phát triển bởi **BeE STEM Solutions** 🇻🇳.
Bo mạch sử dụng **ESP32-S3** làm vi điều khiển trung tâm, hỗ trợ cả **kết nối USB, Bluetooth và Wi-Fi**,
tích hợp nhiều cảm biến và thiết bị ngoại vi sẵn sàng cho việc dạy và học STEM.

---

## 2. <PERSON><PERSON> đồ tổng quan và thành phần

<p align="center">
  <img 
    src="../_static/bee-board-v2/board-layout.jpg" 
    alt="BeE Board V2 Layout" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

| Ký hiệu | Thành phần           | Mô tả                                      |
| ------- | -------------------- | ------------------------------------------ |
| ①       | LED RGB 1 (LED1)     | Điều khiển qua `bee.led1`                  |
| ②       | LED RGB 2 (LED2)     | Điều khiển qua `bee.led2`                  |
| ③       | Nút nhấn A           | Tương ứng `bee.buttonA`                    |
| ④       | Nút nhấn B           | Tương ứng `bee.buttonB`                    |
| ⑤       | Màn hình OLED 0.96"  | Giao tiếp I2C, đối tượng `bee.oled`        |
| ⑥       | Cảm biến IMU MPU6050 | Đo nghiêng, rung, `bee.imu`                |
| ⑦       | Loa Buzzer           | Phát âm, nhạc, `bee.buzzer`                |
| ⑧       | Cổng Motor M1        | Điều khiển DC motor, `bee.motor1`          |
| ⑨       | Cổng Motor M2        | Điều khiển DC motor, `bee.motor2`          |
| ⑩       | Cổng Servo (S1–S4)   | Điều khiển servo, `bee.servo1–4`           |
| ⑪       | Cổng Grove 1         | `bee.PORT1` (GPIO, PWM, Analog, I2C, UART) |
| ⑫       | Cổng Grove 2         | `bee.PORT2` (GPIO, PWM, Analog, I2C, UART) |
| ⑬       | Cổng Grove 3         | `bee.PORT3` (GPIO, PWM, Analog, I2C, UART) |
| ⑭       | Cổng Grove 4         | `bee.PORT4` (GPIO, PWM, Analog, I2C, UART) |
| ⑮       | Cổng Grove 5         | `bee.PORT5` (GPIO, PWM, Analog, I2C, UART) |
| ⑯       | Cổng Grove 6         | `bee.PORT6` (GPIO, PWM, Analog, I2C, UART) |
| ⑰       | Cổng USB-C           | Giao tiếp lập trình qua chip **CH340C**    |
| ⑱       | Nút Boot             | Vào chế độ flashing firmware               |
| ⑲       | Nút Reset            | Reset board                                |
| ⑳       | LED nguồn (PWR)      | Hiển thị trạng thái nguồn                  |
| ㉑      | Nút nguồn            | Bật/tắt nguồn ngoài                        |

---

## 3. Thông số kỹ thuật chi tiết

| Thành phần         | Thông số                       |
| ------------------ | ------------------------------ |
| Vi điều khiển      | ESP32-S3-WROOM-1-N8R8          |
| CPU                | Tensilica LX7 Dual-core 240MHz |
| RAM                | 512 KB (PSRAM 8 MB)            |
| Flash              | 8 MB                           |
| USB Interface      | **CH340C**                     |
| Nguồn cấp          | 5V qua USB-C hoặc pin Li-ion   |
| Giao tiếp          | UART, I2C, PWM, ADC, SPI       |
| Hỗ trợ kết nối     | USB, Wi-Fi, Bluetooth          |
| Tương thích cơ khí | LEGO Technic Frame 7×11        |

---

## 4. Mapping cổng & chân kết nối

| Cổng        | Chức năng chính        | GPIO       | Ghi chú                   |
| ----------- | ---------------------- | ---------- | ------------------------- |
| PORT1       | Digital / PWM / Analog | GPIO 1     | Kết nối module Grove      |
| PORT2       | Digital / PWM / Analog | GPIO 2     |                           |
| PORT3       | Digital / PWM / Analog | GPIO 3     |                           |
| PORT4       | Digital / PWM / Analog | GPIO 4     |                           |
| PORT5       | Digital / PWM / Analog | GPIO 5     |                           |
| PORT6       | Digital / PWM / Analog | GPIO 6     |                           |
| M1 / M2     | Motor Driver           | TB6612     | Điều khiển DC motor       |
| S1–S4       | Servo Output           | `internal` | 12-bit resolution         |
| OLED        | Display 0.96"          | `internal` |                           |
| IMU         | MPU6050                | `internal` |                           |
| Buzzer      | Âm thanh               | `internal` |                           |
| LED1 / LED2 | NeoPixel RGB           | `internal` | WS2812 RGB 16,7 triệu màu |
| Buttons     | A/B                    | `internal` | Có pull-up nội bộ         |

---

## 5. Nguồn cấp và công tắc

<p align="center">
  <img 
    src="../_static/bee-board-v2/power-section.jpg" 
    alt="Power Section" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

-   **Cổng USB-C:** cấp nguồn và nạp chương trình.
-   **Pin Li-ion 14500 / 18650:** có thể cấp qua module sạc ngoài.
-   **Nút Power:** bật/tắt nguồn tổng.
-   **LED nguồn (PWR):** hiển thị trạng thái bật/tắt bo.

---

## 6. Vi xử lý trung tâm (ESP32-S3)

<p align="center">
  <img 
    src="../_static/bee-board-v2/esp32-s3.png" 
    alt="ESP32-S3 Module" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

Bo sử dụng module **ESP32-S3-WROOM-1-N8R8**, có:

-   2 nhân xử lý (dual-core 240 MHz)
-   Tích hợp Wi-Fi & Bluetooth 5.0
-   Hỗ trợ xử lý AI/ML cơ bản
-   Tốc độ cao và tiêu thụ điện năng thấp
-   Chạy được **MicroPython** và **firmware BeE Brain**

---

## 7. Kết nối và giao tiếp

| Giao tiếp          | Mô tả                                 |
| ------------------ | ------------------------------------- |
| **USB-C (CH340C)** | Giao tiếp Serial, nạp code từ web     |
| **Bluetooth BLE**  | Giao tiếp không dây với BeE IDE       |
| **OTA (Wi-Fi)**    | Cập nhật chương trình qua Wifi        |
| **I2C Bus**        | Dành cho OLED, IMU, và module mở rộng |
| **PWM**            | Điều khiển motor, servo, LED RGB      |
| **Analog (ADC)**   | Đọc cảm biến analog (qua PORT1–6)     |

---

## 8. Tương thích LEGO Technic

<p align="center">
  <img 
    src="../_static/bee-board-v2/lego-mount.jpg" 
    alt="LEGO Frame Mount" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

-   BeE Board V2 có kích thước tương thích **khung LEGO Technic 7×11**.
-   Các lỗ bắt vít tiêu chuẩn 4.8 mm.
-   Học sinh có thể kết hợp BeE Board với LEGO để tạo robot di chuyển, xe tự hành, hoặc cánh tay máy.

---

## 9. Các đối tượng trong lập trình Python

| Đối tượng                    | Chức năng             | Ví dụ sử dụng                                  |
| ---------------------------- | --------------------- | ---------------------------------------------- |
| `bee.led1`, `bee.led2`       | LED RGB tích hợp      | `bee.led1.set_rgb(255,0,0)`                    |
| `bee.buttonA`, `bee.buttonB` | Nút nhấn              | `bee.buttonA.is_pressed()`                     |
| `bee.oled`                   | Màn hình hiển thị     | `bee.oled.write("Hello",0,0,1)`                |
| `bee.imu`                    | Cảm biến nghiêng, lắc | `bee.imu.rollDeg`                              |
| `bee.motor1`, `bee.motor2`   | Motor DC              | `bee.motor1.speed(60)`                         |
| `bee.servo1–4`               | Servo RC              | `bee.servo3.position(90)`                      |
| `bee.buzzer`                 | Loa buzzer            | `bee.buzzer.play_tone(bee.buzzer.TONES['C5'])` |
| `bee.PORT1–6`                | GPIO mở rộng          | `bee.digital_read(bee.PORT1)`                  |

---

## 10. Tài liệu tham khảo

-   [Hướng dẫn BeE IDE](../2.bee-ide/index.md)
-   [Ví dụ lập trình với BeE Board V2](../3.examples/2.led-example.md)
-   [Cài đặt thư viện mở rộng trên BeE IDE](../4.extensions/1.index.md)
-   [Giới thiệu BeE STEM Solutions](../5.about/about.md)
