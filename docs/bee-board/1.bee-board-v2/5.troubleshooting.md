# Xử lý lỗi

---

## 1. <PERSON><PERSON>ớ<PERSON> thiệu

Phần này gi<PERSON><PERSON> bạn **x<PERSON><PERSON> định và khắc phục các lỗi thường gặp** khi sử dụng **BeE Board V2** với **BeE IDE** hoặc **BeE Python**.
Hãy làm theo từng bước, và bạn sẽ nhanh chóng tìm ra nguyên nhân 😎

---

## 2. Lỗi khi kết nối USB

| Triệu chứng                           | Nguyên nhân có thể                             | Cách khắc phục                                                                                                          |
| ------------------------------------- | ---------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------- |
| Không thấy cổng COM/USB khi cắm board | Chưa cài driver CH340C                         | Tải và cài đặt driver tại [đây](https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board) |
| Đã cắm USB nhưng IDE không nhận board | Cáp USB chỉ sạc, không truyền dữ liệu          | Dùng cáp USB-C hỗ trợ **data** (loại tốt, 4 dây)                                                                        |
| IDE hiển thị “Kết nối thất bại”       | Board đang chạy chương trình chiếm cổng serial | Nhấn **Reset** trước khi chọn kết nối USB                                                                               |
| Board tự ngắt sau vài giây            | Nguồn USB yếu hoặc không ổn định               | Dùng cổng USB máy tính trực tiếp, tránh hub hoặc dây quá dài                                                            |

---

## 3. Lỗi khi nạp code OTA (Wi-Fi)

| Triệu chứng                        | Nguyên nhân có thể                      | Cách khắc phục                                      |
| ---------------------------------- | --------------------------------------- | --------------------------------------------------- |
| OLED không hiển thị IP OTA         | Wi-Fi yếu hoặc nhập sai mật khẩu        | Kiểm tra Wi-Fi, nhập lại đúng SSID và Password      |
| IDE báo “Connot connect IP OTA”    | Board và máy tính không cùng mạng Wi-Fi | Đảm bảo cả 2 thiết bị trong cùng mạng LAN           |
| OTA nạp xong nhưng code không chạy | Lỗi trong code mới                      | Reset lại board (`bee.init_bee()`) để khởi động lại |
| Không thấy thông báo OTA trong IDE | Board chưa khởi tạo OTA                 | Thêm block “Setup OTA” trước khi chạy               |

---

## 4. Lỗi LED hoặc Button

| Triệu chứng             | Nguyên nhân có thể          | Cách khắc phục                                                   |
| ----------------------- | --------------------------- | ---------------------------------------------------------------- |
| LED không sáng          | Lỗi code hoặc chưa khởi tạo | Không chạy lệnh `bee.led1.off()` ngay sau `bee.led1.on()`        |
| LED chỉ sáng 1 màu      | Tham số R,G,B sai           | Kiểm tra `bee.led1.set_rgb(255,0,0)` (R=255, G=0, B=0 là màu đỏ) |
| Nút nhấn không phản hồi | Board chưa cập nhật dữ liệu | Dùng `bee.buttonA.is_pressed()` trong vòng lặp `while True:`     |

---

## 5. Lỗi màn hình OLED

| Triệu chứng                                 | Nguyên nhân có thể                | Cách khắc phục                                      |
| ------------------------------------------- | --------------------------------- | --------------------------------------------------- |
| Màn hình không sáng                         | Code lỗi                          | Reset board và thử `bee.oled.clear()`               |
| Hiển thị sai ký tự / nhấp nháy              | Chạy quá nhiều lệnh ghi liên tiếp | Thêm `time.sleep(0.1)` giữa các lệnh                |
| Không hiển thị chữ mới / nội dung bị ghi đè | Chưa xóa nội dung cũ              | Gọi `bee.oled.clear()` trước khi `bee.oled.write()` |

---

## 6. Lỗi loa Buzzer

| Triệu chứng       | Nguyên nhân có thể                                | Cách khắc phục                         |
| ----------------- | ------------------------------------------------- | -------------------------------------- |
| Không nghe tiếng  | Chưa gọi `bee.buzzer.play_tone()` hoặc volume = 0 | Dùng `bee.buzzer.set_volume(80)`       |
| Tiếng rè hoặc nhỏ | Board thiếu nguồn                                 | Cấp nguồn qua USB ổn định hoặc pin đầy |
| Kêu liên tục      | Quên gọi `bee.buzzer.be_quiet()`                  | Thêm lệnh tắt sau khi phát âm thanh    |

---

## 7. Lỗi khi lập trình Python

| Lỗi / Thông báo                 | Nguyên nhân                  | Cách khắc phục                                 |
| ------------------------------- | ---------------------------- | ---------------------------------------------- |
| `NameError: bee is not defined` | Quên import thư viện         | Thêm `from BeeBrain import bee` ở đầu file     |
| `OSError: [Errno 19] ENODEV`    | Board không kết nối đúng     | Kiểm tra lại cổng USB / COM                    |
| `SyntaxError`                   | Sai cú pháp Python           | Kiểm tra dấu `:` hoặc thụt dòng                |
| Không thấy kết quả OLED         | Code chạy nhưng không render | Thêm `bee.oled.clear()` và `time.sleep()` ngắn |

---

## 8. Lỗi trong BeE IDE

| Triệu chứng                       | Nguyên nhân có thể           | Cách khắc phục                           |
| --------------------------------- | ---------------------------- | ---------------------------------------- |
| Khối lệnh không nạp được          | Chưa kết nối cổng Serial     | Nhấn “Kết nối” và chọn đúng cổng COM     |
| Block “Setup OTA” không hoạt động | Wi-Fi nhập sai hoặc chưa lưu | Kiểm tra thông tin Wi-Fi                 |
| Khối LED không sáng               | Chưa chọn số LED đúng        | Kiểm tra giá trị trong khối “LED số ...” |

---

## 9. Lỗi trong BeE Python (Code Editor)

| Triệu chứng                    | Nguyên nhân có thể                         | Cách khắc phục                                            |
| ------------------------------ | ------------------------------------------ | --------------------------------------------------------- |
| IDE không nhận board           | Chưa cấp quyền Serial trong trình duyệt    | Nhấn “Cho phép truy cập thiết bị USB” khi trình duyệt hỏi |
| Code nạp xong nhưng không chạy | File `main.py` cũ bị lỗi                   | Nhấn nút reset trên board                                 |
| Không lưu được code            | Trình duyệt bị hạn chế quyền Local Storage | Cho phép BeE Python lưu file trong trình duyệt            |
| Không thấy log trong console   | Board không trả về dữ liệu                 | Reset board và thử lại                                    |

---

## 10. Sự cố nguồn điện

| Triệu chứng                  | Nguyên nhân                    | Cách khắc phục                                      |
| ---------------------------- | ------------------------------ | --------------------------------------------------- |
| Board không sáng đèn nguồn   | Chưa bật công tắc hoặc pin yếu | Sạc pin hoặc cấp nguồn qua USB                      |
| Board khởi động lại liên tục | Nguồn USB không đủ dòng        | Dùng cáp USB tốt và cổng 5V-2A                      |
| Board quá nóng               | Chập mạch hoặc lỗi nguồn       | Ngắt kết nối, kiểm tra lại mạch và module cắm ngoài |

---

## 11. Kiểm tra nhanh bằng code test

Dán đoạn code sau vào BeE Python để kiểm tra nhanh phần cứng:

```python
from BeeBrain import bee
import time

bee.init_bee()
bee.led1.set_rgb(255,0,0)
bee.led2.set_rgb(0,255,0)
bee.oled.write("BeE OK!", 0, 0, 1)
bee.buzzer.play_tone(bee.buzzer.TONES['C5'])
time.sleep(1)
bee.buzzer.be_quiet()
bee.oled.clear()
print("Test hoàn tất!")
```

Nếu OLED hiển thị chữ “BeE OK!” và nghe tiếng beep,
nghĩa là board hoạt động bình thường 🎉

---

## 12. Liên hệ hỗ trợ

Nếu bạn đã thử các bước trên mà vẫn gặp lỗi,
hãy liên hệ đội ngũ BeE STEM Solutions để được hỗ trợ:

-   **Website:** [https://beestemsolutions.com.vn](https://beestemsolutions.com.vn)
-   **Facebook:** [beestemsolutions.com.vn](https://facebook.com/beestemsolutions.com.vn)
-   **TikTok:** [@beestem.vn](https://tiktok.com/@beestemsolutions.com.vn)
-   **Email:** [<EMAIL>](mailto:<EMAIL>)
-   **Hotline:** [0987845231](tel:0987845231)
