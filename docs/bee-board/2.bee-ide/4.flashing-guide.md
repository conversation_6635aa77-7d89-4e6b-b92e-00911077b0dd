# Nạp chương trình

---

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

BeE IDE hỗ trợ **3 cách** để nạp chương trình lên BeE Board:

| Phương thức   | <PERSON><PERSON> tả                                         | Khi nào dùng?               |
| ------------- | --------------------------------------------- | --------------------------- |
| **USB**       | Kết nối BeE Board với máy tính bằng cáp USB-C | Lập trình và kiểm tra       |
| **OTA**       | Cập nhật code không dây qua Wi-Fi             | Board đã kết nối mạng Wi-Fi |
| **Bluetooth** | Cập nhật code không dây qua Bluetooth         | Board đã kết nối Bluetooth  |

> 💡 **Lưu ý:** <PERSON><PERSON><PERSON> chắc chắn rằng bạn đã cài đặt **USB Serial Driver** cho BeE Board V2. Xem [tại đây](https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board) để biết thêm thông tin.

---

## 2. Nạp chương trình qua USB

1. Cắm cáp USB-C vào BeE Board và máy tính
2. Mở BeE IDE hoặc BeE Python
3. Chọn **Kết nối → Chọn cổng Serial (COMx hoặc /dev/ttyUSBx hoặc /dev/cu.usbmodemxxxx)**
4. Chọn đúng cổng USB cho BeE Board
5. Nhấn nút **Run ▶️** để nạp chương trình

---

## 3. Cập nhật chương trình qua OTA

1. Trong Blockly, kéo khối **“Setup OTA Wi-Fi”**
2. Nhập **tên mạng (SSID)** và **mật khẩu**
3. Khi board kết nối thành công, màn hình OLED hiển thị **địa chỉ IP**
4. Trong BeE IDE → chọn **Upload OTA** → nhập IP để nạp code không dây

---

## 4. Cập nhật chương trình qua Bluetooth

1. Trong Blockly, kéo khối **“Setup Bluetooth”**
2. Nhập **tên thiết bị Bluetooth** (ví dụ: BeE-Board)
3. Khi board kết nối thành công, màn hình OLED hiển thị **địa chỉ Bluetooth**
4. Trong BeE IDE → chọn **Upload Bluetooth** → nhập địa chỉ để nạp code không dây
