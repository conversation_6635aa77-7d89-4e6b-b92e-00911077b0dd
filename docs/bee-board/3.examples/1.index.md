# Ví dụ lập trình

---

## Giới thiệu

BeE Board V2 tích hợp nhiều **module phần cứng thông minh** giúp học sinh làm quen với lập trình robot và IoT.
Dưới đây là danh sách các module **onboard** mà bạn có thể lập trình ngay bằng **BeE IDE** hoặc **BeE Python**.

---

## Các module tích hợp

| Module                   | Mô tả                                     | Đối tượng Python             | Ví dụ thực hành                       |
| ------------------------ | ----------------------------------------- | ---------------------------- | ------------------------------------- |
| **LED RGB**              | 2 đèn RGB tích hợp, đổi được 16 triệu màu | `bee.led1`, `bee.led2`       | [LED Example](2.led-example.md)       |
| **Nút nhấn A/B**         | Nhận tín hiệu từ người dùng               | `bee.buttonA`, `bee.buttonB` | [Button Example](3.button-example.md) |
| **Buzzer**               | Phát âm thanh, nhạc, cảnh báo             | `bee.buzzer`                 | [Buzzer Example](4.buzzer-example.md) |
| **OLED 0.96”**           | Hiển thị văn bản và thông tin cảm biến    | `bee.oled`                   | [OLED Example](5.oled-example.md)     |
| **Động cơ DC (M1–M2)**   | Điều khiển robot di chuyển                | `bee.motor1`, `bee.motor2`   | [Motor Example](6.motor-example.md)   |
| **Servo (S1–S4)**        | Điều khiển servo RC qua PCA9685           | `bee.servo1–4`               | [Servo Example](7.servo-example.md)   |
| **Cảm biến IMU MPU6050** | Đo nghiêng, lắc, phát hiện chuyển động    | `bee.imu`                    | [IMU Example](8.imu-example.md)       |

---

## Lập trình dễ dàng với BeE IDE

Truy cập BeE IDE để lập trình trực quan bằng khối kéo thả:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide)

<p align="center">
<img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

Các nhóm khối lệnh tương ứng:

-   **LED / RGB** – điều khiển màu sắc
-   **Button / Input** – xử lý nút nhấn
-   **Digital Read / Analog Read** - đọc tín hiệu từ cảm biến
-   **Digital Write / Analog Write** - ghi tín hiệu vào module
-   **Sound / Buzzer** – phát âm thanh
-   **Display / OLED** – hiển thị thông tin
-   **Motion / Motor, Servo, IMU** – điều khiển robot
-   **Advanced** – OTA, Bluetooth, Reset, Kết nối

---

## Lập trình nâng cao với BeE Python

Truy cập BeE Python để viết code thực tế:
👉 [https://beestemsolutions.com.vn/studio/python](https://beestemsolutions.com.vn/studio/python)

<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-python.png" 
    alt="BeE Python Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

BeE Python hỗ trợ đầy đủ thư viện `BeeBrain`, cho phép điều khiển tất cả module onboard chỉ với vài dòng code.

---

## Các ví dụ thực hành

```{toctree}
:maxdepth: 1
:caption: 💡 Các ví dụ cơ bản
2.led-example
3.button-example
4.buzzer-example
5.oled-example
6.motor-example
7.servo-example
8.imu-example
```

---

## Gợi ý mở rộng

| Dự án               | Mô tả                         | Kết hợp module  |
| ------------------- | ----------------------------- | --------------- |
| Đèn giao thông mini | LED đổi màu theo thời gian    | LED + Buzzer    |
| Robot né vật cản    | Tự quay khi rung hoặc nghiêng | IMU + Motor     |
| Piano mini          | Nhấn nút phát nhạc khác nhau  | Button + Buzzer |
| Hiển thị nhiệt độ   | Đọc DHT11 và hiển thị OLED    | DHT11 + OLED    |
| Servo chỉ hướng     | Servo xoay theo độ nghiêng    | IMU + Servo     |
