# LED RGB

![LED Example Cover](_static/bee-board-v2/examples/led-example-cover.jpg)

---

## M<PERSON><PERSON> tiêu

Trong bài này, bạn sẽ học cách:

-   Bật / tắt LED RGB tích hợp trên BeE Board V2
-   Đổi màu LED bằng khối Blockly hoặc code Python
-   Tạo hiệu ứng nhấp nháy đèn vui nhộn

---

## Phần cứng cần có

| Thành phần      | Số lượng | Ghi chú                  |
| --------------- | -------- | ------------------------ |
| BeE Board V2    | 1        | Bo mạch chính            |
| Cáp USB-C       | 1        | Kết nối máy tính         |
| Trình duyệt web | 1        | Chrome, Edge hoặc Safari |

> 💡 BeE Board V2 có 2 đèn LED RGB tích hợp: `bee.led1` và `bee.led2`

---

## 1. Lập trình với BeE IDE

Truy cập BeE IDE:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://ide.beestemsolutions.com.vn/studio/bee-ide)

### Bật LED 1 màu đỏ

Kéo các khối lệnh sau:

1. “Bật LED 1 RGB”
2. Nhập giá trị **R = 255, G = 0, B = 0**

![Blockly LED Red](_static/bee-ide/examples/led-red-blockly.jpg)

Khi nhấn **Run ▶️**, LED 1 trên BeE Board sẽ sáng màu đỏ.

---

### Hiệu ứng đổi màu

1. Dùng khối **“lặp lại mãi mãi”**
2. Thêm các khối LED với màu khác nhau
3. Chèn khối **“đợi 0.5 giây”** giữa mỗi lần đổi màu

![Blockly LED Loop](_static/bee-ide/examples/led-loop-blockly.jpg)

Kết quả: LED sẽ đổi màu liên tục giữa đỏ → xanh → vàng → tím

---

## 2. Lập trình với BeE Python

Truy cập BeE Python:
👉 [https://beestemsolutions.com.vn/studio/python](https://python.beestemsolutions.com.vn/studio/python)

### Bật LED 1 màu xanh dương

```python
from BeeBrain import bee

bee.init_bee()
bee.led1.set_rgb(0, 0, 255)
```

Khi nhấn **Run ▶️**, LED1 sẽ sáng màu xanh dương

---

### Tạo hiệu ứng nhấp nháy LED

```python
from BeeBrain import bee
import time

bee.init_bee()

while True:
    bee.led1.set_rgb(255, 0, 0)
    time.sleep(0.5)
    bee.led1.off()
    time.sleep(0.5)
```

Kết quả: LED đỏ sẽ nhấp nháy 2 lần mỗi giây.

---

### Đổi màu ngẫu nhiên (nâng cao)

```python
from BeeBrain import bee
import time, random

bee.init_bee()

while True:
    r = random.randint(0, 255)
    g = random.randint(0, 255)
    b = random.randint(0, 255)
    bee.led1.set_rgb(r, g, b)
    time.sleep(0.3)
```

LED sẽ đổi sang màu ngẫu nhiên liên tục 🎨

---

## 3. Thảo luận và mở rộng

| Câu hỏi                                    | Gợi ý                                                    |
| ------------------------------------------ | -------------------------------------------------------- |
| Làm sao để cả LED1 và LED2 cùng sáng?      | Dùng thêm `bee.led2.set_rgb()`                           |
| Làm sao để LED sáng dần rồi tắt dần?       | Dùng vòng lặp và thay đổi giá trị R, G, B theo thời gian |
| Có thể điều khiển LED bằng nút nhấn không? | Có! Dùng `bee.buttonA.is_pressed()` để bật/tắt LED       |

---

## 4. Kết luận

Trong bài học này, bạn đã:

-   Bật và tắt LED bằng Blockly và Python
-   Tạo hiệu ứng ánh sáng vui nhộn
-   Làm quen với lập trình vòng lặp và điều kiện
