# Buzzer

![Buzzer Example Cover](_static/bee-board-v2/examples/buzzer-example-cover.jpg)

---

## <PERSON><PERSON><PERSON> tiêu

Trong bài học này, bạn sẽ học cách:

-   Phát ra âm thanh và giai điệu bằng Buzzer
-   Kết hợp Buzzer với LED hoặc Button
-   Lập trình bài hát đơn giản trên BeE Board V2

---

## Phần cứng cần có

| Thành phần      | Số lượng | Ghi chú                  |
| --------------- | -------- | ------------------------ |
| BeE Board V2    | 1        | Tích hợp sẵn loa Buzzer  |
| Cáp USB-C       | 1        | Kết nối với máy tính     |
| Trình duyệt web | 1        | Chrome, Edge hoặc Safari |

> 💡 Buzzer của BeE Board V2 được điều khiển bằng `bee.buzzer`,
> hỗ trợ phát **nốt nhạc, tần số**, và **melody (giai điệu)**.

---

## 1. Lập trình với BeE IDE

Truy cập BeE IDE:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide)

### Phát 1 nốt nhạc

Kéo các khối:

1. “Phát nốt C5 trong 1/2 giây”
2. “Dừng buzzer”

![Blockly Buzzer Single Note](_static/bee-ide/examples/buzzer-single-blockly.jpg)

Khi chạy, bạn sẽ nghe **tiếng “Đô”**

---

### Phát chuỗi nốt nhạc

1. Kéo khối “Phát nốt ... trong ... giây” nhiều lần
2. Chọn các nốt: C5 → D5 → E5 → F5 → G5
3. Chèn khối “chờ 0.1 giây” giữa mỗi nốt

![Blockly Buzzer Melody](_static/bee-ide/examples/buzzer-melody-blockly.jpg)

Khi chạy, Buzzer sẽ phát giai điệu tăng dần rất vui tai

---

### Kết hợp với LED

1. Thêm khối “Bật LED1 màu đỏ” trước mỗi nốt
2. Sau khi phát xong, “Tắt LED1”

![Blockly Buzzer LED Combo](_static/bee-ide/examples/buzzer-led-blockly.jpg)

LED nhấp nháy theo từng nốt nhạc — tạo hiệu ứng **nhạc + ánh sáng**

---

## 2. Lập trình với BeE Python

Truy cập BeE Python:
👉 [https://beestemsolutions.com.vn/studio/python](https://beestemsolutions.com.vn/studio/python)

### Phát 1 nốt nhạc

```python
from BeeBrain import bee
import time

bee.buzzer.play_tone(bee.buzzer.TONES['C5'])
time.sleep(0.5)
bee.buzzer.be_quiet()
```

Khi chạy, Buzzer phát âm thanh “C5” trong nửa giây.

---

### Phát chuỗi nốt nhạc

```python
from BeeBrain import bee
import time

notes = ['C5', 'D5', 'E5', 'F5', 'G5']

for note in notes:
    bee.buzzer.play_tone(bee.buzzer.TONES[note])
    time.sleep(0.4)
    bee.buzzer.be_quiet()
    time.sleep(0.1)
```

Giai điệu đi lên lần lượt từ Đô → Rê → Mi → Fa → Son

---

### Kết hợp với LED

```python
from BeeBrain import bee
import time

notes = ['C5', 'E5', 'G5', 'C6']

for note in notes:
    bee.led1.set_rgb(255, 0, 0)
    bee.buzzer.play_tone(bee.buzzer.TONES[note])
    time.sleep(0.3)
    bee.led1.off()
    bee.buzzer.be_quiet()
    time.sleep(0.1)
```

Khi mỗi nốt được phát, LED sáng đỏ — tạo hiệu ứng nhạc nháy đèn

---

### Bài hát “Happy Birthday” (nâng cao)

```python
from BeeBrain import bee
import time

melody = [
    ('C5', 0.3), ('C5', 0.3), ('D5', 0.6), ('C5', 0.6),
    ('F5', 0.6), ('E5', 1.2),
    ('C5', 0.3), ('C5', 0.3), ('D5', 0.6), ('C5', 0.6),
    ('G5', 0.6), ('F5', 1.2)
]

for note, duration in melody:
    bee.buzzer.play_tone(bee.buzzer.TONES[note])
    time.sleep(duration)
    bee.buzzer.be_quiet()
    time.sleep(0.05)
```

BeE Board sẽ hát **“Happy Birthday”** bằng loa mini của nó!

---

## 3. Thảo luận và mở rộng

| Câu hỏi                               | Gợi ý                                                 |
| ------------------------------------- | ----------------------------------------------------- |
| Làm sao để thay đổi âm lượng?         | Dùng `bee.buzzer.set_volume(%)`                       |
| Làm sao để phát giai điệu ngẫu nhiên? | Dùng `random.choice()` để chọn nốt                    |
| Có thể kết hợp nút nhấn không?        | Dùng `bee.buttonA.is_pressed()` để phát nhạc khi nhấn |
| Có thể phát âm thanh cảnh báo không?  | Dùng các nốt cao/lặp nhanh như còi báo                |

---

## 4. Kết luận

Trong bài học này, bạn đã học cách:

-   Phát âm thanh bằng Buzzer
-   Lập trình giai điệu cơ bản
-   Kết hợp LED và Button để tạo trải nghiệm tương tác

> 💡 Hãy thử làm một robot biết “hát” và đổi màu LED theo nhịp nhé
