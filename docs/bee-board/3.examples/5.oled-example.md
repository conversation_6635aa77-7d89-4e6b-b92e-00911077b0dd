# Màn hình OLED

![OLED Example Cover](_static/bee-board-v2/examples/oled-example-cover.jpg)

---

## Mụ<PERSON> tiêu

Trong bài học này, bạn sẽ học cách:

-   Hiển thị văn bản lên màn hình OLED
-   Xóa màn hình, in nhiều dòng
-   Tạo hiệu ứng chữ chạy (scroll) và kết hợp hiển thị dữ liệu cảm biến

---

## Phần cứng cần có

| Thành phần      | Số lượng | Ghi chú                            |
| --------------- | -------- | ---------------------------------- |
| BeE Board V2    | 1        | Bo mạch chính có sẵn màn hình OLED |
| Cáp USB-C       | 1        | Kết nối với máy tính               |
| Trình duyệt web | 1        | Chrome, Edge hoặc Safari           |

> 💡 Màn hình OLED 0.96" của BeE Board V2 sử dụng giao tiếp I2C (địa chỉ **0x3C**).

---

## 1. Lập trình với BeE IDE

Truy cập BeE IDE:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide)

### Hiển thị dòng chữ “Hello BeE!”

Kéo các khối lệnh:

1. “Xóa màn hình OLED”
2. “Hiển thị chữ ‘Hello BeE!’ tại (0,0) với cỡ chữ 1”

![Blockly OLED Hello](_static/bee-ide/examples/oled-hello-blockly.jpg)

Khi nhấn **Run ▶️**, màn hình OLED sẽ hiển thị dòng chữ đầu tiên của bạn!

---

### Hiển thị nhiều dòng

1. “Hiển thị chữ ‘Nhiệt độ:’ tại (0,0)”
2. “Hiển thị chữ ‘25°C’ tại (0,1)”
3. “Hiển thị chữ ‘Độ ẩm:’ tại (0,2)”
4. “Hiển thị chữ ‘60%’ tại (0,3)”

![Blockly OLED Multi-line](_static/bee-ide/examples/oled-multiline-blockly.jpg)

> Toạ độ `(x, y)` trong OLED là theo **dòng (0–7)** và **cột ký tự**.

---

### Hiệu ứng chữ chạy

1. Dùng khối “xóa màn hình”
2. Trong vòng lặp “mãi mãi”, thay đổi vị trí hiển thị chữ theo trục X
3. Thêm khối “chờ 0.1 giây”

![Blockly OLED Scroll](_static/bee-ide/examples/oled-scroll-blockly.jpg)

---

## 2. Lập trình với BeE Python

Truy cập BeE Python:
👉 [https://python.beestemsolutions.com.vn](https://python.beestemsolutions.com.vn)

### Hiển thị chữ cơ bản

```python
from BeeBrain import bee

bee.oled.clear()
bee.oled.write("Hello BeE!", 0, 0, 1)
```

OLED hiển thị “Hello BeE!” ở góc trên bên trái của màn hình.

---

### Hiển thị nhiều dòng

```python
from BeeBrain import bee
import time

bee.oled.clear()
bee.oled.write("BeE STEM Solutions", 0, 0, 1)
bee.oled.write("ESP32-S3 Board", 0, 1, 1)
bee.oled.write("AI - IoT - Robotics", 0, 2, 1)
```

Khi chạy, OLED sẽ hiển thị 3 dòng thông tin như màn hình chào

---

### Hiệu ứng chữ chạy

```python
from BeeBrain import bee
import time

for x in range(0, 64, 2):
    bee.oled.clear()
    bee.oled.write("BeE STEM", x, 1, 1)
    time.sleep(0.05)
```

Dòng chữ “BeE STEM” sẽ di chuyển sang phải mượt mà

---

### Hiển thị cảm biến IMU

```python
from BeeBrain import bee
import time

while True:
    bee.imu.update()
    roll = bee.imu.rollDeg
    pitch = bee.imu.pitchDeg
    bee.oled.clear()
    bee.oled.write(f"Roll: {roll:.1f}", 0, 0, 1)
    bee.oled.write(f"Pitch: {pitch:.1f}", 0, 1, 1)
    time.sleep(0.2)
```

OLED hiển thị liên tục giá trị góc nghiêng của board theo thời gian

---

## 3. Thảo luận và mở rộng

| Câu hỏi                                      | Gợi ý                                                               |
| -------------------------------------------- | ------------------------------------------------------------------- |
| Làm sao để hiển thị cả nhiệt độ và độ ẩm?    | Kết hợp cảm biến DHT11 qua PORT1                                    |
| Làm sao để tạo menu chọn bằng nút A/B?       | Dùng `bee.buttonA.is_pressed()` và thay đổi dòng OLED               |
| Có thể hiển thị ký hiệu hoặc hình ảnh không? | Có thể, bằng hàm `bee.oled.image("icon")` (phiên bản sắp phát hành) |

---

## 4. Kết luận

Trong bài này, bạn đã học cách:

-   Hiển thị chữ và nhiều dòng trên OLED
-   Xóa, cuộn và cập nhật nội dung liên tục
-   Ứng dụng hiển thị dữ liệu cảm biến thời gian thực

> 💡 Thử kết hợp OLED + IMU + LED để tạo “dashboard mini” ngay trên BeE Board nhé!
