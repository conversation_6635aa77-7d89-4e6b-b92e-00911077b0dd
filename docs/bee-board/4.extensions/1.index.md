# Giới thiệu

<p align="center">
  <img 
    src="../_static/extensions/extensions.png" 
    alt="Extensions Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

## Giới thiệu

Bên cạnh các module tích hợp sẵn (LED, Buzzer, Button A/B, OLED, IMU, Motor, Servo),
**BeE Board V2** còn hỗ trợ **nhiều module mở rộng (Extension Modules)** thông qua 6 cổng **PORT1 → PORT6**.

Các module này giúp mở rộng khả năng của BeE Board để học sinh có thể:

-   Đo cảm biến môi trường (nhiệt độ, độ ẩm, ánh sáng, khoảng cách, v.v.)
-   Điều khiển thiết bị bên ngoài (đèn, motor, relay)
-   Giao tiếp với IoT hoặc AI (wifi, camera, RFID, v.v.)

> Mỗi module được kết nối dễ dàng qua dây Grove (chuẩn 4 chân: GND, VCC, SIG, SIG2/I2C).

---

## Chuẩn bị

Để sử dụng các module mở rộng, bạn cần:

-   **BeE Board V2** (với firmware mới nhất)
-   **Dây Grove 4 chân** (chuẩn) để kết nối module vào PORT

<p align="center">
  <img 
    src="../_static/extensions/grove-cable.jpg" 
    alt="Grove Cable" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

-   **BeE IDE** hoặc **BeE Python** để lập trình
-   Module có cổng kết nối chuẩn Grove

---

## Cách cài đặt

1. Cắm module vào PORT thích hợp
2. Click vào nút extension trên BeE IDE
3. Chọn module muốn sử dụng, click Install
4. Kéo thả và sử dụng các khối lệnh tương ứng vào Workspace

<p align="center">
  <img 
    src="../_static/extensions/install.jpg" 
    alt="Extension Button" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

## Danh sách module mở rộng

### BeE Starter Kit

| Module               | Loại tín hiệu | Mô tả                                            | Liên kết tài liệu                         |
| -------------------- | ------------- | ------------------------------------------------ | ----------------------------------------- |
| **Button**           | Digital       | Nút nhấn rời ngoài, hỗ trợ debounce & long press | [Button Module](../modules/button.md)     |
| **DHT11**            | 1-Wire        | Cảm biến đo nhiệt độ & độ ẩm                     | [DHT11 Module](../modules/dht11.md)       |
| **Neopixel RGB LED** | 1-Wire        | Dải đèn RGB nhiều màu, điều khiển lập trình      | [Neopixel Module](../modules/neopixel.md) |
| **BH1750**           | I2C           | Cảm biến ánh sáng                                | (Sắp ra mắt)                              |
| **VL53L0X**          | I2C           | Cảm biến đo khoảng cách chính xác                | (Sắp ra mắt)                              |
| **TCR5000**          | ADC           | Cảm biến dò line                                 | (Sắp ra mắt)                              |

### Grove Ecosystem

| Module              | Loại tín hiệu | Mô tả                                     | Liên kết tài liệu                               |
| ------------------- | ------------- | ----------------------------------------- | ----------------------------------------------- |
| **Ultrasonic**      | Digital       | Cảm biến đo khoảng cách (không chính xác) | [Ultrasonic Module](../modules/ultrasonic.md)   |
| **Line Detect**     | Digital       | Cảm biến dò line                          | [Line Detect Module](../modules/line-detect.md) |
| **Led Bar**         | Digital       | Dải led hiển thị cấp độ                   | (Sắp ra mắt)                                    |
| **Light Sensor**    | Analog        | Cảm biến ánh sáng                         | (Sắp ra mắt)                                    |
| **Rotation Sensor** | Analog        | Cảm biến góc quay                         | (Sắp ra mắt)                                    |
| **PIR Sensor**      | Digital       | Cảm biến chuyển động                      | (Sắp ra mắt)                                    |

### OhStem Ecosystem

| Module           | Loại tín hiệu | Mô tả                    | Liên kết tài liệu                                 |
| ---------------- | ------------- | ------------------------ | ------------------------------------------------- |
| **Led Segment**  | Digital       | Dải led hiển thị số      | [Led Segment Module](../modules/led-segment.md)   |
| **RFID RC522**   | I2C           | Nhận dạng thẻ RFID / NFC | [RFID RC522 Module](../modules/rc522.md)          |
| **Color Detect** | I2C           | Cảm biến màu             | [Color Detect Module](../modules/color-detect.md) |
| **Microphone**   | Analog        | Cảm biến âm thanh        | (Sắp ra mắt)                                      |

---

## Cổng mở rộng trên BeE Board V2

![BeE Board Ports Diagram](_static/bee-board-v2/extension/bee-board-ports.jpg)

Mỗi cổng **PORTx** gồm 4 chân:

| Chân    | Mô tả            | Ghi chú                 |
| ------- | ---------------- | ----------------------- |
| VCC     | 3.3V             | Nguồn cấp cho module    |
| GND     | Ground           | Chung mass hệ thống     |
| SIG     | Digital/Analog   | Dữ liệu hoặc điều khiển |
| SCL/SDA | (với module I2C) | Giao tiếp I2C           |

> Một số module (OLED, MPU6050) dùng giao tiếp **I2C** (chung SDA/SCL).
> Các module Digital như Button, DHT11, Buzzer, Neopixel dùng **từng cổng PORT riêng biệt**.

---

## Cách cài đặt và sử dụng

### Bước 1. Cắm module vào PORT thích hợp

-   Dùng dây Grove 4 chân (chuẩn) để kết nối module vào **PORT1 – PORT6**.
-   Đảm bảo hướng dây đúng: GND ↔ GND, VCC ↔ VCC, SIG ↔ SIG.

---

### Bước 2. Import thư viện trong chương trình Python

```python
from BeeBrain import bee
from DHT11 import DHT11
from Button import Button

sensor = DHT11(bee.PORT1)
button = Button(bee.PORT2)
```

---

### Bước 3. Lập trình với BeE IDE

Truy cập:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide)

-   Chọn nhóm khối tương ứng trong **Sensor**, **Input**, hoặc **Output**
-   Kéo thả khối: “Đọc nhiệt độ từ DHT11 ở PORT1”
-   Kết hợp hiển thị kết quả lên OLED hoặc LED RGB

![Blockly Extension Example](_static/bee-ide/blocks/extension-blocks.jpg)

---

### Bước 4. Lập trình với BeE Python

Truy cập:
👉 [https://beestemsolutions.com.vn/studio/python](https://beestemsolutions.com.vn/studio/python)

Ví dụ đọc dữ liệu từ cảm biến và hiển thị OLED:

```python
from BeeBrain import bee
from DHT11 import DHT11
import time

sensor = DHT11(bee.PORT1)

while True:
    t, h = sensor.read()
    bee.oled.clear()
    bee.oled.write(f"Nhiệt độ: {t}°C", 0, 0)
    bee.oled.write(f"Độ ẩm: {h}%", 0, 1)
    time.sleep(1)
```

---

## Hướng dẫn cài đặt thư viện module

Tất cả module mở rộng của BeE Board đều được tích hợp sẵn trong firmware **BeeBrain**.
Bạn **không cần tải thủ công**, chỉ cần đảm bảo firmware mới nhất.

> Cập nhật firmware mới tại:
> [Hướng dẫn nạp firmware](../flashing-guide.md)

---

## Mẹo sử dụng hiệu quả

| Tình huống                     | Giải pháp                                     |
| ------------------------------ | --------------------------------------------- |
| Dữ liệu cảm biến không ổn định | Đọc chậm hơn (1 giây/lần), tránh nhiễu        |
| Kết nối sai chiều              | Kiểm tra dây Grove đúng chiều GND/VCC         |
| Nhiều module I2C               | Dùng chung SDA/SCL, đảm bảo địa chỉ khác nhau |
| Cần thêm module mới            | Liên hệ BeE STEM để cập nhật firmware mới     |

---

```{toctree}
:maxdepth: 1
:hidden:
:caption: Module Mở Rộng
button
color-detect
dht11
led-segment
line-detect
rc522
ultrasonic
```

## Tài liệu liên quan

-   [Giới thiệu phần cứng BeE Board V2](../1.bee-board-v2/3.hardware-overview.md)
-   [Hướng dẫn nạp firmware](../2.bee-ide/5.flashing-image.md)
-   [Lập trình với BeE IDE](../../2.bee-ide/1.index.md)
