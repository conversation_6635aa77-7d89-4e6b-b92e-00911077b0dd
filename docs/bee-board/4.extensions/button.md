# Module N<PERSON>ấn

![Button Module Cover](_static/bee-board-v2/modules/button-cover.jpg)

---

## Giới thiệu

**Module Nút <PERSON>ấn** (External Button) là cảm biến **đầu vào kỹ thuật số (Digital Input)** giúp **phát hiện thao tác nhấn của người dùng**.
Nó có khả năng **chống nhi<PERSON> (debounce)** và **xử lý sự kiện nhấn – thả – nhấn giữ** thông qua callback function.

Trên BeE Board V2 đã có sẵn **2 nút tích hợp (Button A, Button B)**,
nhưng bạn có thể **kết nối thêm nhiều nút gắn ngoài** qua các cổng **PORT1–PORT6** để tạo giao diện điều khiển phong phú hơn.

---

## Ứng dụng thực tế

-   <PERSON><PERSON><PERSON><PERSON> khiể<PERSON>, độ<PERSON> cơ, hoặc bật/tắt hệ thống
-   Tr<PERSON> chơi phản xạ hoặc điều khiển robot
-   Hệ thống báo động, còi cảnh báo
-   Tạo menu điều hướng trên màn hình OLED
-   Giao tiếp người dùng trong dự án IoT

---

## Thông số kỹ thuật

| Thông số                       | Giá trị                             |
| ------------------------------ | ----------------------------------- |
| Điện áp hoạt động              | 3.3V                                |
| Tín hiệu đầu ra                | Digital HIGH/LOW                    |
| Chế độ Pull                    | PULL_UP (mặc định) hoặc PULL_DOWN   |
| Thời gian chống dội (debounce) | 200 ms (mặc định, có thể tùy chỉnh) |
| Thời gian nhấn giữ             | 1000 ms (có thể tùy chỉnh)          |
| Giao tiếp                      | GPIO qua cổng PORT của BeE Board    |
| Hỗ trợ                         | Callback và Interrupt-based         |

---

## Sơ đồ kết nối

| Dây | Mô tả                      |
| --- | -------------------------- |
| GND | Nối đất                    |
| VCC | 3.3V                       |
| SIG | Tín hiệu Digital vào PORTx |

> 💡 Cắm module vào **PORT1 – PORT6** của BeE Board V2.
> Mỗi PORT bao gồm 1 chân tín hiệu (GPIO) và 1 chân GND, 1 chân 3V3.

---

## Giao diện lập trình Python

### Khởi tạo

```python
from BeeBrain import bee
from Button import Button

# Khởi tạo module nút nhấn ở PORT1
ext_btn = Button(bee.PORT1)
```

---

### Kiểm tra trạng thái nhấn

```python
if ext_btn.is_pressed():
    print("Nút đang được nhấn")
```

### Kiểm tra trạng thái thả

```python
if ext_btn.is_released():
    print("Nút vừa được thả ra")
```

---

### Đăng ký sự kiện nhấn

```python
def on_press(pin):
    print("Nút được nhấn!")

ext_btn.on_press(on_press)
```

### Đăng ký sự kiện thả

```python
def on_released(pin):
    print("Nút được thả!")

ext_btn.on_released(on_released)
```

### Đăng ký sự kiện nhấn giữ (long press)

```python
def on_long_press(pin):
    print("Nhấn giữ 2 giây!")

ext_btn.on_long_press(on_long_press, 2000)
```

> 💡 Bạn có thể tạo nhiều nút khác nhau ở các PORT khác nhau:
> `btn1 = Button(bee.PORT1)` – `btn2 = Button(bee.PORT2)`

---

## Lập trình với BeE IDE

![Button Blocks Example](_static/bee-ide/blocks/button-blocks.jpg)

### Ví dụ khối lệnh:

```
khi nút ở PORT1 được nhấn:
    bật LED1
khi nút ở PORT1 được thả:
    tắt LED1
khi nút ở PORT1 được nhấn giữ 2 giây:
    phát âm thanh "beep"
```

> Kéo các khối trong nhóm **Input → Button → PORT** để sử dụng nút ngoài.

---

## Ví dụ Python

### Ví dụ cơ bản – Bật tắt LED khi nhấn nút

```python
from BeeBrain import bee
from Button import Button
import time

led = bee.led1
ext_btn = Button(bee.PORT1)

def toggle_led(pin):
    led.toggle()
    print("LED đổi trạng thái!")

ext_btn.on_press(toggle_led)

while True:
    time.sleep(0.1)
```

---

### Ví dụ nâng cao – Tạo menu điều hướng bằng 2 nút

```python
from BeeBrain import bee
from Button import Button
import time

oled = bee.oled
btn_up = Button(bee.PORT1)
btn_down = Button(bee.PORT2)

menu = ["LED Control", "Motor Control", "Sensor Read", "Settings"]
current = 0

def show_menu():
    oled.clear()
    for i, item in enumerate(menu):
        prefix = "> " if i == current else "  "
        oled.write(f"{prefix}{item}", 0, i)

def move_up(pin):
    global current
    current = (current - 1) % len(menu)
    show_menu()

def move_down(pin):
    global current
    current = (current + 1) % len(menu)
    show_menu()

btn_up.on_press(move_up)
btn_down.on_press(move_down)

show_menu()

while True:
    time.sleep(0.1)
```

---

## Giải thích mã

| Thành phần                   | Mô tả                                      |
| ---------------------------- | ------------------------------------------ |
| `Button(PORTx)`              | Tạo đối tượng nút gắn ngoài tại cổng PORTx |
| `on_press(callback)`         | Gọi hàm khi người dùng nhấn nút            |
| `on_long_press(callback, t)` | Gọi hàm khi nhấn giữ trong t mili-giây     |
| `is_pressed()`               | Trả về `True` nếu nút đang được nhấn       |
| `debounce`                   | Tránh nhiễu tín hiệu nhấn nhiều lần        |

---

## Bài tập mở rộng

1. **Đếm số lần nhấn**
   Hiển thị số lần nhấn nút trên OLED.
2. **Game phản xạ nhanh**
   LED sáng ngẫu nhiên → nhấn nút càng nhanh càng tốt!
3. **Khóa bảo mật đơn giản**
   Mở LED chỉ khi nhập đúng chuỗi nhấn 1–0–1 bằng 3 nút.

---

## Lỗi thường gặp

```{admonition} Nút không phản hồi
:class: warning
**Nguyên nhân:** Pull-up/pull-down không đúng hoặc debounce quá thấp
**Giải pháp:**
- Đặt `PULL_UP=True` khi khởi tạo
- Tăng debounce lên 200–300ms
- Kiểm tra dây nối và chân PORT
```

```{admonition} Callback bị gọi nhiều lần
:class: warning
**Nguyên nhân:** Nhiễu phần cứng hoặc debounce ngắn
**Giải pháp:**
- Tăng thời gian debounce
- Dùng tụ lọc 10nF–100nF giữa tín hiệu và GND
- Đảm bảo dây tín hiệu ngắn và ổn định
```

```{admonition} Long press không hoạt động
:class: warning
**Nguyên nhân:** Thời gian nhấn giữ quá ngắn
**Giải pháp:**
- Dùng thời gian ≥ 1000ms
- Kiểm tra callback có đúng cú pháp (có đối số `pin`)
```

---

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beestemsolutions.com.vn/docs/bee-board)
-   [MicroPython machine.Pin](https://docs.micropython.org/en/latest/library/machine.Pin.html)
-   [ESP32 GPIO Reference](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/peripherals/gpio.html)
-   [BeE IDE Online](https://beestemsolutions.com.vn/studio/bee-ide)
