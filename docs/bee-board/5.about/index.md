# BeE STEM Solutions

![BeE STEM Solutions Cover](_static/about/bee-stem-cover.jpg)

---

## Tầm nhìn

**BeE STEM Solutions** là nền tảng giáo dục **STEM – Robotics – AI** dành cho trẻ em Việt Nam,
gi<PERSON><PERSON> học sinh **họ<PERSON> lập tr<PERSON>nh, s<PERSON><PERSON> tạo robot, và khám phá công nghệ tương lai** thông qua thực hành.

Chúng tôi mong muốn **mang lại môi trường học tập vui – dễ – thực tế**,
nơi trẻ em có thể trở thành **người sáng tạo công nghệ**, chứ không chỉ là người sử dụng.

---

## H<PERSON> sinh thái BeE

BeE STEM Solutions xây dựng **hệ sinh thái học lập trình toàn diện** gồm:

| Th<PERSON><PERSON> phần       | <PERSON><PERSON> tả                                                                                             |
| ---------------- | ------------------------------------------------------------------------------------------------- |
| **BeE Board V2** | Bo mạch thông minh dựa trên ESP32-S3, tích hợp LED, Buzzer, Button, OLED, IMU, Motor, Servo       |
| **BeE IDE**      | Nền tảng lập trình kéo thả (Blockly) trực tuyến, giúp học sinh dễ dàng học lập trình IoT và robot |
| **BeE Python**   | Môi trường lập trình Python trực tuyến cho học sinh nâng cao                                      |
| **BeE Modules**  | Các module mở rộng (cảm biến, động cơ, LED, loa...) tương thích LEGO Technic & Grove              |
| **BeE Academy**  | Chương trình giảng dạy STEM – Robotics cho trường học, trung tâm, và gia đình                     |
| **BeE Kits**     | Bộ học cụ robot, AI và IoT trọn gói dành cho lớp học và tự học tại nhà                            |

---

## Triết lý giáo dục

> “Trẻ em học tốt nhất khi được **chạm vào, nhìn thấy, và tạo ra** điều gì đó.”

BeE STEM hướng tới:

-   **Trải nghiệm thực hành**: mỗi buổi học là một dự án thật
-   **Tư duy sáng tạo**: không chỉ lập trình, mà còn thiết kế – chế tạo
-   **Khơi gợi đam mê công nghệ**: giúp học sinh tự tin trước kỷ nguyên AI và IoT

---

## Các nền tảng trực tuyến của BeE

| Nền tảng       | Mục đích                            | Liên kết                                                                                         |
| -------------- | ----------------------------------- | ------------------------------------------------------------------------------------------------ |
| **BeE IDE**    | Lập trình kéo thả Blockly           | [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide) |
| **BeE Python** | Lập trình ngôn ngữ Python           | [https://beestemsolutions.com.vn/studio/python](https://beestemsolutions.com.vn/studio/python)   |
| **Trang chủ**  | Giới thiệu sản phẩm và chương trình | [https://beestemsolutions.com.vn](https://beestemsolutions.com.vn)                               |

---

## Đối tượng phục vụ

| Nhóm                           | Mục tiêu                                           |
| ------------------------------ | -------------------------------------------------- |
| **Học sinh Tiểu học – THCS**   | Làm quen lập trình và robot qua các ví dụ vui nhộn |
| **Giáo viên & trung tâm STEM** | Dạy học lập trình và robot bằng giáo trình BeE     |
| **Phụ huynh & Maker**          | Hỗ trợ con tự học hoặc sáng tạo tại nhà            |

---

## Các sản phẩm và khóa học tiêu biểu

| Sản phẩm / Khóa học  | Nội dung chính                                     | Thời lượng |
| -------------------- | -------------------------------------------------- | ---------- |
| **BeE Starter Kit**  | Làm quen cảm biến, LED, servo, robot cơ bản        | 16 buổi    |
| **BeE Robotics Kit** | Lập trình robot di chuyển, né vật cản, tự quay đầu | 16 buổi    |
| **BeE IoT Kit**      | Lập trình IoT, đọc dữ liệu cảm biến, dashboard     | 16 buổi    |
| **AI for Kids**      | Học AI cơ bản, nhận diện hình ảnh, giọng nói       | 8 buổi     |

---

## Giá trị cốt lõi

| Giá trị                                 | Mô tả                                                  |
| --------------------------------------- | ------------------------------------------------------ |
| **Học qua làm (Learning by Making)**    | Thực hành dự án thật, học sinh tự tay sáng tạo         |
| **Tư duy mở (Creative Thinking)**       | Khuyến khích thử nghiệm và khám phá                    |
| **Công nghệ gần gũi (Accessible Tech)** | Mọi học sinh đều có thể học lập trình và robot         |
| **Cộng đồng học tập (Community)**       | Kết nối giáo viên, phụ huynh, học sinh cùng phát triển |

---

## Thông tin liên hệ

| Kênh         | Liên kết                                                                                     |
| ------------ | -------------------------------------------------------------------------------------------- |
| **Website**  | [https://beestemsolutions.com.vn](https://beestemsolutions.com.vn)                           |
| **Facebook** | [https://facebook.com/beestemsolutions.com.vn](https://facebook.com/beestemsolutions.com.vn) |
| **TikTok**   | [https://tiktok.com/@beestemsolutions.com.vn](https://tiktok.com/@beestemsolutions.com.vn)   |
| **Email**    | [<EMAIL>](mailto:<EMAIL>)                    |
| **Hotline**  | [0987845231](tel:0987845231)                                                                 |

---

## Lời cảm ơn

Cảm ơn các **thầy cô, phụ huynh, và học sinh** đã đồng hành cùng **BeE STEM Solutions**
trên hành trình **“biến học lập trình thành niềm vui sáng tạo”**
