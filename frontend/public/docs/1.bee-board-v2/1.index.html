

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Gi<PERSON><PERSON> thiệu &mdash; Tài <PERSON> BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Bắt đầu" href="2.getting-started.html" />
    <link rel="prev" title="BeE STEM Solutions Documentation" href="../index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Giới thiệu</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#bee-board-v2-la-gi">BeE Board V2 là gì?</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thong-so-ky-thuat">Thông số kỹ thuật</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thanh-phan-chinh-tren-board">Thành phần chính trên board</a></li>
<li class="toctree-l2"><a class="reference internal" href="#doi-tuong-bee-trong-python">Đối tượng <code class="docutils literal notranslate"><span class="pre">bee</span></code> trong Python</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ket-noi-va-lap-trinh">Kết nối và lập trình</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#ket-noi-qua-usb-c">Kết nối qua USB-C</a></li>
<li class="toctree-l3"><a class="reference internal" href="#cap-nhat-code-qua-ota">Cập nhật code qua OTA</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#mot-so-vi-du-don-gian">Một số ví dụ đơn giản</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-1-bat-led-1-mau-xanh-la">Ví dụ 1: Bật LED 1 màu xanh lá</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-2-hien-thi-dong-chu">Ví dụ 2: Hiển thị dòng chữ</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-3-robot-di-chuyen">Ví dụ 3: Robot di chuyển</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#cac-nhom-khoi-blockly-ho-tro">Các nhóm khối Blockly hỗ trợ</a></li>
<li class="toctree-l2"><a class="reference internal" href="#xu-ly-su-co-thuong-gap">Xử lý sự cố thường gặp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-nguyen-lien-quan">Tài nguyên liên quan</a><ul>
<li class="toctree-l3"><a class="reference internal" href="2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l3"><a class="reference internal" href="3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l3"><a class="reference internal" href="4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l3"><a class="reference internal" href="5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Giới thiệu</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/1.bee-board-v2/1.index.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="gioi-thieu">
<h1>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h1>
<p align="center">
  <img 
    src="../_static/bee-board-v2/board.png" 
    alt="BeE Board V2 tổng quan" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<section id="bee-board-v2-la-gi">
<h2>BeE Board V2 là gì?<a class="headerlink" href="#bee-board-v2-la-gi" title="Link to this heading"></a></h2>
<p><strong>BeE Board V2</strong> là bo mạch học lập trình dành cho học sinh tiểu học và THCS, được thiết kế bởi <strong>BeE STEM Solutions</strong>.
Bo mạch giúp học sinh học lập trình <strong>kéo thả (Blockly)</strong> hoặc <strong>Python (MicroPython)</strong> thông qua <strong>BeE IDE</strong> và <strong>BeE Python</strong>.</p>
<p>Bo tương thích với LEGO Technic, hỗ trợ học sinh tạo robot, mô hình IoT và các dự án sáng tạo thực tế.</p>
</section>
<hr class="docutils" />
<section id="thong-so-ky-thuat">
<h2>Thông số kỹ thuật<a class="headerlink" href="#thong-so-ky-thuat" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Vi điều khiển</p></td>
<td><p>ESP32-S3</p></td>
</tr>
<tr class="row-odd"><td><p>LED tích hợp</p></td>
<td><p>2 LED RGB điều khiển độc lập</p></td>
</tr>
<tr class="row-even"><td><p>Nút nhấn</p></td>
<td><p>2 nút nhấn A và B</p></td>
</tr>
<tr class="row-odd"><td><p>Màn hình OLED</p></td>
<td><p>0.96 inch (128x64 pixel)</p></td>
</tr>
<tr class="row-even"><td><p>Cảm biến</p></td>
<td><p>MPU6050 (con quay + gia tốc kế)</p></td>
</tr>
<tr class="row-odd"><td><p>Loa Buzzer</p></td>
<td><p>Âm thanh đơn hoặc giai điệu</p></td>
</tr>
<tr class="row-even"><td><p>Động cơ DC</p></td>
<td><p>2 cổng điều khiển</p></td>
</tr>
<tr class="row-odd"><td><p>Servo RC</p></td>
<td><p>4 cổng (S1–S4)</p></td>
</tr>
<tr class="row-even"><td><p>Cổng Grove mở rộng</p></td>
<td><p>6 cổng (PORT1–PORT6)</p></td>
</tr>
<tr class="row-odd"><td><p>Giao tiếp</p></td>
<td><p>USB-C, OTA, Bluetooth, Wifi, MQTT</p></td>
</tr>
<tr class="row-even"><td><p>Nguồn cấp</p></td>
<td><p>Pin Li-ion 7.4V ~ 8.4V hoặc USB-C 5V</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="thanh-phan-chinh-tren-board">
<h2>Thành phần chính trên board<a class="headerlink" href="#thanh-phan-chinh-tren-board" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Vị trí</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Ảnh minh họa</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>1</p></td>
<td><p>LED RGB (led1, led2)</p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/led.jpg"><img alt="LED" src="../_images/led.jpg" style="width: 150px;" /></a></p></p></td>
</tr>
<tr class="row-odd"><td><p>2</p></td>
<td><p>Nút nhấn (buttonA, buttonB)</p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/button.jpg"><img alt="Button" src="../_images/button.jpg" style="width: 150px;" /></a></p></p></td>
</tr>
<tr class="row-even"><td><p>3</p></td>
<td><p>Màn hình OLED 0.96&quot;</p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/oled.jpg"><img alt="OLED" src="../_images/oled.jpg" style="width: 150px;" /></a></p></p></td>
</tr>
<tr class="row-odd"><td><p>4</p></td>
<td><p>Loa buzzer</p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/buzzer.jpg"><img alt="Buzzer" src="../_images/buzzer.jpg" style="width: 150px;" /></a></p></p></td>
</tr>
<tr class="row-even"><td><p>5</p></td>
<td><p>Cảm biến MPU6050</p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/imu.jpg"><img alt="IMU" src="../_images/imu.jpg" style="width: 150px;" /></a></p></p></td>
</tr>
<tr class="row-odd"><td><p>6</p></td>
<td><p>Cổng Grove mở rộng</p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/grove.jpg"><img alt="Grove" src="../_images/grove.jpg" style="width: 150px;" /></a></p></p></td>
</tr>
<tr class="row-even"><td><p>7</p></td>
<td><p>Cổng motor (M1, M2)</p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/motor.jpg"><img alt="Motor" src="../_images/motor.jpg" style="width: 150px;" /></a></p></p></td>
</tr>
<tr class="row-odd"><td><p>8</p></td>
<td><p>Cổng Servo (S1–S4)</p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/servo.jpg"><img alt="Servo" src="../_images/servo.jpg" style="width: 150px;" /></a></p></p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="doi-tuong-bee-trong-python">
<h2>Đối tượng <code class="docutils literal notranslate"><span class="pre">bee</span></code> trong Python<a class="headerlink" href="#doi-tuong-bee-trong-python" title="Link to this heading"></a></h2>
<p>Khi lập trình bằng Python trong BeE Python, học sinh sử dụng thư viện:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
</pre></div>
</div>
<p>Một số lệnh cơ bản:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>     <span class="c1"># Bật LED1 màu đỏ</span>
<span class="n">bee</span><span class="o">.</span><span class="n">buttonA</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">()</span>        <span class="c1"># Kiểm tra nút A</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Hello&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">motor1</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">60</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">servo3</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">90</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
<span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">TONES</span><span class="p">[</span><span class="s1">&#39;C5&#39;</span><span class="p">])</span>
</pre></div>
</div>
</section>
<hr class="docutils" />
<section id="ket-noi-va-lap-trinh">
<h2>Kết nối và lập trình<a class="headerlink" href="#ket-noi-va-lap-trinh" title="Link to this heading"></a></h2>
<section id="ket-noi-qua-usb-c">
<h3>Kết nối qua USB-C<a class="headerlink" href="#ket-noi-qua-usb-c" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Gắn BeE Board V2 với máy tính bằng cáp USB-C.</p></li>
<li><p>Mở <strong>BeE IDE</strong> → chọn cổng serial (ví dụ <code class="docutils literal notranslate"><span class="pre">/dev/cu.usbmodem</span></code> hoặc <code class="docutils literal notranslate"><span class="pre">COM3</span></code>).</p></li>
<li><p>Chọn ngôn ngữ lập trình: <strong>Blockly</strong> hoặc <strong>Python</strong>.</p></li>
<li><p>Nạp chương trình và chạy thử.</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/bee-board-v2/connect-usb.jpg" 
    alt="Kết nối USB-C" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="cap-nhat-code-qua-ota">
<h3>Cập nhật code qua OTA<a class="headerlink" href="#cap-nhat-code-qua-ota" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Tạo chương trình <code class="docutils literal notranslate"><span class="pre">Setup</span> <span class="pre">OTA</span></code> trong Blockly hoặc Python.</p></li>
<li><p>Nhập tên Wi-Fi và mật khẩu.</p></li>
<li><p>BeE Board sẽ hiển thị IP trên màn hình OLED.</p></li>
<li><p>Từ <strong>BeE IDE</strong>, chọn “Upload OTA” và nhập IP hiển thị.</p></li>
</ol>
</section>
</section>
<hr class="docutils" />
<section id="mot-so-vi-du-don-gian">
<h2>Một số ví dụ đơn giản<a class="headerlink" href="#mot-so-vi-du-don-gian" title="Link to this heading"></a></h2>
<section id="vi-du-1-bat-led-1-mau-xanh-la">
<h3>Ví dụ 1: Bật LED 1 màu xanh lá<a class="headerlink" href="#vi-du-1-bat-led-1-mau-xanh-la" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>

<span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="vi-du-2-hien-thi-dong-chu">
<h3>Ví dụ 2: Hiển thị dòng chữ<a class="headerlink" href="#vi-du-2-hien-thi-dong-chu" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>

<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Xin chao!&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="vi-du-3-robot-di-chuyen">
<h3>Ví dụ 3: Robot di chuyển<a class="headerlink" href="#vi-du-3-robot-di-chuyen" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>

<span class="n">bee</span><span class="o">.</span><span class="n">move_forward</span><span class="p">(</span><span class="mi">50</span><span class="p">,</span> <span class="mi">2</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">turn_left</span><span class="p">(</span><span class="mi">60</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<hr class="docutils" />
<section id="cac-nhom-khoi-blockly-ho-tro">
<h2>Các nhóm khối Blockly hỗ trợ<a class="headerlink" href="#cac-nhom-khoi-blockly-ho-tro" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nhóm</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>LED</p></td>
<td><p>Điều khiển LED1, LED2 tích hợp</p></td>
</tr>
<tr class="row-odd"><td><p>Display</p></td>
<td><p>Hiển thị chữ, hình, icon trên màn hình tích hợp</p></td>
</tr>
<tr class="row-even"><td><p>Button</p></td>
<td><p>Tương tác nút nhấn A và B tích hợp</p></td>
</tr>
<tr class="row-odd"><td><p>Buzzer</p></td>
<td><p>Phát âm thanh, nhạc tích hợp</p></td>
</tr>
<tr class="row-even"><td><p>Write Signal</p></td>
<td><p>Ghi tín hiệu số, analog từ PORT1-6</p></td>
</tr>
<tr class="row-odd"><td><p>Read Signal</p></td>
<td><p>Đọc tín hiệu số, analog từ PORT1-6</p></td>
</tr>
<tr class="row-even"><td><p>Gyroscope</p></td>
<td><p>Cảm biến góc nghiêng, rung lắc tích hợp</p></td>
</tr>
<tr class="row-odd"><td><p>Motor &amp; Servo</p></td>
<td><p>Điều khiển motor và servo tích hợp</p></td>
</tr>
<tr class="row-even"><td><p>Movement</p></td>
<td><p>Điều khiển robot di chuyển</p></td>
</tr>
<tr class="row-odd"><td><p>Advanced</p></td>
<td><p>Wifi, Bluetooth, MQTT, OTA, Reset</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="xu-ly-su-co-thuong-gap">
<h2>Xử lý sự cố thường gặp<a class="headerlink" href="#xu-ly-su-co-thuong-gap" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Vấn đề</p></th>
<th class="head"><p>Nguyên nhân</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Không nạp được code</p></td>
<td><p>Sai cổng serial hoặc chưa nhấn reset</p></td>
<td><p>Kiểm tra lại kết nối USB, chọn đúng cổng trong IDE</p></td>
</tr>
<tr class="row-odd"><td><p>Không thấy IP OTA</p></td>
<td><p>Wi-Fi yếu hoặc sai mật khẩu</p></td>
<td><p>Kiểm tra mạng Wi-Fi, khởi động lại board</p></td>
</tr>
<tr class="row-even"><td><p>OLED không hiển thị</p></td>
<td><p>Chưa khởi tạo hoặc code lỗi</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">bee.oled.clear()</span></code> trước khi ghi chữ</p></td>
</tr>
<tr class="row-odd"><td><p>LED không sáng</p></td>
<td><p>Dây LED hỏng hoặc code sai</p></td>
<td><p>Kiểm tra lệnh <code class="docutils literal notranslate"><span class="pre">bee.led1.set_rgb()</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="tai-nguyen-lien-quan">
<h2>Tài nguyên liên quan<a class="headerlink" href="#tai-nguyen-lien-quan" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="#../2.bee-ide/index.md"><span class="xref myst">Hướng dẫn BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="../3.examples/2.led-example.html"><span class="std std-doc">Ví dụ lập trình với BeE Board V2</span></a></p></li>
<li><p><a class="reference internal" href="../4.extensions/1.index.html"><span class="std std-doc">Cài đặt thư viện mở rộng trên BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="#../5.about/about.md"><span class="xref myst">Giới thiệu BeE STEM Solutions</span></a></p></li>
</ul>
<hr class="docutils" />
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Bắt đầu</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
</div>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../index.html" class="btn btn-neutral float-left" title="BeE STEM Solutions Documentation" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="2.getting-started.html" class="btn btn-neutral float-right" title="Bắt đầu" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>