

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bắt đầu &mdash; T<PERSON>i <PERSON> BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Giới thiệu phần cứng" href="3.hardware-overview.html" />
    <link rel="prev" title="Giới thiệu" href="1.index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Bắt đầu</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#chuan-bi-truoc-khi-bat-dau">1. Chuẩn bị trước khi bắt đầu</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#ban-can-co">Bạn cần có:</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#truy-cap-nen-tang-lap-trinh">2. Truy cập nền tảng lập trình</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#bee-ide-lap-trinh-keo-tha-blockly">BeE IDE – Lập trình kéo thả (Blockly)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#bee-python-lap-trinh-bang-python">BeE Python – Lập trình bằng Python</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#ket-noi-bee-board-v2">3. Kết nối BeE Board V2</a></li>
<li class="toctree-l2"><a class="reference internal" href="#viet-chuong-trinh-dau-tien">4. Viết chương trình đầu tiên</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#trong-bee-ide">Trong BeE IDE</a></li>
<li class="toctree-l3"><a class="reference internal" href="#trong-bee-python">Trong BeE Python</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#so-sanh-nhanh-giua-bee-ide-va-bee-python">5. So sánh nhanh giữa BeE IDE và BeE Python</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cap-nhat-chuong-trinh-qua-ota-wi-fi">6. Cập nhật chương trình qua OTA (Wi-Fi)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#meo-cho-nguoi-moi-bat-dau">7. Mẹo cho người mới bắt đầu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tiep-theo">Tiếp theo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">Bắt đầu</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/1.bee-board-v2/2.getting-started.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="bat-dau">
<h1>Bắt đầu<a class="headerlink" href="#bat-dau" title="Link to this heading"></a></h1>
<p align="center">
  <img 
    src="../_static/logo.png" 
    alt="BeE-Cover" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<p>Chào mừng bạn đến với <strong>BeE Board V2</strong> – bo mạch học lập trình và robot sáng tạo do <strong>BeE STEM Solutions</strong> phát triển độc quyền 🇻🇳
BeE Board giúp học sinh học lập trình trực tuyến qua hai nền tảng web:</p>
<ul class="simple">
<li><p><strong>BeE IDE</strong> → lập trình <strong>kéo thả Blockly</strong></p></li>
<li><p><strong>BeE Python</strong> → lập trình <strong>bằng code Python</strong></p></li>
</ul>
<p>Không cần cài đặt phần mềm – chỉ cần trình duyệt web!</p>
<hr class="docutils" />
<section id="chuan-bi-truoc-khi-bat-dau">
<h2>1. Chuẩn bị trước khi bắt đầu<a class="headerlink" href="#chuan-bi-truoc-khi-bat-dau" title="Link to this heading"></a></h2>
<section id="ban-can-co">
<h3>Bạn cần có:<a class="headerlink" href="#ban-can-co" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>1 bo mạch <strong>BeE Board V2</strong></p></li>
<li><p>Cáp <strong>USB-C</strong> để kết nối với máy tính</p></li>
<li><p><strong>Trình duyệt web hiện đại</strong> (Chrome, Edge)</p></li>
<li><p><strong>Driver CH340C</strong> nếu là lần đầu kết nối BeE Board qua USB</p></li>
</ul>
<blockquote>
<div><p>💡 BeE Board V2 dùng chip <strong>CH340C</strong> để giao tiếp USB.
Nếu máy tính chưa nhận cổng COM, hãy cài <strong>driver CH340C</strong> theo hướng dẫn:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board">https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board</a></p>
</div></blockquote>
</section>
</section>
<hr class="docutils" />
<section id="truy-cap-nen-tang-lap-trinh">
<h2>2. Truy cập nền tảng lập trình<a class="headerlink" href="#truy-cap-nen-tang-lap-trinh" title="Link to this heading"></a></h2>
<section id="bee-ide-lap-trinh-keo-tha-blockly">
<h3>BeE IDE – Lập trình kéo thả (Blockly)<a class="headerlink" href="#bee-ide-lap-trinh-keo-tha-blockly" title="Link to this heading"></a></h3>
<p>Truy cập trực tiếp bằng trình duyệt:
👉 <a class="reference external" href="https://ide.beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<p><strong>Đặc điểm:</strong></p>
<ul class="simple">
<li><p>Giao diện lập trình trực quan bằng khối kéo thả.</p></li>
<li><p>Dành cho học sinh tiểu học hoặc người mới bắt đầu.</p></li>
<li><p>Không hiển thị code Python — mọi thao tác đều bằng khối lệnh.</p></li>
<li><p>Hỗ trợ nạp code qua USB hoặc OTA.</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="bee-python-lap-trinh-bang-python">
<h3>BeE Python – Lập trình bằng Python<a class="headerlink" href="#bee-python-lap-trinh-bang-python" title="Link to this heading"></a></h3>
<p>Truy cập trực tiếp bằng trình duyệt:
👉 <a class="reference external" href="https://python.beestemsolutions.com.vn/studio/python">https://beestemsolutions.com.vn/studio/python</a></p>
<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-python.png" 
    alt="BeE Python" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<p><strong>Đặc điểm:</strong></p>
<ul class="simple">
<li><p>Viết code Python trên trình soạn thảo Monaco (giống VS Code).</p></li>
<li><p>Dành cho học sinh THCS hoặc đã quen tư duy lập trình.</p></li>
<li><p>Có gợi ý cú pháp, chạy trực tiếp trên board qua USB hoặc OTA.</p></li>
</ul>
</section>
</section>
<hr class="docutils" />
<section id="ket-noi-bee-board-v2">
<h2>3. Kết nối BeE Board V2<a class="headerlink" href="#ket-noi-bee-board-v2" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p>Kết nối BeE Board với máy tính bằng <strong>cáp USB-C</strong></p></li>
<li><p>Mở BeE IDE hoặc BeE Python trong trình duyệt</p></li>
<li><p>Chọn cổng <strong>Serial / USB</strong> (ví dụ: <code class="docutils literal notranslate"><span class="pre">COM3</span></code> hoặc <code class="docutils literal notranslate"><span class="pre">/dev/cu.usbmodem</span></code>)</p></li>
<li><p>Nhấn <strong>Kết nối</strong> để bắt đầu lập trình</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/bee-board-v2/connect-usb.jpg" 
    alt="Kết nối USB-C" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="viet-chuong-trinh-dau-tien">
<h2>4. Viết chương trình đầu tiên<a class="headerlink" href="#viet-chuong-trinh-dau-tien" title="Link to this heading"></a></h2>
<section id="trong-bee-ide">
<h3>Trong BeE IDE<a class="headerlink" href="#trong-bee-ide" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Kéo khối <strong>“bật LED1</strong> vào vùng làm việc bên dưới khối <strong>Khi chương trình bắt đầu</strong></p></li>
<li><p>Nhấn nút <strong>▶️ Upload</strong> để tải chương trình</p></li>
<li><p>Quan sát LED1 sáng lên trên BeE Board!</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/bee-board-v2/blockly-example-led.png" 
    alt="Ví dụ BeE IDE" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="trong-bee-python">
<h3>Trong BeE Python<a class="headerlink" href="#trong-bee-python" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>

<span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Hello BeE!&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
<p>Nhấn <strong>Run ▶️</strong> để chạy chương trình trực tiếp trên BeE Board.</p>
<p align="center">
  <img 
    src="../_static/bee-board-v2/python-run.png" 
    alt="Ví dụ BeE Python" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
</section>
</section>
<hr class="docutils" />
<section id="so-sanh-nhanh-giua-bee-ide-va-bee-python">
<h2>5. So sánh nhanh giữa BeE IDE và BeE Python<a class="headerlink" href="#so-sanh-nhanh-giua-bee-ide-va-bee-python" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nền tảng</p></th>
<th class="head"><p>Kiểu lập trình</p></th>
<th class="head"><p>Đối tượng</p></th>
<th class="head"><p>Đặc điểm</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>BeE IDE</strong></p></td>
<td><p>Kéo thả Blockly</p></td>
<td><p>Học sinh tiểu học, người mới bắt đầu</p></td>
<td><p>Trực quan, sinh động, không hiển thị code</p></td>
</tr>
<tr class="row-odd"><td><p><strong>BeE Python</strong></p></td>
<td><p>Python thực tế</p></td>
<td><p>Học sinh THCS, nâng cao</p></td>
<td><p>Viết code thật, có gợi ý cú pháp và kết nối trực tiếp</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>⚠️ Hai nền tảng hoạt động <strong>độc lập</strong>, không thể mở file chéo.
Người học có thể bắt đầu bằng BeE IDE, sau đó chuyển dần sang BeE Python khi đã quen logic lập trình.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="cap-nhat-chuong-trinh-qua-ota-wi-fi">
<h2>6. Cập nhật chương trình qua OTA (Wi-Fi)<a class="headerlink" href="#cap-nhat-chuong-trinh-qua-ota-wi-fi" title="Link to this heading"></a></h2>
<p>BeE Board V2 hỗ trợ <strong>Over-The-Air (OTA)</strong> – nạp code không cần dây USB bằng BeE IDE:</p>
<ol class="arabic simple">
<li><p>Trong Blockly, kéo khối <strong>“Setup OTA”</strong>, nhập tên Wi-Fi và mật khẩu.</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/bee-board-v2/setup-ota.png" 
    alt="Thiết lập OTA" 
    width="250" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<ol class="arabic simple" start="2">
<li><p>Khi board kết nối thành công, OLED hiển thị <strong>địa chỉ IP</strong>.</p></li>
<li><p>Trong BeE IDE, chọn <strong>Upload OTA</strong> → nhập IP đó.</p></li>
<li><p>Code sẽ được tải và chạy không dây.</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/bee-board-v2/upload-ota.png" 
    alt="Nạp code OTA" 
    width="250" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="meo-cho-nguoi-moi-bat-dau">
<h2>7. Mẹo cho người mới bắt đầu<a class="headerlink" href="#meo-cho-nguoi-moi-bat-dau" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p>Nhấn <strong>Reset</strong> trên board để khởi tạo lại</p></li>
<li><p>Kiểm tra LED bằng lệnh <code class="docutils literal notranslate"><span class="pre">bee.led1.set_rgb(255,</span> <span class="pre">255,</span> <span class="pre">255)</span></code></p></li>
<li><p>Khi OLED không hiển thị, thử <code class="docutils literal notranslate"><span class="pre">bee.oled.show()</span></code></p></li>
<li><p>Luôn chắc chắn chọn đúng cổng Serial trước khi nạp code</p></li>
<li><p>Hãy sáng tạo: lập trình đèn nhấp nháy, robot di chuyển, hay nhạc vui 🎵</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="tiep-theo">
<h2>Tiếp theo<a class="headerlink" href="#tiep-theo" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="#../2.bee-ide/index.md"><span class="xref myst">Hướng dẫn BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="../3.examples/2.led-example.html"><span class="std std-doc">Ví dụ lập trình với BeE Board V2</span></a></p></li>
<li><p><a class="reference internal" href="../4.extensions/1.index.html"><span class="std std-doc">Cài đặt thư viện mở rộng trên BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="#../5.about/about.md"><span class="xref myst">Giới thiệu BeE STEM Solutions</span></a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="1.index.html" class="btn btn-neutral float-left" title="Giới thiệu" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="3.hardware-overview.html" class="btn btn-neutral float-right" title="Giới thiệu phần cứng" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>