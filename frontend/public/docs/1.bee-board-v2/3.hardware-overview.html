

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON><PERSON><PERSON> thiệu phần cứng &mdash; Tài <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Hướng dẫn lập trình" href="4.programming-guide.html" />
    <link rel="prev" title="Bắt đầu" href="2.getting-started.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Giới thiệu phần cứng</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#tong-quan">1. Tổng quan</a></li>
<li class="toctree-l2"><a class="reference internal" href="#so-do-tong-quan-va-thanh-phan">2. Sơ đồ tổng quan và thành phần</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thong-so-ky-thuat-chi-tiet">3. Thông số kỹ thuật chi tiết</a></li>
<li class="toctree-l2"><a class="reference internal" href="#mapping-cong-chan-ket-noi">4. Mapping cổng &amp; chân kết nối</a></li>
<li class="toctree-l2"><a class="reference internal" href="#nguon-cap-va-cong-tac">5. Nguồn cấp và công tắc</a></li>
<li class="toctree-l2"><a class="reference internal" href="#vi-xu-ly-trung-tam-esp32-s3">6. Vi xử lý trung tâm (ESP32-S3)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ket-noi-va-giao-tiep">7. Kết nối và giao tiếp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tuong-thich-lego-technic">8. Tương thích LEGO Technic</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cac-doi-tuong-trong-lap-trinh-python">9. Các đối tượng trong lập trình Python</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-lieu-tham-khao">10. Tài liệu tham khảo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">Giới thiệu phần cứng</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/1.bee-board-v2/3.hardware-overview.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="gioi-thieu-phan-cung">
<h1>Giới thiệu phần cứng<a class="headerlink" href="#gioi-thieu-phan-cung" title="Link to this heading"></a></h1>
<p align="center">
  <img 
    src="../_static/bee-board-v2/board.png" 
    alt="BeE Board V2 tổng quan" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<hr class="docutils" />
<section id="tong-quan">
<h2>1. Tổng quan<a class="headerlink" href="#tong-quan" title="Link to this heading"></a></h2>
<p><strong>BeE Board V2</strong> là bo mạch học lập trình và robot thông minh được phát triển bởi <strong>BeE STEM Solutions</strong> 🇻🇳.
Bo mạch sử dụng <strong>ESP32-S3</strong> làm vi điều khiển trung tâm, hỗ trợ cả <strong>kết nối USB, Bluetooth và Wi-Fi</strong>,
tích hợp nhiều cảm biến và thiết bị ngoại vi sẵn sàng cho việc dạy và học STEM.</p>
</section>
<hr class="docutils" />
<section id="so-do-tong-quan-va-thanh-phan">
<h2>2. Sơ đồ tổng quan và thành phần<a class="headerlink" href="#so-do-tong-quan-va-thanh-phan" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-board-v2/board-layout.jpg" 
    alt="BeE Board V2 Layout" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Ký hiệu</p></th>
<th class="head"><p>Thành phần</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>①</p></td>
<td><p>LED RGB 1 (LED1)</p></td>
<td><p>Điều khiển qua <code class="docutils literal notranslate"><span class="pre">bee.led1</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>②</p></td>
<td><p>LED RGB 2 (LED2)</p></td>
<td><p>Điều khiển qua <code class="docutils literal notranslate"><span class="pre">bee.led2</span></code></p></td>
</tr>
<tr class="row-even"><td><p>③</p></td>
<td><p>Nút nhấn A</p></td>
<td><p>Tương ứng <code class="docutils literal notranslate"><span class="pre">bee.buttonA</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>④</p></td>
<td><p>Nút nhấn B</p></td>
<td><p>Tương ứng <code class="docutils literal notranslate"><span class="pre">bee.buttonB</span></code></p></td>
</tr>
<tr class="row-even"><td><p>⑤</p></td>
<td><p>Màn hình OLED 0.96&quot;</p></td>
<td><p>Giao tiếp I2C, đối tượng <code class="docutils literal notranslate"><span class="pre">bee.oled</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>⑥</p></td>
<td><p>Cảm biến IMU MPU6050</p></td>
<td><p>Đo nghiêng, rung, <code class="docutils literal notranslate"><span class="pre">bee.imu</span></code></p></td>
</tr>
<tr class="row-even"><td><p>⑦</p></td>
<td><p>Loa Buzzer</p></td>
<td><p>Phát âm, nhạc, <code class="docutils literal notranslate"><span class="pre">bee.buzzer</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>⑧</p></td>
<td><p>Cổng Motor M1</p></td>
<td><p>Điều khiển DC motor, <code class="docutils literal notranslate"><span class="pre">bee.motor1</span></code></p></td>
</tr>
<tr class="row-even"><td><p>⑨</p></td>
<td><p>Cổng Motor M2</p></td>
<td><p>Điều khiển DC motor, <code class="docutils literal notranslate"><span class="pre">bee.motor2</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>⑩</p></td>
<td><p>Cổng Servo (S1–S4)</p></td>
<td><p>Điều khiển servo, <code class="docutils literal notranslate"><span class="pre">bee.servo1–4</span></code></p></td>
</tr>
<tr class="row-even"><td><p>⑪</p></td>
<td><p>Cổng Grove 1</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.PORT1</span></code> (GPIO, PWM, Analog, I2C, UART)</p></td>
</tr>
<tr class="row-odd"><td><p>⑫</p></td>
<td><p>Cổng Grove 2</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.PORT2</span></code> (GPIO, PWM, Analog, I2C, UART)</p></td>
</tr>
<tr class="row-even"><td><p>⑬</p></td>
<td><p>Cổng Grove 3</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.PORT3</span></code> (GPIO, PWM, Analog, I2C, UART)</p></td>
</tr>
<tr class="row-odd"><td><p>⑭</p></td>
<td><p>Cổng Grove 4</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.PORT4</span></code> (GPIO, PWM, Analog, I2C, UART)</p></td>
</tr>
<tr class="row-even"><td><p>⑮</p></td>
<td><p>Cổng Grove 5</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.PORT5</span></code> (GPIO, PWM, Analog, I2C, UART)</p></td>
</tr>
<tr class="row-odd"><td><p>⑯</p></td>
<td><p>Cổng Grove 6</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.PORT6</span></code> (GPIO, PWM, Analog, I2C, UART)</p></td>
</tr>
<tr class="row-even"><td><p>⑰</p></td>
<td><p>Cổng USB-C</p></td>
<td><p>Giao tiếp lập trình qua chip <strong>CH340C</strong></p></td>
</tr>
<tr class="row-odd"><td><p>⑱</p></td>
<td><p>Nút Boot</p></td>
<td><p>Vào chế độ flashing firmware</p></td>
</tr>
<tr class="row-even"><td><p>⑲</p></td>
<td><p>Nút Reset</p></td>
<td><p>Reset board</p></td>
</tr>
<tr class="row-odd"><td><p>⑳</p></td>
<td><p>LED nguồn (PWR)</p></td>
<td><p>Hiển thị trạng thái nguồn</p></td>
</tr>
<tr class="row-even"><td><p>㉑</p></td>
<td><p>Nút nguồn</p></td>
<td><p>Bật/tắt nguồn ngoài</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="thong-so-ky-thuat-chi-tiet">
<h2>3. Thông số kỹ thuật chi tiết<a class="headerlink" href="#thong-so-ky-thuat-chi-tiet" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Thông số</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Vi điều khiển</p></td>
<td><p>ESP32-S3-WROOM-1-N8R8</p></td>
</tr>
<tr class="row-odd"><td><p>CPU</p></td>
<td><p>Tensilica LX7 Dual-core 240MHz</p></td>
</tr>
<tr class="row-even"><td><p>RAM</p></td>
<td><p>512 KB (PSRAM 8 MB)</p></td>
</tr>
<tr class="row-odd"><td><p>Flash</p></td>
<td><p>8 MB</p></td>
</tr>
<tr class="row-even"><td><p>USB Interface</p></td>
<td><p><strong>CH340C</strong></p></td>
</tr>
<tr class="row-odd"><td><p>Nguồn cấp</p></td>
<td><p>5V qua USB-C hoặc pin Li-ion</p></td>
</tr>
<tr class="row-even"><td><p>Giao tiếp</p></td>
<td><p>UART, I2C, PWM, ADC, SPI</p></td>
</tr>
<tr class="row-odd"><td><p>Hỗ trợ kết nối</p></td>
<td><p>USB, Wi-Fi, Bluetooth</p></td>
</tr>
<tr class="row-even"><td><p>Tương thích cơ khí</p></td>
<td><p>LEGO Technic Frame 7×11</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="mapping-cong-chan-ket-noi">
<h2>4. Mapping cổng &amp; chân kết nối<a class="headerlink" href="#mapping-cong-chan-ket-noi" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Cổng</p></th>
<th class="head"><p>Chức năng chính</p></th>
<th class="head"><p>GPIO</p></th>
<th class="head"><p>Ghi chú</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>PORT1</p></td>
<td><p>Digital / PWM / Analog</p></td>
<td><p>GPIO 1</p></td>
<td><p>Kết nối module Grove</p></td>
</tr>
<tr class="row-odd"><td><p>PORT2</p></td>
<td><p>Digital / PWM / Analog</p></td>
<td><p>GPIO 2</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>PORT3</p></td>
<td><p>Digital / PWM / Analog</p></td>
<td><p>GPIO 3</p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>PORT4</p></td>
<td><p>Digital / PWM / Analog</p></td>
<td><p>GPIO 4</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>PORT5</p></td>
<td><p>Digital / PWM / Analog</p></td>
<td><p>GPIO 5</p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>PORT6</p></td>
<td><p>Digital / PWM / Analog</p></td>
<td><p>GPIO 6</p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>M1 / M2</p></td>
<td><p>Motor Driver</p></td>
<td><p>TB6612</p></td>
<td><p>Điều khiển DC motor</p></td>
</tr>
<tr class="row-odd"><td><p>S1–S4</p></td>
<td><p>Servo Output</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">internal</span></code></p></td>
<td><p>12-bit resolution</p></td>
</tr>
<tr class="row-even"><td><p>OLED</p></td>
<td><p>Display 0.96&quot;</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">internal</span></code></p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>IMU</p></td>
<td><p>MPU6050</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">internal</span></code></p></td>
<td><p></p></td>
</tr>
<tr class="row-even"><td><p>Buzzer</p></td>
<td><p>Âm thanh</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">internal</span></code></p></td>
<td><p></p></td>
</tr>
<tr class="row-odd"><td><p>LED1 / LED2</p></td>
<td><p>NeoPixel RGB</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">internal</span></code></p></td>
<td><p>WS2812 RGB 16,7 triệu màu</p></td>
</tr>
<tr class="row-even"><td><p>Buttons</p></td>
<td><p>A/B</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">internal</span></code></p></td>
<td><p>Có pull-up nội bộ</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="nguon-cap-va-cong-tac">
<h2>5. Nguồn cấp và công tắc<a class="headerlink" href="#nguon-cap-va-cong-tac" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-board-v2/power-section.jpg" 
    alt="Power Section" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<ul class="simple">
<li><p><strong>Cổng USB-C:</strong> cấp nguồn và nạp chương trình.</p></li>
<li><p><strong>Pin Li-ion 14500 / 18650:</strong> có thể cấp qua module sạc ngoài.</p></li>
<li><p><strong>Nút Power:</strong> bật/tắt nguồn tổng.</p></li>
<li><p><strong>LED nguồn (PWR):</strong> hiển thị trạng thái bật/tắt bo.</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="vi-xu-ly-trung-tam-esp32-s3">
<h2>6. Vi xử lý trung tâm (ESP32-S3)<a class="headerlink" href="#vi-xu-ly-trung-tam-esp32-s3" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-board-v2/esp32-s3.png" 
    alt="ESP32-S3 Module" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<p>Bo sử dụng module <strong>ESP32-S3-WROOM-1-N8R8</strong>, có:</p>
<ul class="simple">
<li><p>2 nhân xử lý (dual-core 240 MHz)</p></li>
<li><p>Tích hợp Wi-Fi &amp; Bluetooth 5.0</p></li>
<li><p>Hỗ trợ xử lý AI/ML cơ bản</p></li>
<li><p>Tốc độ cao và tiêu thụ điện năng thấp</p></li>
<li><p>Chạy được <strong>MicroPython</strong> và <strong>firmware BeE Brain</strong></p></li>
</ul>
</section>
<hr class="docutils" />
<section id="ket-noi-va-giao-tiep">
<h2>7. Kết nối và giao tiếp<a class="headerlink" href="#ket-noi-va-giao-tiep" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Giao tiếp</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>USB-C (CH340C)</strong></p></td>
<td><p>Giao tiếp Serial, nạp code từ web</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Bluetooth BLE</strong></p></td>
<td><p>Giao tiếp không dây với BeE IDE</p></td>
</tr>
<tr class="row-even"><td><p><strong>OTA (Wi-Fi)</strong></p></td>
<td><p>Cập nhật chương trình qua Wifi</p></td>
</tr>
<tr class="row-odd"><td><p><strong>I2C Bus</strong></p></td>
<td><p>Dành cho OLED, IMU, và module mở rộng</p></td>
</tr>
<tr class="row-even"><td><p><strong>PWM</strong></p></td>
<td><p>Điều khiển motor, servo, LED RGB</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Analog (ADC)</strong></p></td>
<td><p>Đọc cảm biến analog (qua PORT1–6)</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="tuong-thich-lego-technic">
<h2>8. Tương thích LEGO Technic<a class="headerlink" href="#tuong-thich-lego-technic" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-board-v2/lego-mount.jpg" 
    alt="LEGO Frame Mount" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<ul class="simple">
<li><p>BeE Board V2 có kích thước tương thích <strong>khung LEGO Technic 7×11</strong>.</p></li>
<li><p>Các lỗ bắt vít tiêu chuẩn 4.8 mm.</p></li>
<li><p>Học sinh có thể kết hợp BeE Board với LEGO để tạo robot di chuyển, xe tự hành, hoặc cánh tay máy.</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="cac-doi-tuong-trong-lap-trinh-python">
<h2>9. Các đối tượng trong lập trình Python<a class="headerlink" href="#cac-doi-tuong-trong-lap-trinh-python" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Đối tượng</p></th>
<th class="head"><p>Chức năng</p></th>
<th class="head"><p>Ví dụ sử dụng</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bee.led1</span></code>, <code class="docutils literal notranslate"><span class="pre">bee.led2</span></code></p></td>
<td><p>LED RGB tích hợp</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.led1.set_rgb(255,0,0)</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bee.buttonA</span></code>, <code class="docutils literal notranslate"><span class="pre">bee.buttonB</span></code></p></td>
<td><p>Nút nhấn</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.buttonA.is_pressed()</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bee.oled</span></code></p></td>
<td><p>Màn hình hiển thị</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.oled.write(&quot;Hello&quot;,0,0,1)</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bee.imu</span></code></p></td>
<td><p>Cảm biến nghiêng, lắc</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.imu.rollDeg</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bee.motor1</span></code>, <code class="docutils literal notranslate"><span class="pre">bee.motor2</span></code></p></td>
<td><p>Motor DC</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.motor1.speed(60)</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bee.servo1–4</span></code></p></td>
<td><p>Servo RC</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.servo3.position(90)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bee.buzzer</span></code></p></td>
<td><p>Loa buzzer</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.buzzer.play_tone(bee.buzzer.TONES['C5'])</span></code></p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bee.PORT1–6</span></code></p></td>
<td><p>GPIO mở rộng</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.digital_read(bee.PORT1)</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="tai-lieu-tham-khao">
<h2>10. Tài liệu tham khảo<a class="headerlink" href="#tai-lieu-tham-khao" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="#../2.bee-ide/index.md"><span class="xref myst">Hướng dẫn BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="../3.examples/2.led-example.html"><span class="std std-doc">Ví dụ lập trình với BeE Board V2</span></a></p></li>
<li><p><a class="reference internal" href="../4.extensions/1.index.html"><span class="std std-doc">Cài đặt thư viện mở rộng trên BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="#../5.about/about.md"><span class="xref myst">Giới thiệu BeE STEM Solutions</span></a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="2.getting-started.html" class="btn btn-neutral float-left" title="Bắt đầu" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="4.programming-guide.html" class="btn btn-neutral float-right" title="Hướng dẫn lập trình" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>