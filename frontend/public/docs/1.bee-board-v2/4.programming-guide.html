

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Hướng dẫn lập trình &mdash; <PERSON><PERSON>i <PERSON> BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Xử lý lỗi" href="5.troubleshooting.html" />
    <link rel="prev" title="Giới thiệu phần cứng" href="3.hardware-overview.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Hướng dẫn lập trình</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">1. Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-keo-tha-voi-bee-ide">2. Lập trình kéo thả với BeE IDE</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#truy-cap">Truy cập</a></li>
<li class="toctree-l3"><a class="reference internal" href="#giao-dien-chinh">Giao diện chính</a></li>
<li class="toctree-l3"><a class="reference internal" href="#nhom-khoi-lenh-co-ban">Nhóm khối lệnh cơ bản</a></li>
<li class="toctree-l3"><a class="reference internal" href="#nap-chuong-trinh-tu-bee-ide">Nạp chương trình từ BeE IDE</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#nap-qua-usb">Nạp qua USB</a></li>
<li class="toctree-l4"><a class="reference internal" href="#nap-qua-ota">Nạp qua OTA</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-python-voi-bee-python">3. Lập trình Python với BeE Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id1">Truy cập</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id2">Giao diện chính</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-chuong-trinh-co-ban">Ví dụ chương trình cơ bản</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#bat-led-1-mau-do">Bật LED 1 màu đỏ</a></li>
<li class="toctree-l4"><a class="reference internal" href="#hien-thi-chu-tren-oled">Hiển thị chữ trên OLED</a></li>
<li class="toctree-l4"><a class="reference internal" href="#robot-tien-va-quay">Robot tiến và quay</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#mot-so-ham-co-ban-trong-python">Một số hàm cơ bản trong Python</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#cap-nhat-chuong-trinh-ota-tu-bee-python">4. Cập nhật chương trình OTA từ BeE Python</a></li>
<li class="toctree-l2"><a class="reference internal" href="#goi-y-du-an-thuc-hanh">5. Gợi ý dự án thực hành</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-lieu-lien-quan">6. Tài liệu liên quan</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">Hướng dẫn lập trình</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/1.bee-board-v2/4.programming-guide.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="huong-dan-lap-trinh">
<h1>Hướng dẫn lập trình<a class="headerlink" href="#huong-dan-lap-trinh" title="Link to this heading"></a></h1>
<hr class="docutils" />
<section id="gioi-thieu">
<h2>1. Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p>BeE Board V2 có thể lập trình theo <strong>hai cách</strong> tùy vào cấp độ của học sinh:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nền tảng</p></th>
<th class="head"><p>Kiểu lập trình</p></th>
<th class="head"><p>Đối tượng</p></th>
<th class="head"><p>Đặc điểm</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>BeE IDE</strong></p></td>
<td><p>Kéo thả Blockly</p></td>
<td><p>Học sinh tiểu học, người mới học</p></td>
<td><p>Trực quan, sinh động, không hiển thị code</p></td>
</tr>
<tr class="row-odd"><td><p><strong>BeE Python</strong></p></td>
<td><p>Viết code Python</p></td>
<td><p>Học sinh THCS hoặc nâng cao</p></td>
<td><p>Gần với lập trình thật, có gợi ý cú pháp</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>⚠️ Hai nền tảng này hoạt động <strong>độc lập</strong>, không thể mở file lẫn nhau.
Học sinh có thể bắt đầu với <strong>BeE IDE</strong>, sau đó chuyển dần sang <strong>BeE Python</strong> khi đã quen tư duy lập trình.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="lap-trinh-keo-tha-voi-bee-ide">
<h2>2. Lập trình kéo thả với BeE IDE<a class="headerlink" href="#lap-trinh-keo-tha-voi-bee-ide" title="Link to this heading"></a></h2>
<section id="truy-cap">
<h3>Truy cập<a class="headerlink" href="#truy-cap" title="Link to this heading"></a></h3>
<p>👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
<p>BeE IDE là nền tảng lập trình trực quan cho trẻ em.
Mỗi chương trình được xây dựng từ các <strong>khối lệnh (Blocks)</strong> kéo thả vào vùng làm việc.</p>
<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE UI" 
    width="500" 
    style="border-radius: 10px; max-width: 90%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="giao-dien-chinh">
<h3>Giao diện chính<a class="headerlink" href="#giao-dien-chinh" title="Link to this heading"></a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Khu vực</p></th>
<th class="head"><p>Chức năng</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>① Thanh công cụ</p></td>
<td><p>Tạo mới, mở, lưu, nạp code</p></td>
<td><p>Điều khiển chung</p></td>
</tr>
<tr class="row-odd"><td><p>② Danh mục khối</p></td>
<td><p>Chứa các nhóm lệnh (LED, Button, Motor, OLED…)</p></td>
<td><p>Kéo thả lệnh từ đây</p></td>
</tr>
<tr class="row-even"><td><p>③ Vùng làm việc</p></td>
<td><p>Nơi ghép nối các khối lệnh</p></td>
<td><p>Xây dựng chương trình</p></td>
</tr>
<tr class="row-odd"><td><p>④ Khu điều khiển</p></td>
<td><p>Nút <strong>Upload</strong></p></td>
<td><p>Nạp và chạy code trên BeE Board</p></td>
</tr>
<tr class="row-even"><td><p>⑤ Log</p></td>
<td><p>Hiển thị thông báo</p></td>
<td><p>Kết quả và thông báo lỗi</p></td>
</tr>
<tr class="row-odd"><td><p>⑤ BeE Assistant</p></td>
<td><p>Trợ lý AI giúp lập trình</p></td>
<td><p>Hỗ trợ giải thích các khối lệnh</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="nhom-khoi-lenh-co-ban">
<h3>Nhóm khối lệnh cơ bản<a class="headerlink" href="#nhom-khoi-lenh-co-ban" title="Link to this heading"></a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nhóm</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Ví dụ khối</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>LED</strong></p></td>
<td><p>Bật/tắt hoặc đổi màu LED RGB</p></td>
<td><p>“Bật LED1 màu đỏ”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Button</strong></p></td>
<td><p>Kiểm tra nút nhấn A hoặc B</p></td>
<td><p>“Nếu nút A được nhấn”</p></td>
</tr>
<tr class="row-even"><td><p><strong>Display</strong></p></td>
<td><p>Hiển thị chữ, hình, biểu tượng</p></td>
<td><p>“Hiển thị ‘Hello’ tại (0,0)”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Write Signal</strong></p></td>
<td><p>Ghi tín hiệu số, analog từ PORT1-6</p></td>
<td><p>“Ghi 50% PWM vào PORT1”</p></td>
</tr>
<tr class="row-even"><td><p><strong>Read Signal</strong></p></td>
<td><p>Đọc tín hiệu số, analog từ PORT1-6</p></td>
<td><p>“Đọc tín hiệu nút nhấn từ PORT1”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Buzzer</strong></p></td>
<td><p>Phát âm thanh hoặc giai điệu</p></td>
<td><p>“Phát nốt C5 trong 1/4 giây”</p></td>
</tr>
<tr class="row-even"><td><p><strong>Gyroscope</strong></p></td>
<td><p>Phát hiện nghiêng hoặc rung</p></td>
<td><p>“Nếu board bị lắc thì bật đèn”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Motor &amp; Servo</strong></p></td>
<td><p>Điều khiển động cơ hoặc servo</p></td>
<td><p>“Động cơ M1 quay với tốc độ 50%”</p></td>
</tr>
<tr class="row-even"><td><p><strong>Movement</strong></p></td>
<td><p>Điều khiển robot di chuyển</p></td>
<td><p>“Robot tiến 2 giây”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Advanced</strong></p></td>
<td><p>OTA, Reset, Kết nối</p></td>
<td><p>“Cấu hình OTA Wi-Fi”</p></td>
</tr>
</tbody>
</table>
<p align="center">
  <img 
    src="../_static/bee-board-v2/blockly-example-led.png" 
    alt="Blockly LED Example" 
    width="500" 
    style="border-radius: 10px; max-width: 90%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="nap-chuong-trinh-tu-bee-ide">
<h3>Nạp chương trình từ BeE IDE<a class="headerlink" href="#nap-chuong-trinh-tu-bee-ide" title="Link to this heading"></a></h3>
<p>BeE IDE có hai cách nạp chương trình:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Phương thức</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Khi sử dụng</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>USB (Serial)</strong></p></td>
<td><p>Kết nối qua cáp USB-C</p></td>
<td><p>Khi học trực tiếp trong lớp</p></td>
</tr>
<tr class="row-odd"><td><p><strong>OTA (Wi-Fi)</strong></p></td>
<td><p>Cập nhật code không dây</p></td>
<td><p>Khi board đã kết nối mạng Wi-Fi</p></td>
</tr>
</tbody>
</table>
<section id="nap-qua-usb">
<h4>Nạp qua USB<a class="headerlink" href="#nap-qua-usb" title="Link to this heading"></a></h4>
<ol class="arabic simple">
<li><p>Cắm BeE Board vào máy tính bằng <strong>cáp USB-C</strong></p></li>
<li><p>Nhấn <strong>Kết nối → Chọn cổng Serial (COMx / usbmodem)</strong></p></li>
<li><p>Nhấn <strong>Upload ▶️</strong> để tải code xuống board</p></li>
</ol>
</section>
<section id="nap-qua-ota">
<h4>Nạp qua OTA<a class="headerlink" href="#nap-qua-ota" title="Link to this heading"></a></h4>
<ol class="arabic simple">
<li><p>Kéo khối “Setup OTA” và nhập Wi-Fi + mật khẩu</p></li>
<li><p>Sau khi board hiển thị <strong>IP</strong> trên màn hình OLED</p></li>
<li><p>Chọn <strong>Upload OTA</strong> → nhập IP đó để tải code không dây</p></li>
</ol>
</section>
</section>
</section>
<hr class="docutils" />
<section id="lap-trinh-python-voi-bee-python">
<h2>3. Lập trình Python với BeE Python<a class="headerlink" href="#lap-trinh-python-voi-bee-python" title="Link to this heading"></a></h2>
<section id="id1">
<h3>Truy cập<a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<p>👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/python">https://beestemsolutions.com.vn/studio/python</a></p>
<p>BeE Python là nền tảng web cho học sinh lập trình trực tiếp bằng <strong>ngôn ngữ Python (MicroPython)</strong>.</p>
<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-python.png" 
    alt="BeE Python" 
    width="500" 
    style="border-radius: 10px; max-width: 90%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="id2">
<h3>Giao diện chính<a class="headerlink" href="#id2" title="Link to this heading"></a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Khu vực</p></th>
<th class="head"><p>Chức năng</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>① Thanh công cụ</p></td>
<td><p>Kết nối, tải code, reset board</p></td>
<td><p>Giao tiếp trực tiếp với BeE Board</p></td>
</tr>
<tr class="row-odd"><td><p>② Quản lý dự án</p></td>
<td><p>Tạo mới, mở, lưu</p></td>
<td><p>Quản lý dự án</p></td>
</tr>
<tr class="row-even"><td><p>③ Trình soạn thảo code</p></td>
<td><p>Monaco Editor (giống VS Code)</p></td>
<td><p>Viết code Python</p></td>
</tr>
<tr class="row-odd"><td><p>④ Console</p></td>
<td><p>Hiển thị kết quả &amp; thông báo</p></td>
<td><p>Có thể nhập lệnh trực tiếp</p></td>
</tr>
<tr class="row-even"><td><p>⑤ Thanh trạng thái</p></td>
<td><p>Hiển thị cổng kết nối, trạng thái</p></td>
<td><p>Theo dõi trạng thái board</p></td>
</tr>
<tr class="row-odd"><td><p>⑥ BeE Assistant</p></td>
<td><p>Trợ lý AI giúp lập trình</p></td>
<td><p>Hỗ trợ giải thích các khối lệnh</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="vi-du-chuong-trinh-co-ban">
<h3>Ví dụ chương trình cơ bản<a class="headerlink" href="#vi-du-chuong-trinh-co-ban" title="Link to this heading"></a></h3>
<section id="bat-led-1-mau-do">
<h4>Bật LED 1 màu đỏ<a class="headerlink" href="#bat-led-1-mau-do" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>

<span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="hien-thi-chu-tren-oled">
<h4>Hiển thị chữ trên OLED<a class="headerlink" href="#hien-thi-chu-tren-oled" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>

<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Hello BeE!&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="robot-tien-va-quay">
<h4>Robot tiến và quay<a class="headerlink" href="#robot-tien-va-quay" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>

<span class="n">bee</span><span class="o">.</span><span class="n">move_forward</span><span class="p">(</span><span class="mi">50</span><span class="p">,</span> <span class="mi">2</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">turn_left</span><span class="p">(</span><span class="mi">60</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<hr class="docutils" />
<section id="mot-so-ham-co-ban-trong-python">
<h3>Một số hàm cơ bản trong Python<a class="headerlink" href="#mot-so-ham-co-ban-trong-python" title="Link to this heading"></a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nhóm</p></th>
<th class="head"><p>Hàm / Lệnh</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>LED</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.led1.set_rgb(r,g,b)</span></code></p></td>
<td><p>Bật LED RGB</p></td>
</tr>
<tr class="row-odd"><td><p>OLED</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.oled.write(text,</span> <span class="pre">x,</span> <span class="pre">y,</span> <span class="pre">size)</span></code></p></td>
<td><p>Ghi chữ lên màn hình</p></td>
</tr>
<tr class="row-even"><td><p>Button</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.buttonA.is_pressed()</span></code></p></td>
<td><p>Kiểm tra nút nhấn</p></td>
</tr>
<tr class="row-odd"><td><p>IMU</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.imu.is_shaking()</span></code></p></td>
<td><p>Phát hiện lắc</p></td>
</tr>
<tr class="row-even"><td><p>Motor</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.motor1.speed(power)</span></code></p></td>
<td><p>Chạy động cơ</p></td>
</tr>
<tr class="row-odd"><td><p>Servo</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.servo1.position(angle)</span></code></p></td>
<td><p>Quay servo</p></td>
</tr>
<tr class="row-even"><td><p>Buzzer</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.buzzer.play_tone(note)</span></code></p></td>
<td><p>Phát âm thanh</p></td>
</tr>
<tr class="row-odd"><td><p>Reset</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.init_bee()</span></code></p></td>
<td><p>Khởi tạo lại hệ thống</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<hr class="docutils" />
<section id="cap-nhat-chuong-trinh-ota-tu-bee-python">
<h2>4. Cập nhật chương trình OTA từ BeE Python<a class="headerlink" href="#cap-nhat-chuong-trinh-ota-tu-bee-python" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p>Chạy chương trình “Setup OTA” từ BeE IDE hoặc Python</p></li>
<li><p>OLED hiển thị <strong>địa chỉ IP</strong></p></li>
<li><p>Trong BeE Python, chọn <strong>Upload OTA → nhập IP</strong></p></li>
<li><p>Nhấn <strong>Run ▶️</strong> để tải và chạy code qua Wi-Fi</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/bee-board-v2/upload-ota.png" 
    alt="BeE OTA Example" 
    width="200" 
    style="border-radius: 10px; max-width: 90%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="goi-y-du-an-thuc-hanh">
<h2>5. Gợi ý dự án thực hành<a class="headerlink" href="#goi-y-du-an-thuc-hanh" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Dự án</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Kiến thức áp dụng</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Đèn giao thông mini</p></td>
<td><p>Dùng LED RGB đổi màu theo thời gian</p></td>
<td><p>Điều khiển LED + Timer</p></td>
</tr>
<tr class="row-odd"><td><p>Chuông cửa thông minh</p></td>
<td><p>Phát nhạc khi nhấn nút</p></td>
<td><p>Button + Buzzer</p></td>
</tr>
<tr class="row-even"><td><p>Robot tránh vật cản</p></td>
<td><p>Xe tự động quay khi phát hiện rung</p></td>
<td><p>IMU + Motor</p></td>
</tr>
<tr class="row-odd"><td><p>Nhiệt kế hiển thị OLED</p></td>
<td><p>Đọc cảm biến DHT11 qua cổng Grove</p></td>
<td><p>PORT + OLED</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="tai-lieu-lien-quan">
<h2>6. Tài liệu liên quan<a class="headerlink" href="#tai-lieu-lien-quan" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="#../2.bee-ide/index.md"><span class="xref myst">Hướng dẫn BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="../3.examples/2.led-example.html"><span class="std std-doc">Ví dụ lập trình với BeE Board V2</span></a></p></li>
<li><p><a class="reference internal" href="../4.extensions/1.index.html"><span class="std std-doc">Cài đặt thư viện mở rộng trên BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="#../5.about/about.md"><span class="xref myst">Giới thiệu BeE STEM Solutions</span></a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="3.hardware-overview.html" class="btn btn-neutral float-left" title="Giới thiệu phần cứng" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="5.troubleshooting.html" class="btn btn-neutral float-right" title="Xử lý lỗi" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>