

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Xử lý lỗi &mdash; <PERSON><PERSON>i <PERSON> Be<PERSON> Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="BeE IDE" href="../2.bee-ide/1.index.html" />
    <link rel="prev" title="Hướng dẫn lập trình" href="4.programming-guide.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Xử lý lỗi</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">1. Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-khi-ket-noi-usb">2. Lỗi khi kết nối USB</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-khi-nap-code-ota-wi-fi">3. Lỗi khi nạp code OTA (Wi-Fi)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-led-hoac-button">4. Lỗi LED hoặc Button</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-man-hinh-oled">5. Lỗi màn hình OLED</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-loa-buzzer">6. Lỗi loa Buzzer</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-khi-lap-trinh-python">7. Lỗi khi lập trình Python</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-trong-bee-ide">8. Lỗi trong BeE IDE</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-trong-bee-python-code-editor">9. Lỗi trong BeE Python (Code Editor)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#su-co-nguon-dien">10. Sự cố nguồn điện</a></li>
<li class="toctree-l2"><a class="reference internal" href="#kiem-tra-nhanh-bang-code-test">11. Kiểm tra nhanh bằng code test</a></li>
<li class="toctree-l2"><a class="reference internal" href="#lien-he-ho-tro">12. Liên hệ hỗ trợ</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">Xử lý lỗi</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/1.bee-board-v2/5.troubleshooting.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="xu-ly-loi">
<h1>Xử lý lỗi<a class="headerlink" href="#xu-ly-loi" title="Link to this heading"></a></h1>
<hr class="docutils" />
<section id="gioi-thieu">
<h2>1. Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p>Phần này giúp bạn <strong>xác định và khắc phục các lỗi thường gặp</strong> khi sử dụng <strong>BeE Board V2</strong> với <strong>BeE IDE</strong> hoặc <strong>BeE Python</strong>.
Hãy làm theo từng bước, và bạn sẽ nhanh chóng tìm ra nguyên nhân 😎</p>
</section>
<hr class="docutils" />
<section id="loi-khi-ket-noi-usb">
<h2>2. Lỗi khi kết nối USB<a class="headerlink" href="#loi-khi-ket-noi-usb" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Triệu chứng</p></th>
<th class="head"><p>Nguyên nhân có thể</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Không thấy cổng COM/USB khi cắm board</p></td>
<td><p>Chưa cài driver CH340C</p></td>
<td><p>Tải và cài đặt driver tại <a class="reference external" href="https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board">đây</a></p></td>
</tr>
<tr class="row-odd"><td><p>Đã cắm USB nhưng IDE không nhận board</p></td>
<td><p>Cáp USB chỉ sạc, không truyền dữ liệu</p></td>
<td><p>Dùng cáp USB-C hỗ trợ <strong>data</strong> (loại tốt, 4 dây)</p></td>
</tr>
<tr class="row-even"><td><p>IDE hiển thị “Kết nối thất bại”</p></td>
<td><p>Board đang chạy chương trình chiếm cổng serial</p></td>
<td><p>Nhấn <strong>Reset</strong> trước khi chọn kết nối USB</p></td>
</tr>
<tr class="row-odd"><td><p>Board tự ngắt sau vài giây</p></td>
<td><p>Nguồn USB yếu hoặc không ổn định</p></td>
<td><p>Dùng cổng USB máy tính trực tiếp, tránh hub hoặc dây quá dài</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="loi-khi-nap-code-ota-wi-fi">
<h2>3. Lỗi khi nạp code OTA (Wi-Fi)<a class="headerlink" href="#loi-khi-nap-code-ota-wi-fi" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Triệu chứng</p></th>
<th class="head"><p>Nguyên nhân có thể</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>OLED không hiển thị IP OTA</p></td>
<td><p>Wi-Fi yếu hoặc nhập sai mật khẩu</p></td>
<td><p>Kiểm tra Wi-Fi, nhập lại đúng SSID và Password</p></td>
</tr>
<tr class="row-odd"><td><p>IDE báo “Connot connect IP OTA”</p></td>
<td><p>Board và máy tính không cùng mạng Wi-Fi</p></td>
<td><p>Đảm bảo cả 2 thiết bị trong cùng mạng LAN</p></td>
</tr>
<tr class="row-even"><td><p>OTA nạp xong nhưng code không chạy</p></td>
<td><p>Lỗi trong code mới</p></td>
<td><p>Reset lại board (<code class="docutils literal notranslate"><span class="pre">bee.init_bee()</span></code>) để khởi động lại</p></td>
</tr>
<tr class="row-odd"><td><p>Không thấy thông báo OTA trong IDE</p></td>
<td><p>Board chưa khởi tạo OTA</p></td>
<td><p>Thêm block “Setup OTA” trước khi chạy</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="loi-led-hoac-button">
<h2>4. Lỗi LED hoặc Button<a class="headerlink" href="#loi-led-hoac-button" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Triệu chứng</p></th>
<th class="head"><p>Nguyên nhân có thể</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>LED không sáng</p></td>
<td><p>Lỗi code hoặc chưa khởi tạo</p></td>
<td><p>Không chạy lệnh <code class="docutils literal notranslate"><span class="pre">bee.led1.off()</span></code> ngay sau <code class="docutils literal notranslate"><span class="pre">bee.led1.on()</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>LED chỉ sáng 1 màu</p></td>
<td><p>Tham số R,G,B sai</p></td>
<td><p>Kiểm tra <code class="docutils literal notranslate"><span class="pre">bee.led1.set_rgb(255,0,0)</span></code> (R=255, G=0, B=0 là màu đỏ)</p></td>
</tr>
<tr class="row-even"><td><p>Nút nhấn không phản hồi</p></td>
<td><p>Board chưa cập nhật dữ liệu</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">bee.buttonA.is_pressed()</span></code> trong vòng lặp <code class="docutils literal notranslate"><span class="pre">while</span> <span class="pre">True:</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="loi-man-hinh-oled">
<h2>5. Lỗi màn hình OLED<a class="headerlink" href="#loi-man-hinh-oled" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Triệu chứng</p></th>
<th class="head"><p>Nguyên nhân có thể</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Màn hình không sáng</p></td>
<td><p>Code lỗi</p></td>
<td><p>Reset board và thử <code class="docutils literal notranslate"><span class="pre">bee.oled.clear()</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Hiển thị sai ký tự / nhấp nháy</p></td>
<td><p>Chạy quá nhiều lệnh ghi liên tiếp</p></td>
<td><p>Thêm <code class="docutils literal notranslate"><span class="pre">time.sleep(0.1)</span></code> giữa các lệnh</p></td>
</tr>
<tr class="row-even"><td><p>Không hiển thị chữ mới / nội dung bị ghi đè</p></td>
<td><p>Chưa xóa nội dung cũ</p></td>
<td><p>Gọi <code class="docutils literal notranslate"><span class="pre">bee.oled.clear()</span></code> trước khi <code class="docutils literal notranslate"><span class="pre">bee.oled.write()</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="loi-loa-buzzer">
<h2>6. Lỗi loa Buzzer<a class="headerlink" href="#loi-loa-buzzer" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Triệu chứng</p></th>
<th class="head"><p>Nguyên nhân có thể</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Không nghe tiếng</p></td>
<td><p>Chưa gọi <code class="docutils literal notranslate"><span class="pre">bee.buzzer.play_tone()</span></code> hoặc volume = 0</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">bee.buzzer.set_volume(80)</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Tiếng rè hoặc nhỏ</p></td>
<td><p>Board thiếu nguồn</p></td>
<td><p>Cấp nguồn qua USB ổn định hoặc pin đầy</p></td>
</tr>
<tr class="row-even"><td><p>Kêu liên tục</p></td>
<td><p>Quên gọi <code class="docutils literal notranslate"><span class="pre">bee.buzzer.be_quiet()</span></code></p></td>
<td><p>Thêm lệnh tắt sau khi phát âm thanh</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="loi-khi-lap-trinh-python">
<h2>7. Lỗi khi lập trình Python<a class="headerlink" href="#loi-khi-lap-trinh-python" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Lỗi / Thông báo</p></th>
<th class="head"><p>Nguyên nhân</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">NameError:</span> <span class="pre">bee</span> <span class="pre">is</span> <span class="pre">not</span> <span class="pre">defined</span></code></p></td>
<td><p>Quên import thư viện</p></td>
<td><p>Thêm <code class="docutils literal notranslate"><span class="pre">from</span> <span class="pre">BeeBrain</span> <span class="pre">import</span> <span class="pre">bee</span></code> ở đầu file</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">OSError:</span> <span class="pre">[Errno</span> <span class="pre">19]</span> <span class="pre">ENODEV</span></code></p></td>
<td><p>Board không kết nối đúng</p></td>
<td><p>Kiểm tra lại cổng USB / COM</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">SyntaxError</span></code></p></td>
<td><p>Sai cú pháp Python</p></td>
<td><p>Kiểm tra dấu <code class="docutils literal notranslate"><span class="pre">:</span></code> hoặc thụt dòng</p></td>
</tr>
<tr class="row-odd"><td><p>Không thấy kết quả OLED</p></td>
<td><p>Code chạy nhưng không render</p></td>
<td><p>Thêm <code class="docutils literal notranslate"><span class="pre">bee.oled.clear()</span></code> và <code class="docutils literal notranslate"><span class="pre">time.sleep()</span></code> ngắn</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="loi-trong-bee-ide">
<h2>8. Lỗi trong BeE IDE<a class="headerlink" href="#loi-trong-bee-ide" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Triệu chứng</p></th>
<th class="head"><p>Nguyên nhân có thể</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Khối lệnh không nạp được</p></td>
<td><p>Chưa kết nối cổng Serial</p></td>
<td><p>Nhấn “Kết nối” và chọn đúng cổng COM</p></td>
</tr>
<tr class="row-odd"><td><p>Block “Setup OTA” không hoạt động</p></td>
<td><p>Wi-Fi nhập sai hoặc chưa lưu</p></td>
<td><p>Kiểm tra thông tin Wi-Fi</p></td>
</tr>
<tr class="row-even"><td><p>Khối LED không sáng</p></td>
<td><p>Chưa chọn số LED đúng</p></td>
<td><p>Kiểm tra giá trị trong khối “LED số …”</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="loi-trong-bee-python-code-editor">
<h2>9. Lỗi trong BeE Python (Code Editor)<a class="headerlink" href="#loi-trong-bee-python-code-editor" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Triệu chứng</p></th>
<th class="head"><p>Nguyên nhân có thể</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>IDE không nhận board</p></td>
<td><p>Chưa cấp quyền Serial trong trình duyệt</p></td>
<td><p>Nhấn “Cho phép truy cập thiết bị USB” khi trình duyệt hỏi</p></td>
</tr>
<tr class="row-odd"><td><p>Code nạp xong nhưng không chạy</p></td>
<td><p>File <code class="docutils literal notranslate"><span class="pre">main.py</span></code> cũ bị lỗi</p></td>
<td><p>Nhấn nút reset trên board</p></td>
</tr>
<tr class="row-even"><td><p>Không lưu được code</p></td>
<td><p>Trình duyệt bị hạn chế quyền Local Storage</p></td>
<td><p>Cho phép BeE Python lưu file trong trình duyệt</p></td>
</tr>
<tr class="row-odd"><td><p>Không thấy log trong console</p></td>
<td><p>Board không trả về dữ liệu</p></td>
<td><p>Reset board và thử lại</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="su-co-nguon-dien">
<h2>10. Sự cố nguồn điện<a class="headerlink" href="#su-co-nguon-dien" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Triệu chứng</p></th>
<th class="head"><p>Nguyên nhân</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Board không sáng đèn nguồn</p></td>
<td><p>Chưa bật công tắc hoặc pin yếu</p></td>
<td><p>Sạc pin hoặc cấp nguồn qua USB</p></td>
</tr>
<tr class="row-odd"><td><p>Board khởi động lại liên tục</p></td>
<td><p>Nguồn USB không đủ dòng</p></td>
<td><p>Dùng cáp USB tốt và cổng 5V-2A</p></td>
</tr>
<tr class="row-even"><td><p>Board quá nóng</p></td>
<td><p>Chập mạch hoặc lỗi nguồn</p></td>
<td><p>Ngắt kết nối, kiểm tra lại mạch và module cắm ngoài</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="kiem-tra-nhanh-bang-code-test">
<h2>11. Kiểm tra nhanh bằng code test<a class="headerlink" href="#kiem-tra-nhanh-bang-code-test" title="Link to this heading"></a></h2>
<p>Dán đoạn code sau vào BeE Python để kiểm tra nhanh phần cứng:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">bee</span><span class="o">.</span><span class="n">init_bee</span><span class="p">()</span>
<span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span><span class="mi">0</span><span class="p">,</span><span class="mi">0</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">led2</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span><span class="mi">255</span><span class="p">,</span><span class="mi">0</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;BeE OK!&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">TONES</span><span class="p">[</span><span class="s1">&#39;C5&#39;</span><span class="p">])</span>
<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">be_quiet</span><span class="p">()</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Test hoàn tất!&quot;</span><span class="p">)</span>
</pre></div>
</div>
<p>Nếu OLED hiển thị chữ “BeE OK!” và nghe tiếng beep,
nghĩa là board hoạt động bình thường 🎉</p>
</section>
<hr class="docutils" />
<section id="lien-he-ho-tro">
<h2>12. Liên hệ hỗ trợ<a class="headerlink" href="#lien-he-ho-tro" title="Link to this heading"></a></h2>
<p>Nếu bạn đã thử các bước trên mà vẫn gặp lỗi,
hãy liên hệ đội ngũ BeE STEM Solutions để được hỗ trợ:</p>
<ul class="simple">
<li><p><strong>Website:</strong> <a class="reference external" href="https://beestemsolutions.com.vn">https://beestemsolutions.com.vn</a></p></li>
<li><p><strong>Facebook:</strong> <a class="reference external" href="https://facebook.com/beestemsolutions.com.vn">beestemsolutions.com.vn</a></p></li>
<li><p><strong>TikTok:</strong> <a class="reference external" href="https://tiktok.com/&#64;beestemsolutions.com.vn">&#64;beestem.vn</a></p></li>
<li><p><strong>Email:</strong> <a class="reference external" href="mailto:beestemsolutions&#46;info&#37;&#52;&#48;gmail&#46;com">beestemsolutions<span>&#46;</span>info<span>&#64;</span>gmail<span>&#46;</span>com</a></p></li>
<li><p><strong>Hotline:</strong> <a class="reference internal" href="#tel:0987845231"><span class="xref myst">0987845231</span></a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="4.programming-guide.html" class="btn btn-neutral float-left" title="Hướng dẫn lập trình" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../2.bee-ide/1.index.html" class="btn btn-neutral float-right" title="BeE IDE" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>