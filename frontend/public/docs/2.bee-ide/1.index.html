

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>BeE IDE &mdash; Tài <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="LED RGB" href="../3.examples/2.led-example.html" />
    <link rel="prev" title="Xử lý lỗi" href="../1.bee-board-v2/5.troubleshooting.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">BeE IDE</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#bee-ide-la-gi">BeE IDE là gì?</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cac-tinh-nang-noi-bat">Các tính năng nổi bật</a></li>
<li class="toctree-l2"><a class="reference internal" href="#giao-dien-bee-ide">Giao diện BeE IDE</a></li>
<li class="toctree-l2"><a class="reference internal" href="#nhom-khoi-lenh-chinh">Nhóm khối lệnh chính</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cach-ket-noi-bee-board-v2">Cách kết nối BeE Board V2</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#qua-usb">Qua USB</a></li>
<li class="toctree-l3"><a class="reference internal" href="#qua-wi-fi-ota">Qua Wi-Fi (OTA)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#quan-ly-du-an">Quản lý dự án</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thuc-hanh-voi-bee-ide">Thực hành với BeE IDE</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l3"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l3"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l3"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l3"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l3"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l3"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#meo-hoc-hieu-qua">Mẹo học hiệu quả</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-lieu-lien-quan">Tài liệu liên quan</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">BeE IDE</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/2.bee-ide/1.index.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="bee-ide">
<h1>BeE IDE<a class="headerlink" href="#bee-ide" title="Link to this heading"></a></h1>
<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<hr class="docutils" />
<section id="bee-ide-la-gi">
<h2>BeE IDE là gì?<a class="headerlink" href="#bee-ide-la-gi" title="Link to this heading"></a></h2>
<p><strong>BeE IDE</strong> là nền tảng <strong>lập trình kéo thả trực quan (Blockly)</strong> được phát triển bởi <strong>BeE STEM Solutions</strong> 🇻🇳.
Nó giúp học sinh, đặc biệt là cấp Tiểu học và THCS, <strong>học lập trình thông qua thao tác kéo thả khối lệnh</strong>, không cần gõ code.</p>
<blockquote>
<div><p>🌐 Truy cập trực tiếp:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
</div></blockquote>
<p>BeE IDE hoạt động hoàn toàn <strong>trên trình duyệt web</strong>,
không cần cài đặt phần mềm — chỉ cần cắm <strong>BeE Board V2</strong> là có thể lập trình ngay!</p>
</section>
<hr class="docutils" />
<section id="cac-tinh-nang-noi-bat">
<h2>Các tính năng nổi bật<a class="headerlink" href="#cac-tinh-nang-noi-bat" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Tính năng</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Lập trình kéo thả Blockly</strong></p></td>
<td><p>Dễ học, sinh động, phù hợp trẻ em</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Kết nối trực tiếp với BeE Board V2</strong></p></td>
<td><p>Qua USB, Bluetooth hoặc OTA Wi-Fi</p></td>
</tr>
<tr class="row-even"><td><p><strong>Nhóm khối phong phú</strong></p></td>
<td><p>LED, Buzzer, Button, OLED, Motor, Servo, IMU…</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Xuất và lưu dự án</strong></p></td>
<td><p>Lưu trên trình duyệt hoặc tải file <code class="docutils literal notranslate"><span class="pre">.json</span></code></p></td>
</tr>
<tr class="row-even"><td><p><strong>Giao diện web</strong></p></td>
<td><p>Chạy trên mọi thiết bị: Windows, macOS, Linux, Chromebook</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Tải chương trình OTA</strong></p></td>
<td><p>Nạp code không dây qua mạng Wi-Fi</p></td>
</tr>
<tr class="row-even"><td><p><strong>Cài đặt thư viện mở rộng</strong></p></td>
<td><p>Tích hợp các thư viện các module mở rộng bên ngoài</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="giao-dien-bee-ide">
<h2>Giao diện BeE IDE<a class="headerlink" href="#giao-dien-bee-ide" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-ide/bee-ide.png" 
    alt="BeE IDE UI" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Khu vực</p></th>
<th class="head"><p>Chức năng</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>① Thanh công cụ</p></td>
<td><p>Tạo mới, mở, lưu, nạp code</p></td>
<td><p>Điều khiển chung</p></td>
</tr>
<tr class="row-odd"><td><p>② Danh mục khối</p></td>
<td><p>Chứa các nhóm lệnh (LED, Button, Motor, OLED…)</p></td>
<td><p>Kéo thả lệnh từ đây</p></td>
</tr>
<tr class="row-even"><td><p>③ Vùng làm việc</p></td>
<td><p>Nơi ghép nối các khối lệnh</p></td>
<td><p>Xây dựng chương trình</p></td>
</tr>
<tr class="row-odd"><td><p>④ Khu điều khiển</p></td>
<td><p>Nút <strong>Upload</strong></p></td>
<td><p>Nạp và chạy code trên BeE Board</p></td>
</tr>
<tr class="row-even"><td><p>⑤ Log</p></td>
<td><p>Hiển thị thông báo</p></td>
<td><p>Kết quả và thông báo lỗi</p></td>
</tr>
<tr class="row-odd"><td><p>⑥ BeE Assistant</p></td>
<td><p>Trợ lý AI giúp lập trình</p></td>
<td><p>Hỗ trợ giải thích các khối lệnh</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="nhom-khoi-lenh-chinh">
<h2>Nhóm khối lệnh chính<a class="headerlink" href="#nhom-khoi-lenh-chinh" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nhóm</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Ví dụ</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>LED</strong></p></td>
<td><p>Bật/tắt hoặc đổi màu LED RGB</p></td>
<td><p>“Bật LED1 màu đỏ”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Button</strong></p></td>
<td><p>Kiểm tra nút nhấn A hoặc B</p></td>
<td><p>“Nếu nút A được nhấn”</p></td>
</tr>
<tr class="row-even"><td><p><strong>Display</strong></p></td>
<td><p>Hiển thị chữ, hình, biểu tượng</p></td>
<td><p>“Hiển thị ‘Hello’ tại (0,0)”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Write Signal</strong></p></td>
<td><p>Ghi tín hiệu số, analog từ PORT1-6</p></td>
<td><p>“Ghi 50% PWM vào PORT1”</p></td>
</tr>
<tr class="row-even"><td><p><strong>Read Signal</strong></p></td>
<td><p>Đọc tín hiệu số, analog từ PORT1-6</p></td>
<td><p>“Đọc tín hiệu nút nhấn từ PORT1”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Buzzer</strong></p></td>
<td><p>Phát âm thanh hoặc giai điệu</p></td>
<td><p>“Phát nốt C5 trong 1/4 giây”</p></td>
</tr>
<tr class="row-even"><td><p><strong>Gyroscope</strong></p></td>
<td><p>Phát hiện nghiêng hoặc rung</p></td>
<td><p>“Nếu board bị lắc thì bật đèn”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Motor &amp; Servo</strong></p></td>
<td><p>Điều khiển động cơ hoặc servo</p></td>
<td><p>“Động cơ M1 quay với tốc độ 50%”</p></td>
</tr>
<tr class="row-even"><td><p><strong>Movement</strong></p></td>
<td><p>Điều khiển robot di chuyển</p></td>
<td><p>“Robot tiến 2 giây”</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Advanced</strong></p></td>
<td><p>OTA, Reset, Kết nối</p></td>
<td><p>“Cấu hình OTA Wi-Fi”</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="cach-ket-noi-bee-board-v2">
<h2>Cách kết nối BeE Board V2<a class="headerlink" href="#cach-ket-noi-bee-board-v2" title="Link to this heading"></a></h2>
<section id="qua-usb">
<h3>Qua USB<a class="headerlink" href="#qua-usb" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Cắm cáp USB-C vào BeE Board V2</p></li>
<li><p>Chọn <strong>Kết nối → Chọn cổng Serial (COMx / /dev/cu.usbmodem)</strong></p></li>
<li><p>Nhấn <strong>Run ▶️</strong> để nạp chương trình</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/bee-board-v2/connect-usb.jpg" 
    alt="USB Connect" 
    width="250" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="qua-wi-fi-ota">
<h3>Qua Wi-Fi (OTA)<a class="headerlink" href="#qua-wi-fi-ota" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Kéo khối “Setup OTA” và nhập Wi-Fi + mật khẩu</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/bee-board-v2/setup-ota.png" 
    alt="Setup OTA" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<ol class="arabic simple" start="2">
<li><p>Sau khi OLED hiển thị <strong>địa chỉ IP</strong>, chọn <strong>Upload OTA</strong></p></li>
<li><p>Nhập IP đó để tải chương trình không dây</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/bee-board-v2/upload-ota.png" 
    alt="Upload OTA" 
    width="250" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
</section>
</section>
<hr class="docutils" />
<section id="quan-ly-du-an">
<h2>Quản lý dự án<a class="headerlink" href="#quan-ly-du-an" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Hành động</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Lưu dự án</strong></p></td>
<td><p>Nhấn “Save Project” để lưu trong trình duyệt</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Mở dự án</strong></p></td>
<td><p>Nhấn “Open Project” để chọn file <code class="docutils literal notranslate"><span class="pre">.json</span></code></p></td>
</tr>
<tr class="row-even"><td><p><strong>Xuất file</strong></p></td>
<td><p>Tải file <code class="docutils literal notranslate"><span class="pre">.json</span></code> để lưu trữ hoặc chia sẻ</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>Các file <code class="docutils literal notranslate"><span class="pre">.json</span></code> có thể mở lại trực tiếp trên BeE IDE ở bất kỳ máy tính nào.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="thuc-hanh-voi-bee-ide">
<h2>Thực hành với BeE IDE<a class="headerlink" href="#thuc-hanh-voi-bee-ide" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">Ví dụ Blockly</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
</div>
</section>
<hr class="docutils" />
<section id="meo-hoc-hieu-qua">
<h2>Mẹo học hiệu quả<a class="headerlink" href="#meo-hoc-hieu-qua" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p>Bắt đầu với LED và Button để hiểu khối lệnh cơ bản</p></li>
<li><p>Thử tạo <strong>âm thanh + đèn LED</strong> để làm mini piano 🎹</p></li>
<li><p>Kết hợp <strong>IMU + Motor</strong> để làm robot tự quay khi rung</p></li>
<li><p>Lưu dự án mỗi buổi học để theo dõi tiến độ</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="tai-lieu-lien-quan">
<h2>Tài liệu liên quan<a class="headerlink" href="#tai-lieu-lien-quan" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html"><span class="std std-doc">Bắt đầu với BeE Board V2</span></a></p></li>
<li><p><a class="reference internal" href="../4.extensions/1.index.html"><span class="std std-doc">Cài đặt mở rộng thư viện trên BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html"><span class="std std-doc">Giới thiệu phần cứng BeE Board V2</span></a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../1.bee-board-v2/5.troubleshooting.html" class="btn btn-neutral float-left" title="Xử lý lỗi" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../3.examples/2.led-example.html" class="btn btn-neutral float-right" title="LED RGB" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>