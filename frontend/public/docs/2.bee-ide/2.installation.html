

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Cài đặt &mdash; Tài <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Giao diện" href="3.user-interface.html" />
    <link rel="prev" title="Cảm biến IMU" href="../3.examples/8.imu-example.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">BeE IDE</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Cài đặt</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#muc-tieu">Mục tiêu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#yeu-cau-he-thong">1. Yêu cầu hệ thống</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cai-dat-driver-ch340c">2. Cài đặt driver CH340C</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#windows">Windows</a></li>
<li class="toctree-l3"><a class="reference internal" href="#macos-linux">MacOS &amp; Linux</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#truy-cap-nen-tang-lap-trinh">3. Truy cập nền tảng lập trình</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ket-noi-bee-board-v2-qua-usb">4. Kết nối BeE Board V2 qua USB</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cau-hinh-ket-noi-qua-wi-fi-ota">5. Cấu hình kết nối qua Wi-Fi (OTA)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#kiem-tra-hoat-dong-cua-board">6. Kiểm tra hoạt động của board</a></li>
<li class="toctree-l2"><a class="reference internal" href="#su-co-thuong-gap">7. Sự cố thường gặp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#buoc-tiep-theo">8. Bước tiếp theo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Cài đặt</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/2.bee-ide/2.installation.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="cai-dat">
<h1>Cài đặt<a class="headerlink" href="#cai-dat" title="Link to this heading"></a></h1>
<hr class="docutils" />
<section id="muc-tieu">
<h2>Mục tiêu<a class="headerlink" href="#muc-tieu" title="Link to this heading"></a></h2>
<p>Hướng dẫn bạn <strong>chuẩn bị máy tính và kết nối BeE Board V2</strong> với nền tảng lập trình trực tuyến <strong>BeE IDE</strong> và <strong>BeE Python</strong>.
Sau khi hoàn tất, bạn có thể lập trình và nạp code cho BeE Board trực tiếp qua trình duyệt.</p>
</section>
<hr class="docutils" />
<section id="yeu-cau-he-thong">
<h2>1. Yêu cầu hệ thống<a class="headerlink" href="#yeu-cau-he-thong" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Yêu cầu</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Hệ điều hành</strong></p></td>
<td><p>Windows 10/11, macOS, Linux, Chromebook</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Trình duyệt</strong></p></td>
<td><p>Chrome / Edge (phiên bản mới nhất)</p></td>
</tr>
<tr class="row-even"><td><p><strong>Kết nối Internet</strong></p></td>
<td><p>Bắt buộc (để truy cập BeE IDE và BeE Python)</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Cáp USB-C</strong></p></td>
<td><p>Hỗ trợ truyền dữ liệu (4 dây), không chỉ sạc</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>💡 Bạn <strong>không cần cài đặt phần mềm</strong>, vì <strong>BeE IDE</strong> và <strong>BeE Python</strong> hoạt động trực tiếp trên web.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="cai-dat-driver-ch340c">
<h2>2. Cài đặt driver CH340C<a class="headerlink" href="#cai-dat-driver-ch340c" title="Link to this heading"></a></h2>
<p>BeE Board V2 sử dụng <strong>chip CH340C</strong> để giao tiếp USB với máy tính.
Lần đầu kết nối, bạn cần cài <strong>driver CH340C</strong> để máy tính nhận cổng COM.</p>
<section id="windows">
<h3>Windows<a class="headerlink" href="#windows" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Tải driver và cài đặt theo hướng dẫn tại <a class="reference external" href="https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board">đây</a></p></li>
<li><p>Mở file <code class="docutils literal notranslate"><span class="pre">.exe</span></code> và nhấn <strong>INSTALL</strong></p></li>
<li><p>Sau khi cài đặt, rút và cắm lại BeE Board</p></li>
<li><p>Mở <strong>Device Manager → Ports (COM &amp; LPT)</strong>, bạn sẽ thấy cổng như <code class="docutils literal notranslate"><span class="pre">CH340</span> <span class="pre">(COM3)</span></code></p></li>
</ol>
</section>
<hr class="docutils" />
<section id="macos-linux">
<h3>MacOS &amp; Linux<a class="headerlink" href="#macos-linux" title="Link to this heading"></a></h3>
<p>Trên hầu hết các bản phân phối của MacOS và Linux, CH340C đã có sẵn driver.
Nếu không nhận cổng, kiểm tra bằng:</p>
<div class="highlight-bash notranslate"><div class="highlight"><pre><span></span>dmesg<span class="w"> </span><span class="p">|</span><span class="w"> </span>grep<span class="w"> </span>ttyUSB
</pre></div>
</div>
</section>
</section>
<hr class="docutils" />
<section id="truy-cap-nen-tang-lap-trinh">
<h2>3. Truy cập nền tảng lập trình<a class="headerlink" href="#truy-cap-nen-tang-lap-trinh" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nền tảng</p></th>
<th class="head"><p>Mục đích</p></th>
<th class="head"><p>Liên kết</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>BeE IDE</strong></p></td>
<td><p>Lập trình kéo thả Blockly</p></td>
<td><p><a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p></td>
</tr>
<tr class="row-odd"><td><p><strong>BeE Python</strong></p></td>
<td><p>Lập trình bằng ngôn ngữ Python</p></td>
<td><p><a class="reference external" href="https://beestemsolutions.com.vn/studio/python">https://beestemsolutions.com.vn/studio/python</a></p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>Cả hai nền tảng hoạt động hoàn toàn <strong>trực tuyến</strong>, không cần tải về.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="ket-noi-bee-board-v2-qua-usb">
<h2>4. Kết nối BeE Board V2 qua USB<a class="headerlink" href="#ket-noi-bee-board-v2-qua-usb" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p>Cắm cáp USB-C vào BeE Board và máy tính</p></li>
<li><p>Mở BeE IDE hoặc BeE Python</p></li>
<li><p>Nhấn nút <strong>Kết nối → Chọn thiết bị USB</strong></p></li>
<li><p>Chọn dòng có chữ <code class="docutils literal notranslate"><span class="pre">cu.usbmodemxxxx</span></code> (MacOS) hoặc <code class="docutils literal notranslate"><span class="pre">COMx</span></code> (Windows) hoặc <code class="docutils literal notranslate"><span class="pre">/dev/ttyUSBx</span></code> (Linux)</p></li>
<li><p>Khi kết nối thành công, biểu tượng ▶️ sẽ sáng lên</p></li>
</ol>
</section>
<hr class="docutils" />
<section id="cau-hinh-ket-noi-qua-wi-fi-ota">
<h2>5. Cấu hình kết nối qua Wi-Fi (OTA)<a class="headerlink" href="#cau-hinh-ket-noi-qua-wi-fi-ota" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p>Trong Blockly, kéo khối <strong>“Setup OTA Wi-Fi”</strong></p></li>
<li><p>Nhập <strong>tên mạng (SSID)</strong> và <strong>mật khẩu</strong></p></li>
<li><p>Khi board kết nối thành công, màn hình OLED hiển thị <strong>địa chỉ IP</strong></p></li>
<li><p>Trong BeE IDE hoặc BeE Python → chọn <strong>Upload OTA</strong> → nhập IP để nạp code không dây</p></li>
</ol>
</section>
<hr class="docutils" />
<section id="kiem-tra-hoat-dong-cua-board">
<h2>6. Kiểm tra hoạt động của board<a class="headerlink" href="#kiem-tra-hoat-dong-cua-board" title="Link to this heading"></a></h2>
<p>Dán đoạn code sau trong <strong>BeE Python</strong> để kiểm tra nhanh:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;BeE Ready!&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">TONES</span><span class="p">[</span><span class="s1">&#39;C5&#39;</span><span class="p">])</span>
<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.3</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">be_quiet</span><span class="p">()</span>
</pre></div>
</div>
<blockquote>
<div><p>Nếu LED sáng xanh + OLED hiển thị chữ “BeE Ready!” + nghe tiếng “bíp” → board hoạt động bình thường.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="su-co-thuong-gap">
<h2>7. Sự cố thường gặp<a class="headerlink" href="#su-co-thuong-gap" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Vấn đề</p></th>
<th class="head"><p>Nguyên nhân</p></th>
<th class="head"><p>Cách khắc phục</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Không thấy cổng USB</p></td>
<td><p>Chưa cài driver CH340C</p></td>
<td><p>Cài lại driver từ WCH</p></td>
</tr>
<tr class="row-odd"><td><p>Board không sáng đèn nguồn</p></td>
<td><p>Cáp chỉ sạc hoặc nguồn yếu</p></td>
<td><p>Dùng cáp dữ liệu USB-C chất lượng</p></td>
</tr>
<tr class="row-even"><td><p>Không kết nối được IDE</p></td>
<td><p>Trình duyệt chưa cấp quyền USB</p></td>
<td><p>Chọn “Cho phép” khi trình duyệt hỏi</p></td>
</tr>
<tr class="row-odd"><td><p>Không nạp được code OTA</p></td>
<td><p>Board và máy tính khác mạng Wi-Fi</p></td>
<td><p>Đảm bảo cùng mạng LAN</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="buoc-tiep-theo">
<h2>8. Bước tiếp theo<a class="headerlink" href="#buoc-tiep-theo" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="#1.bee-board-v2/2.getting-started.md"><span class="xref myst">Bắt đầu với BeE Board V2</span></a></p></li>
<li><p><a class="reference internal" href="#2.bee-ide/1.index.md"><span class="xref myst">Lập trình với BeE IDE</span></a></p></li>
<li><p><a class="reference internal" href="#4.extensions/1.index.md"><span class="xref myst">Cài đặt module mở rộng</span></a></p></li>
<li><p><a class="reference internal" href="#1.bee-board-v2/3.hardware-overview.md"><span class="xref myst">Giới thiệu phần cứng BeE Board V2</span></a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../3.examples/8.imu-example.html" class="btn btn-neutral float-left" title="Cảm biến IMU" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="3.user-interface.html" class="btn btn-neutral float-right" title="Giao diện" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>