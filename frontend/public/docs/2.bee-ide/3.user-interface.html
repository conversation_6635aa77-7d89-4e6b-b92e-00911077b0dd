

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Giao <PERSON> &mdash; <PERSON><PERSON>i <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Nạp chương trình" href="4.flashing-guide.html" />
    <link rel="prev" title="Cài đặt" href="2.installation.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="2.installation.html">Cài đặt</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Giao diện</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#muc-tieu">Mục tiêu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tong-quan-giao-dien">Tổng quan giao diện</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thanh-cong-cu-toolbar">1. Thanh công cụ (Toolbar)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#danh-muc-khoi-lenh-toolbox">2. Danh mục khối lệnh (Toolbox)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#khu-vuc-lam-viec-workspace">3. Khu vực làm việc (Workspace)</a></li>
<li class="toctree-l2"><a class="reference internal" href="#bang-log">4. Bảng Log</a></li>
<li class="toctree-l2"><a class="reference internal" href="#giao-dien-tuy-chinh">5. Giao diện tùy chỉnh</a></li>
<li class="toctree-l2"><a class="reference internal" href="#bee-assistant">6. BeE Assistant</a></li>
<li class="toctree-l2"><a class="reference internal" href="#meo-su-dung-hieu-qua">7. Mẹo sử dụng hiệu quả</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-lieu-lien-quan">8. Tài liệu liên quan</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Giao diện</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/2.bee-ide/3.user-interface.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="giao-dien">
<h1>Giao diện<a class="headerlink" href="#giao-dien" title="Link to this heading"></a></h1>
<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE UI Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<hr class="docutils" />
<section id="muc-tieu">
<h2>Mục tiêu<a class="headerlink" href="#muc-tieu" title="Link to this heading"></a></h2>
<p>Hiểu rõ <strong>bố cục giao diện</strong> của BeE IDE để học sinh và giáo viên có thể <strong>sử dụng hiệu quả các công cụ lập trình kéo thả</strong>.</p>
</section>
<hr class="docutils" />
<section id="tong-quan-giao-dien">
<h2>Tổng quan giao diện<a class="headerlink" href="#tong-quan-giao-dien" title="Link to this heading"></a></h2>
<p align="center">
    <img 
    src="../_static/bee-ide/bee-ide.png" 
    alt="BeE IDE Layout" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"/>
</p>
<p>BeE IDE được chia thành <strong>6 khu vực chính</strong>:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Khu vực</p></th>
<th class="head"><p>Tên</p></th>
<th class="head"><p>Chức năng</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>① Thanh công cụ</p></td>
<td><p>Tạo mới, mở, lưu, nạp code</p></td>
<td><p>Điều khiển chung</p></td>
</tr>
<tr class="row-odd"><td><p>② Danh mục khối</p></td>
<td><p>Chứa các nhóm lệnh (LED, Button, Motor, OLED…)</p></td>
<td><p>Kéo thả lệnh từ đây</p></td>
</tr>
<tr class="row-even"><td><p>③ Vùng làm việc</p></td>
<td><p>Nơi ghép nối các khối lệnh</p></td>
<td><p>Xây dựng chương trình</p></td>
</tr>
<tr class="row-odd"><td><p>④ Khu điều khiển</p></td>
<td><p>Nút <strong>Upload</strong></p></td>
<td><p>Nạp và chạy code trên BeE Board</p></td>
</tr>
<tr class="row-even"><td><p>⑤ Log</p></td>
<td><p>Hiển thị thông báo</p></td>
<td><p>Kết quả và thông báo lỗi</p></td>
</tr>
<tr class="row-odd"><td><p>⑥ BeE Assistant</p></td>
<td><p>Trợ lý AI giúp lập trình</p></td>
<td><p>Hỗ trợ giải thích các khối lệnh</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="thanh-cong-cu-toolbar">
<h2>1. Thanh công cụ (Toolbar)<a class="headerlink" href="#thanh-cong-cu-toolbar" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-ide/toolbar.png" 
    alt="Toolbar" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nút</p></th>
<th class="head"><p>Biểu tượng</p></th>
<th class="head"><p>Chức năng</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Project Name</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/project-name.png"><img alt="../_images/project-name.png" src="../_images/project-name.png" style="width: 100px;" /></a></p></p></td>
<td><p>Chọn tên cho dự án của bạn</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Connect Serial</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/connect-serial.png"><img alt="../_images/connect-serial.png" src="../_images/connect-serial.png" style="width: 20px;" /></a></p></p></td>
<td><p>Chọn cổng USB</p></td>
</tr>
<tr class="row-even"><td><p><strong>Connect Bluetooth</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/connect-bluetooth.png"><img alt="../_images/connect-bluetooth.png" src="../_images/connect-bluetooth.png" style="width: 20px;" /></a></p></p></td>
<td><p>Chọn thiết bị Bluetooth</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Save</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/save.png"><img alt="../_images/save.png" src="../_images/save.png" style="width: 20px;" /></a></p></p></td>
<td><p>Lưu trong trình duyệt</p></td>
</tr>
<tr class="row-even"><td><p><strong>Load</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/load.png"><img alt="../_images/load.png" src="../_images/load.png" style="width: 20px;" /></a></p></p></td>
<td><p>Tải lại dự án đã lưu</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="danh-muc-khoi-lenh-toolbox">
<h2>2. Danh mục khối lệnh (Toolbox)<a class="headerlink" href="#danh-muc-khoi-lenh-toolbox" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-ide/toolbox.png" 
    alt="Toolbox" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<p>Các khối lệnh trong BeE IDE được chia theo <strong>nhóm chức năng</strong>:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nhóm</p></th>
<th class="head"><p>Biểu tượng</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>LED</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/led.png"><img alt="../_images/led.png" src="../_images/led.png" style="width: 20px;" /></a></p></p></td>
<td><p>Bật/tắt, đổi màu LED</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Button</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/button.png"><img alt="../_images/button.png" src="../_images/button.png" style="width: 20px;" /></a></p></p></td>
<td><p>Kiểm tra nút A/B được nhấn</p></td>
</tr>
<tr class="row-even"><td><p><strong>Buzzer</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/buzzer.png"><img alt="../_images/buzzer.png" src="../_images/buzzer.png" style="width: 20px;" /></a></p></p></td>
<td><p>Phát nốt nhạc hoặc âm thanh</p></td>
</tr>
<tr class="row-odd"><td><p><strong>OLED</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/display.png"><img alt="../_images/display.png" src="../_images/display.png" style="width: 20px;" /></a></p></p></td>
<td><p>Hiển thị văn bản, cảm biến</p></td>
</tr>
<tr class="row-even"><td><p><strong>Motor</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/motor.png"><img alt="../_images/motor.png" src="../_images/motor.png" style="width: 20px;" /></a></p></p></td>
<td><p>Điều khiển servo, motor</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Servo</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/servo.png"><img alt="../_images/servo.png" src="../_images/servo.png" style="width: 20px;" /></a></p></p></td>
<td><p>Điều khiển servo, motor</p></td>
</tr>
<tr class="row-even"><td><p><strong>IMU</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/imu.png"><img alt="../_images/imu.png" src="../_images/imu.png" style="width: 20px;" /></a></p></p></td>
<td><p>Đọc góc nghiêng, phát hiện lắc</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Digital Read</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/digital-read.png"><img alt="../_images/digital-read.png" src="../_images/digital-read.png" style="width: 20px;" /></a></p></p></td>
<td><p>Đọc tín hiệu digital</p></td>
</tr>
<tr class="row-even"><td><p><strong>Analog Read</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/analog-read.png"><img alt="../_images/analog-read.png" src="../_images/analog-read.png" style="width: 20px;" /></a></p></p></td>
<td><p>Đọc tín hiệu analog</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Digital Write</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/digital-write.png"><img alt="../_images/digital-write.png" src="../_images/digital-write.png" style="width: 20px;" /></a></p></p></td>
<td><p>Ghi tín hiệu digital</p></td>
</tr>
<tr class="row-even"><td><p><strong>Advanced / OTA</strong></p></td>
<td><p><p align="center"><a class="reference internal" href="../_images/ota.png"><img alt="../_images/ota.png" src="../_images/ota.png" style="width: 20px;" /></a></p></p></td>
<td><p>Setup Wi-Fi, OTA, Reset</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="khu-vuc-lam-viec-workspace">
<h2>3. Khu vực làm việc (Workspace)<a class="headerlink" href="#khu-vuc-lam-viec-workspace" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-ide/workspace.png" 
    alt="Workspace" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<ul class="simple">
<li><p>Kéo thả các khối từ Toolbox vào Workspace.</p></li>
<li><p>Các khối tự động <strong>khớp (snap)</strong> với nhau theo logic.</p></li>
<li><p>Sử dụng chuột cuộn để phóng to/thu nhỏ.</p></li>
<li><p>Nhấn <strong>Ctrl + A</strong> để chọn tất cả khối, <strong>Delete</strong> để xóa.</p></li>
</ul>
<blockquote>
<div><p>Mỗi chương trình sẽ có <strong>một khối bắt đầu chính</strong> (ví dụ: “Khi bắt đầu chạy chương trình”).</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="bang-log">
<h2>4. Bảng Log<a class="headerlink" href="#bang-log" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-ide/log.png" 
    alt="Log" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<p>Bảng này hiển thị:</p>
<ul class="simple">
<li><p>Thông báo khi nạp code thành công</p></li>
<li><p>Cảnh báo hoặc lỗi khi kết nối không thành công</p></li>
</ul>
<blockquote>
<div><p>Gợi ý: Khi gặp lỗi “Connection failed”, hãy kiểm tra lại <strong>cáp USB hoặc IP Wi-Fi</strong>.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="giao-dien-tuy-chinh">
<h2>5. Giao diện tùy chỉnh<a class="headerlink" href="#giao-dien-tuy-chinh" title="Link to this heading"></a></h2>
<p>BeE IDE hỗ trợ <strong>ngôn ngữ tiếng Việt / tiếng Anh</strong> tự động nhận theo trình duyệt.</p>
<p align="center">
  <img 
    src="../_static/bee-ide/language.png" 
    alt="Theme Switch" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Tùy chọn</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>🇻🇳 / 🇬🇧 <strong>Ngôn ngữ</strong></p></td>
<td><p>Tự động chuyển theo ngôn ngữ trình duyệt</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="bee-assistant">
<h2>6. BeE Assistant<a class="headerlink" href="#bee-assistant" title="Link to this heading"></a></h2>
<p align="center">
  <img 
    src="../_static/bee-ide/assistant.png" 
    alt="Assistant" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<p>BeE Assistant là trợ lý AI tích hợp sẵn trong BeE IDE, giúp học sinh và giáo viên <strong>học hỏi và giải đáp thắc mắc</strong>.</p>
</section>
<section id="meo-su-dung-hieu-qua">
<h2>7. Mẹo sử dụng hiệu quả<a class="headerlink" href="#meo-su-dung-hieu-qua" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p>Nhấn <strong>Ctrl + Z / Ctrl + Y</strong> để hoàn tác hoặc khôi phục thao tác</p></li>
<li><p>Sử dụng <strong>khối “ghi chú” (comment)</strong> để giải thích từng phần chương trình</p></li>
<li><p>Thường xuyên <strong>lưu dự án (.bee)</strong> sau mỗi bài học</p></li>
<li><p>Dùng <strong>khối “chờ 0.5 giây”</strong> để làm mượt hiệu ứng LED, motor, buzzer</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="tai-lieu-lien-quan">
<h2>8. Tài liệu liên quan<a class="headerlink" href="#tai-lieu-lien-quan" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html"><span class="std std-doc">Bắt đầu với BeE Board V2</span></a></p></li>
<li><p><a class="reference internal" href="2.installation.html"><span class="std std-doc">Cài đặt và kết nối BeE Board</span></a></p></li>
<li><p><a class="reference internal" href="../4.extensions/1.index.html"><span class="std std-doc">Cài đặt module mở rộng</span></a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="2.installation.html" class="btn btn-neutral float-left" title="Cài đặt" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="4.flashing-guide.html" class="btn btn-neutral float-right" title="Nạp chương trình" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>