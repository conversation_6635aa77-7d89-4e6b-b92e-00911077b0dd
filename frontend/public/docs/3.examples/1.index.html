

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Ví dụ lập trình &mdash; <PERSON><PERSON>i <PERSON> BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Giới thiệu" href="../4.extensions/1.index.html" />
    <link rel="prev" title="Nạp Firmware" href="../2.bee-ide/5.flashing-image.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Ví dụ lập trình</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cac-module-tich-hop">Các module tích hợp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-de-dang-voi-bee-ide">Lập trình dễ dàng với BeE IDE</a></li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-nang-cao-voi-bee-python">Lập trình nâng cao với BeE Python</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cac-vi-du-thuc-hanh">Các ví dụ thực hành</a><ul>
<li class="toctree-l3"><a class="reference internal" href="2.led-example.html">LED RGB</a></li>
<li class="toctree-l3"><a class="reference internal" href="3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l3"><a class="reference internal" href="4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l3"><a class="reference internal" href="5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l3"><a class="reference internal" href="6.motor-example.html">Động cơ</a></li>
<li class="toctree-l3"><a class="reference internal" href="7.servo-example.html">Servo</a></li>
<li class="toctree-l3"><a class="reference internal" href="8.imu-example.html">Cảm biến IMU</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#goi-y-mo-rong">Gợi ý mở rộng</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Ví dụ lập trình</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/3.examples/1.index.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="vi-du-lap-trinh">
<h1>Ví dụ lập trình<a class="headerlink" href="#vi-du-lap-trinh" title="Link to this heading"></a></h1>
<hr class="docutils" />
<section id="gioi-thieu">
<h2>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p>BeE Board V2 tích hợp nhiều <strong>module phần cứng thông minh</strong> giúp học sinh làm quen với lập trình robot và IoT.
Dưới đây là danh sách các module <strong>onboard</strong> mà bạn có thể lập trình ngay bằng <strong>BeE IDE</strong> hoặc <strong>BeE Python</strong>.</p>
</section>
<hr class="docutils" />
<section id="cac-module-tich-hop">
<h2>Các module tích hợp<a class="headerlink" href="#cac-module-tich-hop" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Module</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Đối tượng Python</p></th>
<th class="head"><p>Ví dụ thực hành</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>LED RGB</strong></p></td>
<td><p>2 đèn RGB tích hợp, đổi được 16 triệu màu</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.led1</span></code>, <code class="docutils literal notranslate"><span class="pre">bee.led2</span></code></p></td>
<td><p><a class="reference internal" href="2.led-example.html"><span class="std std-doc">LED Example</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><strong>Nút nhấn A/B</strong></p></td>
<td><p>Nhận tín hiệu từ người dùng</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.buttonA</span></code>, <code class="docutils literal notranslate"><span class="pre">bee.buttonB</span></code></p></td>
<td><p><a class="reference internal" href="3.button-example.html"><span class="std std-doc">Button Example</span></a></p></td>
</tr>
<tr class="row-even"><td><p><strong>Buzzer</strong></p></td>
<td><p>Phát âm thanh, nhạc, cảnh báo</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.buzzer</span></code></p></td>
<td><p><a class="reference internal" href="4.buzzer-example.html"><span class="std std-doc">Buzzer Example</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><strong>OLED 0.96”</strong></p></td>
<td><p>Hiển thị văn bản và thông tin cảm biến</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.oled</span></code></p></td>
<td><p><a class="reference internal" href="5.oled-example.html"><span class="std std-doc">OLED Example</span></a></p></td>
</tr>
<tr class="row-even"><td><p><strong>Động cơ DC (M1–M2)</strong></p></td>
<td><p>Điều khiển robot di chuyển</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.motor1</span></code>, <code class="docutils literal notranslate"><span class="pre">bee.motor2</span></code></p></td>
<td><p><a class="reference internal" href="6.motor-example.html"><span class="std std-doc">Motor Example</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><strong>Servo (S1–S4)</strong></p></td>
<td><p>Điều khiển servo RC qua PCA9685</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.servo1–4</span></code></p></td>
<td><p><a class="reference internal" href="7.servo-example.html"><span class="std std-doc">Servo Example</span></a></p></td>
</tr>
<tr class="row-even"><td><p><strong>Cảm biến IMU MPU6050</strong></p></td>
<td><p>Đo nghiêng, lắc, phát hiện chuyển động</p></td>
<td><p><code class="docutils literal notranslate"><span class="pre">bee.imu</span></code></p></td>
<td><p><a class="reference internal" href="8.imu-example.html"><span class="std std-doc">IMU Example</span></a></p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="lap-trinh-de-dang-voi-bee-ide">
<h2>Lập trình dễ dàng với BeE IDE<a class="headerlink" href="#lap-trinh-de-dang-voi-bee-ide" title="Link to this heading"></a></h2>
<p>Truy cập BeE IDE để lập trình trực quan bằng khối kéo thả:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
<p align="center">
<img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<p>Các nhóm khối lệnh tương ứng:</p>
<ul class="simple">
<li><p><strong>LED / RGB</strong> – điều khiển màu sắc</p></li>
<li><p><strong>Button / Input</strong> – xử lý nút nhấn</p></li>
<li><p><strong>Digital Read / Analog Read</strong> - đọc tín hiệu từ cảm biến</p></li>
<li><p><strong>Digital Write / Analog Write</strong> - ghi tín hiệu vào module</p></li>
<li><p><strong>Sound / Buzzer</strong> – phát âm thanh</p></li>
<li><p><strong>Display / OLED</strong> – hiển thị thông tin</p></li>
<li><p><strong>Motion / Motor, Servo, IMU</strong> – điều khiển robot</p></li>
<li><p><strong>Advanced</strong> – OTA, Bluetooth, Reset, Kết nối</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="lap-trinh-nang-cao-voi-bee-python">
<h2>Lập trình nâng cao với BeE Python<a class="headerlink" href="#lap-trinh-nang-cao-voi-bee-python" title="Link to this heading"></a></h2>
<p>Truy cập BeE Python để viết code thực tế:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/python">https://beestemsolutions.com.vn/studio/python</a></p>
<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-python.png" 
    alt="BeE Python Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<p>BeE Python hỗ trợ đầy đủ thư viện <code class="docutils literal notranslate"><span class="pre">BeeBrain</span></code>, cho phép điều khiển tất cả module onboard chỉ với vài dòng code.</p>
</section>
<hr class="docutils" />
<section id="cac-vi-du-thuc-hanh">
<h2>Các ví dụ thực hành<a class="headerlink" href="#cac-vi-du-thuc-hanh" title="Link to this heading"></a></h2>
<div class="toctree-wrapper compound">
<p class="caption" role="heading"><span class="caption-text">💡 Các ví dụ cơ bản</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="8.imu-example.html">Cảm biến IMU</a></li>
</ul>
</div>
</section>
<hr class="docutils" />
<section id="goi-y-mo-rong">
<h2>Gợi ý mở rộng<a class="headerlink" href="#goi-y-mo-rong" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Dự án</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Kết hợp module</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Đèn giao thông mini</p></td>
<td><p>LED đổi màu theo thời gian</p></td>
<td><p>LED + Buzzer</p></td>
</tr>
<tr class="row-odd"><td><p>Robot né vật cản</p></td>
<td><p>Tự quay khi rung hoặc nghiêng</p></td>
<td><p>IMU + Motor</p></td>
</tr>
<tr class="row-even"><td><p>Piano mini</p></td>
<td><p>Nhấn nút phát nhạc khác nhau</p></td>
<td><p>Button + Buzzer</p></td>
</tr>
<tr class="row-odd"><td><p>Hiển thị nhiệt độ</p></td>
<td><p>Đọc DHT11 và hiển thị OLED</p></td>
<td><p>DHT11 + OLED</p></td>
</tr>
<tr class="row-even"><td><p>Servo chỉ hướng</p></td>
<td><p>Servo xoay theo độ nghiêng</p></td>
<td><p>IMU + Servo</p></td>
</tr>
</tbody>
</table>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../2.bee-ide/5.flashing-image.html" class="btn btn-neutral float-left" title="Nạp Firmware" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../4.extensions/1.index.html" class="btn btn-neutral float-right" title="Giới thiệu" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>