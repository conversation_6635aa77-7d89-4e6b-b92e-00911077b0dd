

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Nút nhấn &mdash; Tài <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Buzzer" href="4.buzzer-example.html" />
    <link rel="prev" title="LED RGB" href="2.led-example.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="2.led-example.html">LED RGB</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Nút nhấn</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#muc-tieu">Mục tiêu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#phan-cung-can-co">Phần cứng cần có</a></li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-ide">1. Lập trình với BeE IDE</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#bat-led-khi-nhan-nut-a">Bật LED khi nhấn nút A</a></li>
<li class="toctree-l3"><a class="reference internal" href="#phat-nhac-khi-nhan-nut-b">Phát nhạc khi nhấn nút B</a></li>
<li class="toctree-l3"><a class="reference internal" href="#ket-hop-a-b">Kết hợp A &amp; B</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-python">2. Lập trình với BeE Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id1">Bật LED khi nhấn nút A</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id2">Phát nhạc khi nhấn nút B</a></li>
<li class="toctree-l3"><a class="reference internal" href="#ket-hop-ca-hai-nut">Kết hợp cả hai nút</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#thao-luan-va-mo-rong">3. Thảo luận và mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ket-luan">4. Kết luận</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../2.bee-ide/1.index.html">BeE IDE</a></li>
      <li class="breadcrumb-item active">Nút nhấn</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/3.examples/3.button-example.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="nut-nhan">
<h1>Nút nhấn<a class="headerlink" href="#nut-nhan" title="Link to this heading"></a></h1>
<p><img alt="Button Example Cover" src="3.examples/_static/bee-board-v2/examples/button-example-cover.jpg" /></p>
<hr class="docutils" />
<section id="muc-tieu">
<h2>Mục tiêu<a class="headerlink" href="#muc-tieu" title="Link to this heading"></a></h2>
<p>Trong bài học này, bạn sẽ học cách:</p>
<ul class="simple">
<li><p>Phát hiện khi nút nhấn <strong>A hoặc B</strong> được bấm</p></li>
<li><p>Kết hợp nút nhấn để <strong>điều khiển LED và Buzzer</strong></p></li>
<li><p>Lập trình hành vi phản ứng khi người dùng tương tác với board</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="phan-cung-can-co">
<h2>Phần cứng cần có<a class="headerlink" href="#phan-cung-can-co" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Số lượng</p></th>
<th class="head"><p>Ghi chú</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>BeE Board V2</p></td>
<td><p>1</p></td>
<td><p>Bo mạch chính có sẵn nút A và B</p></td>
</tr>
<tr class="row-odd"><td><p>Cáp USB-C</p></td>
<td><p>1</p></td>
<td><p>Kết nối với máy tính</p></td>
</tr>
<tr class="row-even"><td><p>Trình duyệt web</p></td>
<td><p>1</p></td>
<td><p>Chrome, Edge hoặc Safari</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>💡 Nút <strong>A</strong> và <strong>B</strong> được kết nối với GPIO nội bộ,
bạn có thể truy cập qua <code class="docutils literal notranslate"><span class="pre">bee.buttonA</span></code> và <code class="docutils literal notranslate"><span class="pre">bee.buttonB</span></code>.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-ide">
<h2>1. Lập trình với BeE IDE<a class="headerlink" href="#lap-trinh-voi-bee-ide" title="Link to this heading"></a></h2>
<p>Truy cập BeE IDE:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
<section id="bat-led-khi-nhan-nut-a">
<h3>Bật LED khi nhấn nút A<a class="headerlink" href="#bat-led-khi-nhan-nut-a" title="Link to this heading"></a></h3>
<p>Kéo các khối:</p>
<ol class="arabic simple">
<li><p>“Nếu nút A được nhấn”</p></li>
<li><p>“Bật LED 1 màu xanh lá cây”</p></li>
<li><p>“Nếu không thì tắt LED 1”</p></li>
</ol>
<p><img alt="Blockly Button LED" src="3.examples/_static/bee-ide/examples/button-led-blockly.jpg" /></p>
<p>Khi bạn nhấn nút <strong>A</strong>, LED 1 sáng lên
Khi thả tay, LED tắt.</p>
</section>
<hr class="docutils" />
<section id="phat-nhac-khi-nhan-nut-b">
<h3>Phát nhạc khi nhấn nút B<a class="headerlink" href="#phat-nhac-khi-nhan-nut-b" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>“Nếu nút B được nhấn”</p></li>
<li><p>“Phát nốt C5 trong 1/2 giây”</p></li>
<li><p>“Nếu không thì tắt loa buzzer”</p></li>
</ol>
<p><img alt="Blockly Button Buzzer" src="3.examples/_static/bee-ide/examples/button-buzzer-blockly.jpg" /></p>
<p>Khi nhấn nút <strong>B</strong>, bạn sẽ nghe tiếng “bíp”.</p>
</section>
<hr class="docutils" />
<section id="ket-hop-a-b">
<h3>Kết hợp A &amp; B<a class="headerlink" href="#ket-hop-a-b" title="Link to this heading"></a></h3>
<p>Bạn có thể tạo chương trình như:</p>
<ul class="simple">
<li><p>A bật LED</p></li>
<li><p>B phát nhạc</p></li>
<li><p>A + B cùng nhấn thì đổi màu LED</p></li>
</ul>
<p><img alt="Blockly Button Combo" src="3.examples/_static/bee-ide/examples/button-combo-blockly.jpg" /></p>
</section>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-python">
<h2>2. Lập trình với BeE Python<a class="headerlink" href="#lap-trinh-voi-bee-python" title="Link to this heading"></a></h2>
<p>Truy cập BeE Python:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/python">https://beestemsolutions.com.vn/studio/python</a></p>
<section id="id1">
<h3>Bật LED khi nhấn nút A<a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">buttonA</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">():</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">off</span><span class="p">()</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>
</pre></div>
</div>
<p>Nhấn nút <strong>A</strong> → LED1 sáng màu xanh lá.
Thả ra → LED tắt.</p>
</section>
<hr class="docutils" />
<section id="id2">
<h3>Phát nhạc khi nhấn nút B<a class="headerlink" href="#id2" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">buttonB</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">():</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">TONES</span><span class="p">[</span><span class="s1">&#39;C5&#39;</span><span class="p">])</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">be_quiet</span><span class="p">()</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>
</pre></div>
</div>
<p>Khi bạn nhấn <strong>B</strong>, buzzer phát nốt C5.</p>
</section>
<hr class="docutils" />
<section id="ket-hop-ca-hai-nut">
<h3>Kết hợp cả hai nút<a class="headerlink" href="#ket-hop-ca-hai-nut" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">buttonA</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">()</span> <span class="ow">and</span> <span class="n">bee</span><span class="o">.</span><span class="n">buttonB</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">():</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>
    <span class="k">elif</span> <span class="n">bee</span><span class="o">.</span><span class="n">buttonA</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">():</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="k">elif</span> <span class="n">bee</span><span class="o">.</span><span class="n">buttonB</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">():</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">off</span><span class="p">()</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>
</pre></div>
</div>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Nút nhấn</p></th>
<th class="head"><p>Hành động</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>A</p></td>
<td><p>LED xanh lá</p></td>
</tr>
<tr class="row-odd"><td><p>B</p></td>
<td><p>LED xanh dương</p></td>
</tr>
<tr class="row-even"><td><p>A + B</p></td>
<td><p>LED tím</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<hr class="docutils" />
<section id="thao-luan-va-mo-rong">
<h2>3. Thảo luận và mở rộng<a class="headerlink" href="#thao-luan-va-mo-rong" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Câu hỏi</p></th>
<th class="head"><p>Gợi ý</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Làm sao để LED sáng khi giữ nút A, và tắt khi thả?</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">if</span> <span class="pre">bee.buttonA.is_pressed()</span></code> trong vòng lặp</p></td>
</tr>
<tr class="row-odd"><td><p>Làm sao để phát nhạc theo giai điệu khi nhấn nút B?</p></td>
<td><p>Dùng danh sách nốt và lặp qua <code class="docutils literal notranslate"><span class="pre">bee.buzzer.play_tone()</span></code></p></td>
</tr>
<tr class="row-even"><td><p>Có thể tạo trò chơi bằng 2 nút này không?</p></td>
<td><p>Có! Bạn có thể làm game “phản xạ nhanh” với A và B</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="ket-luan">
<h2>4. Kết luận<a class="headerlink" href="#ket-luan" title="Link to this heading"></a></h2>
<p>Trong bài học này, bạn đã học cách:</p>
<ul class="simple">
<li><p>Sử dụng nút nhấn A và B</p></li>
<li><p>Tạo phản hồi bằng LED và Buzzer</p></li>
<li><p>Xây dựng chương trình tương tác người dùng</p></li>
</ul>
<blockquote>
<div><p>💡 Hãy thử thêm OLED để hiển thị thông điệp “Xin chào!” khi nhấn nút nhé</p>
</div></blockquote>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="2.led-example.html" class="btn btn-neutral float-left" title="LED RGB" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="4.buzzer-example.html" class="btn btn-neutral float-right" title="Buzzer" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>