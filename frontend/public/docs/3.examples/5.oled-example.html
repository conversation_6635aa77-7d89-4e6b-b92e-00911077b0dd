

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title><PERSON><PERSON>n <PERSON> &mdash; Tài Liệu BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Động cơ" href="6.motor-example.html" />
    <link rel="prev" title="Buzzer" href="4.buzzer-example.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Màn hình OLED</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#muc-tieu">Mục tiêu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#phan-cung-can-co">Phần cứng cần có</a></li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-ide">1. Lập trình với BeE IDE</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#hien-thi-dong-chu-hello-bee">Hiển thị dòng chữ “Hello BeE!”</a></li>
<li class="toctree-l3"><a class="reference internal" href="#hien-thi-nhieu-dong">Hiển thị nhiều dòng</a></li>
<li class="toctree-l3"><a class="reference internal" href="#hieu-ung-chu-chay">Hiệu ứng chữ chạy</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-python">2. Lập trình với BeE Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#hien-thi-chu-co-ban">Hiển thị chữ cơ bản</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id1">Hiển thị nhiều dòng</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id2">Hiệu ứng chữ chạy</a></li>
<li class="toctree-l3"><a class="reference internal" href="#hien-thi-cam-bien-imu">Hiển thị cảm biến IMU</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#thao-luan-va-mo-rong">3. Thảo luận và mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ket-luan">4. Kết luận</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../2.bee-ide/1.index.html">BeE IDE</a></li>
      <li class="breadcrumb-item active">Màn hình OLED</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/3.examples/5.oled-example.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="man-hinh-oled">
<h1>Màn hình OLED<a class="headerlink" href="#man-hinh-oled" title="Link to this heading"></a></h1>
<p><img alt="OLED Example Cover" src="3.examples/_static/bee-board-v2/examples/oled-example-cover.jpg" /></p>
<hr class="docutils" />
<section id="muc-tieu">
<h2>Mục tiêu<a class="headerlink" href="#muc-tieu" title="Link to this heading"></a></h2>
<p>Trong bài học này, bạn sẽ học cách:</p>
<ul class="simple">
<li><p>Hiển thị văn bản lên màn hình OLED</p></li>
<li><p>Xóa màn hình, in nhiều dòng</p></li>
<li><p>Tạo hiệu ứng chữ chạy (scroll) và kết hợp hiển thị dữ liệu cảm biến</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="phan-cung-can-co">
<h2>Phần cứng cần có<a class="headerlink" href="#phan-cung-can-co" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Số lượng</p></th>
<th class="head"><p>Ghi chú</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>BeE Board V2</p></td>
<td><p>1</p></td>
<td><p>Bo mạch chính có sẵn màn hình OLED</p></td>
</tr>
<tr class="row-odd"><td><p>Cáp USB-C</p></td>
<td><p>1</p></td>
<td><p>Kết nối với máy tính</p></td>
</tr>
<tr class="row-even"><td><p>Trình duyệt web</p></td>
<td><p>1</p></td>
<td><p>Chrome, Edge hoặc Safari</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>💡 Màn hình OLED 0.96&quot; của BeE Board V2 sử dụng giao tiếp I2C (địa chỉ <strong>0x3C</strong>).</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-ide">
<h2>1. Lập trình với BeE IDE<a class="headerlink" href="#lap-trinh-voi-bee-ide" title="Link to this heading"></a></h2>
<p>Truy cập BeE IDE:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
<section id="hien-thi-dong-chu-hello-bee">
<h3>Hiển thị dòng chữ “Hello BeE!”<a class="headerlink" href="#hien-thi-dong-chu-hello-bee" title="Link to this heading"></a></h3>
<p>Kéo các khối lệnh:</p>
<ol class="arabic simple">
<li><p>“Xóa màn hình OLED”</p></li>
<li><p>“Hiển thị chữ ‘Hello BeE!’ tại (0,0) với cỡ chữ 1”</p></li>
</ol>
<p><img alt="Blockly OLED Hello" src="3.examples/_static/bee-ide/examples/oled-hello-blockly.jpg" /></p>
<p>Khi nhấn <strong>Run ▶️</strong>, màn hình OLED sẽ hiển thị dòng chữ đầu tiên của bạn!</p>
</section>
<hr class="docutils" />
<section id="hien-thi-nhieu-dong">
<h3>Hiển thị nhiều dòng<a class="headerlink" href="#hien-thi-nhieu-dong" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>“Hiển thị chữ ‘Nhiệt độ:’ tại (0,0)”</p></li>
<li><p>“Hiển thị chữ ‘25°C’ tại (0,1)”</p></li>
<li><p>“Hiển thị chữ ‘Độ ẩm:’ tại (0,2)”</p></li>
<li><p>“Hiển thị chữ ‘60%’ tại (0,3)”</p></li>
</ol>
<p><img alt="Blockly OLED Multi-line" src="3.examples/_static/bee-ide/examples/oled-multiline-blockly.jpg" /></p>
<blockquote>
<div><p>Toạ độ <code class="docutils literal notranslate"><span class="pre">(x,</span> <span class="pre">y)</span></code> trong OLED là theo <strong>dòng (0–7)</strong> và <strong>cột ký tự</strong>.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="hieu-ung-chu-chay">
<h3>Hiệu ứng chữ chạy<a class="headerlink" href="#hieu-ung-chu-chay" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Dùng khối “xóa màn hình”</p></li>
<li><p>Trong vòng lặp “mãi mãi”, thay đổi vị trí hiển thị chữ theo trục X</p></li>
<li><p>Thêm khối “chờ 0.1 giây”</p></li>
</ol>
<p><img alt="Blockly OLED Scroll" src="3.examples/_static/bee-ide/examples/oled-scroll-blockly.jpg" /></p>
</section>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-python">
<h2>2. Lập trình với BeE Python<a class="headerlink" href="#lap-trinh-voi-bee-python" title="Link to this heading"></a></h2>
<p>Truy cập BeE Python:
👉 <a class="reference external" href="https://python.beestemsolutions.com.vn">https://python.beestemsolutions.com.vn</a></p>
<section id="hien-thi-chu-co-ban">
<h3>Hiển thị chữ cơ bản<a class="headerlink" href="#hien-thi-chu-co-ban" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>

<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Hello BeE!&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
<p>OLED hiển thị “Hello BeE!” ở góc trên bên trái của màn hình.</p>
</section>
<hr class="docutils" />
<section id="id1">
<h3>Hiển thị nhiều dòng<a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;BeE STEM Solutions&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;ESP32-S3 Board&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;AI - IoT - Robotics&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
<p>Khi chạy, OLED sẽ hiển thị 3 dòng thông tin như màn hình chào</p>
</section>
<hr class="docutils" />
<section id="id2">
<h3>Hiệu ứng chữ chạy<a class="headerlink" href="#id2" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">64</span><span class="p">,</span> <span class="mi">2</span><span class="p">):</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;BeE STEM&quot;</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>
</pre></div>
</div>
<p>Dòng chữ “BeE STEM” sẽ di chuyển sang phải mượt mà</p>
</section>
<hr class="docutils" />
<section id="hien-thi-cam-bien-imu">
<h3>Hiển thị cảm biến IMU<a class="headerlink" href="#hien-thi-cam-bien-imu" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
    <span class="n">roll</span> <span class="o">=</span> <span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">rollDeg</span>
    <span class="n">pitch</span> <span class="o">=</span> <span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">pitchDeg</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Roll: </span><span class="si">{</span><span class="n">roll</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Pitch: </span><span class="si">{</span><span class="n">pitch</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>
</pre></div>
</div>
<p>OLED hiển thị liên tục giá trị góc nghiêng của board theo thời gian</p>
</section>
</section>
<hr class="docutils" />
<section id="thao-luan-va-mo-rong">
<h2>3. Thảo luận và mở rộng<a class="headerlink" href="#thao-luan-va-mo-rong" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Câu hỏi</p></th>
<th class="head"><p>Gợi ý</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Làm sao để hiển thị cả nhiệt độ và độ ẩm?</p></td>
<td><p>Kết hợp cảm biến DHT11 qua PORT1</p></td>
</tr>
<tr class="row-odd"><td><p>Làm sao để tạo menu chọn bằng nút A/B?</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">bee.buttonA.is_pressed()</span></code> và thay đổi dòng OLED</p></td>
</tr>
<tr class="row-even"><td><p>Có thể hiển thị ký hiệu hoặc hình ảnh không?</p></td>
<td><p>Có thể, bằng hàm <code class="docutils literal notranslate"><span class="pre">bee.oled.image(&quot;icon&quot;)</span></code> (phiên bản sắp phát hành)</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="ket-luan">
<h2>4. Kết luận<a class="headerlink" href="#ket-luan" title="Link to this heading"></a></h2>
<p>Trong bài này, bạn đã học cách:</p>
<ul class="simple">
<li><p>Hiển thị chữ và nhiều dòng trên OLED</p></li>
<li><p>Xóa, cuộn và cập nhật nội dung liên tục</p></li>
<li><p>Ứng dụng hiển thị dữ liệu cảm biến thời gian thực</p></li>
</ul>
<blockquote>
<div><p>💡 Thử kết hợp OLED + IMU + LED để tạo “dashboard mini” ngay trên BeE Board nhé!</p>
</div></blockquote>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="4.buzzer-example.html" class="btn btn-neutral float-left" title="Buzzer" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="6.motor-example.html" class="btn btn-neutral float-right" title="Động cơ" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>