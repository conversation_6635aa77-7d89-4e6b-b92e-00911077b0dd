

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Động cơ &mdash; <PERSON><PERSON>i <PERSON> Be<PERSON> Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Servo" href="7.servo-example.html" />
    <link rel="prev" title="Màn hình OLED" href="5.oled-example.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Động cơ</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#muc-tieu">Mục tiêu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#phan-cung-can-co">Phần cứng cần có</a></li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-ide">1. Lập trình với BeE IDE</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#chay-robot-tien-2-giay">Chạy robot tiến 2 giây</a></li>
<li class="toctree-l3"><a class="reference internal" href="#quay-trai-va-quay-phai">Quay trái và quay phải</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dieu-khien-servo-xoay-goc">Điều khiển servo xoay góc</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-python">2. Lập trình với BeE Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#chay-robot-tien-dung">🔹 Chạy robot tiến &amp; dừng</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id1">Quay trái và quay phải</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dieu-khien-tung-motor-rieng">Điều khiển từng motor riêng</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dieu-khien-servo">Điều khiển servo</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#thao-luan-va-mo-rong">3. Thảo luận và mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ket-luan">4. Kết luận</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../2.bee-ide/1.index.html">BeE IDE</a></li>
      <li class="breadcrumb-item active">Động cơ</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/3.examples/6.motor-example.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="dong-co">
<h1>Động cơ<a class="headerlink" href="#dong-co" title="Link to this heading"></a></h1>
<p><img alt="Motor Example Cover" src="3.examples/_static/bee-board-v2/examples/motor-example-cover.jpg" /></p>
<hr class="docutils" />
<section id="muc-tieu">
<h2>Mục tiêu<a class="headerlink" href="#muc-tieu" title="Link to this heading"></a></h2>
<p>Trong bài học này, bạn sẽ học cách:</p>
<ul class="simple">
<li><p>Điều khiển <strong>động cơ DC</strong> chạy tiến, lùi, dừng</p></li>
<li><p>Điều khiển <strong>servo</strong> xoay góc chính xác</p></li>
<li><p>Tạo chương trình điều khiển robot di chuyển</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="phan-cung-can-co">
<h2>Phần cứng cần có<a class="headerlink" href="#phan-cung-can-co" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Số lượng</p></th>
<th class="head"><p>Ghi chú</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>BeE Board V2</p></td>
<td><p>1</p></td>
<td><p>Bo mạch chính</p></td>
</tr>
<tr class="row-odd"><td><p>Bộ khung LEGO / xe robot</p></td>
<td><p>1</p></td>
<td><p>Có thể gắn động cơ M1 &amp; M2</p></td>
</tr>
<tr class="row-even"><td><p>Cáp USB-C</p></td>
<td><p>1</p></td>
<td><p>Kết nối với máy tính</p></td>
</tr>
<tr class="row-odd"><td><p>Nguồn pin 5V–8.4V</p></td>
<td><p>1</p></td>
<td><p>Cấp cho motor hoạt động mạnh hơn</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>💡 BeE Board V2 có sẵn <strong>2 cổng điều khiển động cơ DC (M1, M2)</strong>
và <strong>4 cổng servo (S1–S4)</strong> được điều khiển qua I2C.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-ide">
<h2>1. Lập trình với BeE IDE<a class="headerlink" href="#lap-trinh-voi-bee-ide" title="Link to this heading"></a></h2>
<p>Truy cập BeE IDE:
👉 <a class="reference external" href="https://ide.beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
<section id="chay-robot-tien-2-giay">
<h3>Chạy robot tiến 2 giây<a class="headerlink" href="#chay-robot-tien-2-giay" title="Link to this heading"></a></h3>
<p>Kéo các khối lệnh sau:</p>
<ol class="arabic simple">
<li><p>“Robot tiến với tốc độ 60% trong 2 giây”</p></li>
<li><p>“Dừng robot”</p></li>
</ol>
<p><img alt="Blockly Move Forward" src="3.examples/_static/bee-ide/examples/motor-forward-blockly.jpg" /></p>
<p>Khi nhấn <strong>Run ▶️</strong>, hai động cơ M1 và M2 sẽ quay cùng chiều, giúp xe di chuyển thẳng.</p>
</section>
<hr class="docutils" />
<section id="quay-trai-va-quay-phai">
<h3>Quay trái và quay phải<a class="headerlink" href="#quay-trai-va-quay-phai" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p><strong>Quay trái:</strong> dùng “Robot quay trái 60% trong 1 giây”</p></li>
<li><p><strong>Quay phải:</strong> dùng “Robot quay phải 60% trong 1 giây”</p></li>
</ul>
<p><img alt="Blockly Turn Left" src="3.examples/_static/bee-ide/examples/motor-turn-blockly.jpg" /></p>
<p>Kết hợp với vòng lặp <code class="docutils literal notranslate"><span class="pre">mãi</span> <span class="pre">mãi</span></code>, bạn có thể tạo robot tuần tra theo hình vuông.</p>
</section>
<hr class="docutils" />
<section id="dieu-khien-servo-xoay-goc">
<h3>Điều khiển servo xoay góc<a class="headerlink" href="#dieu-khien-servo-xoay-goc" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Chọn khối “Servo số … xoay đến góc … độ”</p></li>
<li><p>Đặt góc lần lượt: 0°, 90°, 180°</p></li>
<li><p>Thêm khối “chờ 0.5 giây” giữa mỗi lần</p></li>
</ol>
<p><img alt="Blockly Servo Example" src="3.examples/_static/bee-ide/examples/servo-blockly.jpg" /></p>
<p>Khi chạy, servo sẽ xoay qua lại như robot đang vẫy tay</p>
</section>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-python">
<h2>2. Lập trình với BeE Python<a class="headerlink" href="#lap-trinh-voi-bee-python" title="Link to this heading"></a></h2>
<p>Truy cập BeE Python:
👉 <a class="reference external" href="https://python.beestemsolutions.com.vn/studio/python">https://beestemsolutions.com.vn/studio/python</a></p>
<section id="chay-robot-tien-dung">
<h3>🔹 Chạy robot tiến &amp; dừng<a class="headerlink" href="#chay-robot-tien-dung" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">bee</span><span class="o">.</span><span class="n">init_bee</span><span class="p">()</span>

<span class="n">bee</span><span class="o">.</span><span class="n">move_forward</span><span class="p">(</span><span class="mi">60</span><span class="p">,</span> <span class="mi">2</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
</pre></div>
</div>
<p>Xe robot sẽ chạy tiến với tốc độ 60% trong 2 giây, sau đó dừng.</p>
</section>
<hr class="docutils" />
<section id="id1">
<h3>Quay trái và quay phải<a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">bee</span><span class="o">.</span><span class="n">turn_left</span><span class="p">(</span><span class="mi">70</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">turn_right</span><span class="p">(</span><span class="mi">70</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
</pre></div>
</div>
<blockquote>
<div><p>💡 <code class="docutils literal notranslate"><span class="pre">turn_left()</span></code> và <code class="docutils literal notranslate"><span class="pre">turn_right()</span></code> điều khiển M1 &amp; M2 theo hướng ngược nhau.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="dieu-khien-tung-motor-rieng">
<h3>Điều khiển từng motor riêng<a class="headerlink" href="#dieu-khien-tung-motor-rieng" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">bee</span><span class="o">.</span><span class="n">motor1</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">80</span><span class="p">)</span>   <span class="c1"># Motor trái</span>
<span class="n">bee</span><span class="o">.</span><span class="n">motor2</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">80</span><span class="p">)</span>   <span class="c1"># Motor phải</span>
<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
</pre></div>
</div>
</section>
<hr class="docutils" />
<section id="dieu-khien-servo">
<h3>Điều khiển servo<a class="headerlink" href="#dieu-khien-servo" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">bee</span><span class="o">.</span><span class="n">init_bee</span><span class="p">()</span>

<span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">90</span><span class="p">)</span>
<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">180</span><span class="p">)</span>
</pre></div>
</div>
<p>Servo sẽ xoay tuần tự từ 0 → 90 → 180 độ.</p>
</section>
</section>
<hr class="docutils" />
<section id="thao-luan-va-mo-rong">
<h2>3. Thảo luận và mở rộng<a class="headerlink" href="#thao-luan-va-mo-rong" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Câu hỏi</p></th>
<th class="head"><p>Gợi ý</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Làm sao để robot tiến khi nhấn nút A?</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">bee.buttonA.is_pressed()</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Làm sao để robot lùi khi gặp vật cản?</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">bee.imu.is_shaking()</span></code> hoặc cảm biến khoảng cách</p></td>
</tr>
<tr class="row-even"><td><p>Làm sao để servo xoay theo LED?</p></td>
<td><p>Kết hợp <code class="docutils literal notranslate"><span class="pre">bee.led1.set_rgb()</span></code> với <code class="docutils literal notranslate"><span class="pre">bee.servo1.position()</span></code></p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="ket-luan">
<h2>4. Kết luận<a class="headerlink" href="#ket-luan" title="Link to this heading"></a></h2>
<p>Trong bài này, bạn đã học cách:</p>
<ul class="simple">
<li><p>Điều khiển motor DC (tiến, lùi, dừng, quay)</p></li>
<li><p>Điều khiển servo xoay góc</p></li>
<li><p>Lập trình robot di chuyển bằng Blockly hoặc Python</p></li>
</ul>
<blockquote>
<div><p>Bước tiếp theo: Hãy kết hợp <strong>button + LED + motor</strong> để làm robot “biết phản ứng” khi được nhấn!</p>
</div></blockquote>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="5.oled-example.html" class="btn btn-neutral float-left" title="Màn hình OLED" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="7.servo-example.html" class="btn btn-neutral float-right" title="Servo" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>