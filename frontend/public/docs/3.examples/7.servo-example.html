

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Servo &mdash; Tài <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Cảm biến IMU" href="8.imu-example.html" />
    <link rel="prev" title="Động cơ" href="6.motor-example.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Servo</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#muc-tieu">Mục tiêu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#phan-cung-can-co">Phần cứng cần có</a></li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-ide">1. Lập trình với BeE IDE</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#xoay-servo-den-goc-90">Xoay servo đến góc 90°</a></li>
<li class="toctree-l3"><a class="reference internal" href="#xoay-tuan-tu-nhieu-goc">Xoay tuần tự nhiều góc</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dieu-khien-servo-bang-nut-a-b">Điều khiển servo bằng nút A/B</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-python">2. Lập trình với BeE Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#xoay-servo-den-goc-co-dinh">Xoay servo đến góc cố định</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id1">Xoay tuần tự nhiều góc</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dieu-khien-bang-nut-a-b">Điều khiển bằng nút A/B</a></li>
<li class="toctree-l3"><a class="reference internal" href="#ket-hop-led-va-servo">Kết hợp LED và Servo</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#thao-luan-va-mo-rong">3. Thảo luận và mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ket-luan">4. Kết luận</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../2.bee-ide/1.index.html">BeE IDE</a></li>
      <li class="breadcrumb-item active">Servo</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/3.examples/7.servo-example.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="servo">
<h1>Servo<a class="headerlink" href="#servo" title="Link to this heading"></a></h1>
<p><img alt="Servo Example Cover" src="3.examples/_static/bee-board-v2/examples/servo-example-cover.jpg" /></p>
<hr class="docutils" />
<section id="muc-tieu">
<h2>Mục tiêu<a class="headerlink" href="#muc-tieu" title="Link to this heading"></a></h2>
<p>Trong bài học này, bạn sẽ học cách:</p>
<ul class="simple">
<li><p>Điều khiển servo xoay đến góc mong muốn</p></li>
<li><p>Tạo chuyển động tuần tự và hiệu ứng servo mượt mà</p></li>
<li><p>Kết hợp servo với Button và LED để tạo robot có hành vi thông minh</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="phan-cung-can-co">
<h2>Phần cứng cần có<a class="headerlink" href="#phan-cung-can-co" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Số lượng</p></th>
<th class="head"><p>Ghi chú</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>BeE Board V2</p></td>
<td><p>1</p></td>
<td><p>Bo mạch chính</p></td>
</tr>
<tr class="row-odd"><td><p>Servo RC (SG90 hoặc tương đương)</p></td>
<td><p>1–2</p></td>
<td><p>Cắm vào cổng S1–S4</p></td>
</tr>
<tr class="row-even"><td><p>Cáp USB-C</p></td>
<td><p>1</p></td>
<td><p>Kết nối với máy tính</p></td>
</tr>
<tr class="row-odd"><td><p>Nguồn pin 5–6V</p></td>
<td><p>1</p></td>
<td><p>Nếu dùng nhiều servo</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>💡 BeE Board V2 có 4 cổng điều khiển servo: <strong>S1 – S4</strong>,
được điều khiển thông qua chip <strong>PCA9685 (I2C)</strong>.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-ide">
<h2>1. Lập trình với BeE IDE<a class="headerlink" href="#lap-trinh-voi-bee-ide" title="Link to this heading"></a></h2>
<p>Truy cập BeE IDE:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
<section id="xoay-servo-den-goc-90">
<h3>Xoay servo đến góc 90°<a class="headerlink" href="#xoay-servo-den-goc-90" title="Link to this heading"></a></h3>
<p>Kéo các khối:</p>
<ol class="arabic simple">
<li><p>“Servo số 1 xoay đến góc 90 độ”</p></li>
<li><p>“Chờ 1 giây”</p></li>
<li><p>“Servo số 1 xoay đến góc 0 độ”</p></li>
</ol>
<p><img alt="Blockly Servo Basic" src="3.examples/_static/bee-ide/examples/servo-basic-blockly.jpg" /></p>
<p>Khi nhấn <strong>Run ▶️</strong>, servo xoay từ 0° → 90° rồi trở lại 0°.</p>
</section>
<hr class="docutils" />
<section id="xoay-tuan-tu-nhieu-goc">
<h3>Xoay tuần tự nhiều góc<a class="headerlink" href="#xoay-tuan-tu-nhieu-goc" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Dùng khối “lặp lại 3 lần”</p></li>
<li><p>Trong vòng lặp, tăng góc mỗi lần 30°</p></li>
<li><p>Thêm “chờ 0.2 giây” giữa mỗi lần</p></li>
</ol>
<p><img alt="Blockly Servo Loop" src="3.examples/_static/bee-ide/examples/servo-loop-blockly.jpg" /></p>
<p>Kết quả: servo xoay dần từng bước, tạo hiệu ứng mượt mà</p>
</section>
<hr class="docutils" />
<section id="dieu-khien-servo-bang-nut-a-b">
<h3>Điều khiển servo bằng nút A/B<a class="headerlink" href="#dieu-khien-servo-bang-nut-a-b" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Nếu <strong>A được nhấn</strong> → servo xoay sang 0°</p></li>
<li><p>Nếu <strong>B được nhấn</strong> → servo xoay sang 180°</p></li>
</ol>
<p><img alt="Blockly Servo Button" src="3.examples/_static/bee-ide/examples/servo-button-blockly.jpg" /></p>
</section>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-python">
<h2>2. Lập trình với BeE Python<a class="headerlink" href="#lap-trinh-voi-bee-python" title="Link to this heading"></a></h2>
<p>Truy cập BeE Python:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/python">https://beestemsolutions.com.vn/studio/python</a></p>
<section id="xoay-servo-den-goc-co-dinh">
<h3>Xoay servo đến góc cố định<a class="headerlink" href="#xoay-servo-den-goc-co-dinh" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">90</span><span class="p">)</span>
<span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
<span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
</pre></div>
</div>
<p>Servo xoay từ 0° → 90° rồi trở lại.</p>
</section>
<hr class="docutils" />
<section id="id1">
<h3>Xoay tuần tự nhiều góc<a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">for</span> <span class="n">angle</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">181</span><span class="p">,</span> <span class="mi">30</span><span class="p">):</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="n">angle</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>
</pre></div>
</div>
<p>Servo sẽ xoay qua 0°, 30°, 60°, 90°, 120°, 150°, 180° tuần tự.</p>
</section>
<hr class="docutils" />
<section id="dieu-khien-bang-nut-a-b">
<h3>Điều khiển bằng nút A/B<a class="headerlink" href="#dieu-khien-bang-nut-a-b" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">buttonA</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">():</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
    <span class="k">elif</span> <span class="n">bee</span><span class="o">.</span><span class="n">buttonB</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">():</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">180</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>
</pre></div>
</div>
<p>Khi nhấn nút <strong>A</strong>, servo về vị trí 0°.
Khi nhấn nút <strong>B</strong>, servo xoay sang 180°.</p>
</section>
<hr class="docutils" />
<section id="ket-hop-led-va-servo">
<h3>Kết hợp LED và Servo<a class="headerlink" href="#ket-hop-led-va-servo" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">for</span> <span class="n">angle</span> <span class="ow">in</span> <span class="p">[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">90</span><span class="p">,</span> <span class="mi">180</span><span class="p">,</span> <span class="mi">90</span><span class="p">]:</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="n">angle</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">off</span><span class="p">()</span>
</pre></div>
</div>
<p>Servo xoay tuần tự, LED nhấp nháy theo chuyển động</p>
</section>
</section>
<hr class="docutils" />
<section id="thao-luan-va-mo-rong">
<h2>3. Thảo luận và mở rộng<a class="headerlink" href="#thao-luan-va-mo-rong" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Câu hỏi</p></th>
<th class="head"><p>Gợi ý</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Làm sao để servo xoay theo góc ngẫu nhiên?</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">random.randint(0,180)</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Làm sao để servo dừng ở vị trí giữa khi khởi động?</p></td>
<td><p>Gọi <code class="docutils literal notranslate"><span class="pre">bee.servo1.position(90)</span></code> ở đầu chương trình</p></td>
</tr>
<tr class="row-even"><td><p>Có thể dùng nhiều servo cùng lúc không?</p></td>
<td><p>Có! Dùng <code class="docutils literal notranslate"><span class="pre">bee.servo2</span></code>, <code class="docutils literal notranslate"><span class="pre">bee.servo3</span></code>, <code class="docutils literal notranslate"><span class="pre">bee.servo4</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Có thể điều khiển servo bằng cảm biến không?</p></td>
<td><p>Có thể, ví dụ: xoay theo góc nghiêng IMU</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="ket-luan">
<h2>4. Kết luận<a class="headerlink" href="#ket-luan" title="Link to this heading"></a></h2>
<p>Trong bài học này, bạn đã học cách:</p>
<ul class="simple">
<li><p>Điều khiển servo xoay đến góc mong muốn</p></li>
<li><p>Tạo chuyển động tuần tự hoặc phản ứng theo nút nhấn</p></li>
<li><p>Kết hợp servo với LED và cảm biến</p></li>
</ul>
<blockquote>
<div><p>💡 Hãy thử làm <strong>cánh tay robot mini</strong> hoặc <strong>robot gật đầu</strong> bằng LEGO + BeE Board nhé</p>
</div></blockquote>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="6.motor-example.html" class="btn btn-neutral float-left" title="Động cơ" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="8.imu-example.html" class="btn btn-neutral float-right" title="Cảm biến IMU" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>