

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Cảm biến <PERSON> &mdash; T<PERSON>i <PERSON> BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Cài đặt" href="../2.bee-ide/2.installation.html" />
    <link rel="prev" title="Servo" href="7.servo-example.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="7.servo-example.html">Servo</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Cảm biến IMU</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#muc-tieu">Mục tiêu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#phan-cung-can-co">Phần cứng cần có</a></li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-ide">1. Lập trình với BeE IDE</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#hien-thi-goc-nghieng">Hiển thị góc nghiêng</a></li>
<li class="toctree-l3"><a class="reference internal" href="#phat-hien-rung-lac">Phát hiện rung lắc</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dieu-khien-servo-theo-goc-nghieng">Điều khiển Servo theo góc nghiêng</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-python">2. Lập trình với BeE Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#id1">Hiển thị góc nghiêng</a></li>
<li class="toctree-l3"><a class="reference internal" href="#phat-hien-rung-shake-detection">Phát hiện rung (Shake Detection)</a></li>
<li class="toctree-l3"><a class="reference internal" href="#id2">Điều khiển Servo theo góc nghiêng</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#thao-luan-va-mo-rong">3. Thảo luận và mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ket-luan">4. Kết luận</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="../4.extensions/ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="../2.bee-ide/1.index.html">BeE IDE</a></li>
      <li class="breadcrumb-item active">Cảm biến IMU</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/3.examples/8.imu-example.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="cam-bien-imu">
<h1>Cảm biến IMU<a class="headerlink" href="#cam-bien-imu" title="Link to this heading"></a></h1>
<p><img alt="IMU Example Cover" src="3.examples/_static/bee-board-v2/examples/imu-example-cover.jpg" /></p>
<hr class="docutils" />
<section id="muc-tieu">
<h2>Mục tiêu<a class="headerlink" href="#muc-tieu" title="Link to this heading"></a></h2>
<p>Trong bài học này, bạn sẽ học cách:</p>
<ul class="simple">
<li><p>Sử dụng cảm biến <strong>IMU MPU6050</strong> để phát hiện nghiêng và rung</p></li>
<li><p>Hiển thị dữ liệu góc nghiêng trên OLED</p></li>
<li><p>Kết hợp IMU với LED hoặc Servo để tạo robot phản ứng thông minh</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="phan-cung-can-co">
<h2>Phần cứng cần có<a class="headerlink" href="#phan-cung-can-co" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Số lượng</p></th>
<th class="head"><p>Ghi chú</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>BeE Board V2</p></td>
<td><p>1</p></td>
<td><p>Bo mạch chính có sẵn cảm biến IMU MPU6050</p></td>
</tr>
<tr class="row-odd"><td><p>Cáp USB-C</p></td>
<td><p>1</p></td>
<td><p>Kết nối với máy tính</p></td>
</tr>
<tr class="row-even"><td><p>Trình duyệt web</p></td>
<td><p>1</p></td>
<td><p>Chrome, Edge hoặc Safari</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>💡 Cảm biến <strong>MPU6050</strong> đo được <strong>gia tốc (Accelerometer)</strong> và <strong>con quay hồi chuyển (Gyroscope)</strong>,
dùng để xác định <strong>góc nghiêng, rung, hoặc chuyển động của board</strong>.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-ide">
<h2>1. Lập trình với BeE IDE<a class="headerlink" href="#lap-trinh-voi-bee-ide" title="Link to this heading"></a></h2>
<p>Truy cập BeE IDE:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
<section id="hien-thi-goc-nghieng">
<h3>Hiển thị góc nghiêng<a class="headerlink" href="#hien-thi-goc-nghieng" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Kéo khối “Lấy giá trị Roll của IMU”</p></li>
<li><p>Hiển thị giá trị đó trên màn hình OLED</p></li>
<li><p>Lặp lại liên tục</p></li>
</ol>
<p><img alt="Blockly IMU OLED" src="3.examples/_static/bee-ide/examples/imu-oled-blockly.jpg" /></p>
<blockquote>
<div><p>Mỗi khi bạn nghiêng board, giá trị trên OLED sẽ thay đổi theo góc nghiêng</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="phat-hien-rung-lac">
<h3>Phát hiện rung lắc<a class="headerlink" href="#phat-hien-rung-lac" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Dùng khối “Nếu board bị lắc thì…”</p></li>
<li><p>Bật LED1 màu đỏ hoặc phát tiếng Buzzer</p></li>
</ol>
<p><img alt="Blockly IMU Shake" src="3.examples/_static/bee-ide/examples/imu-shake-blockly.jpg" /></p>
<p>Khi bạn rung nhẹ board, LED sẽ sáng hoặc buzzer kêu cảnh báo</p>
</section>
<hr class="docutils" />
<section id="dieu-khien-servo-theo-goc-nghieng">
<h3>Điều khiển Servo theo góc nghiêng<a class="headerlink" href="#dieu-khien-servo-theo-goc-nghieng" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p>Đọc giá trị <strong>Pitch</strong></p></li>
<li><p>Chuyển đổi sang góc servo (0–180)</p></li>
<li><p>Dùng khối “Servo số 1 xoay đến góc … độ”</p></li>
</ol>
<p><img alt="Blockly IMU Servo" src="3.examples/_static/bee-ide/examples/imu-servo-blockly.jpg" /></p>
</section>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-python">
<h2>2. Lập trình với BeE Python<a class="headerlink" href="#lap-trinh-voi-bee-python" title="Link to this heading"></a></h2>
<p>Truy cập BeE Python:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/python">https://beestemsolutions.com.vn/studio/python</a></p>
<section id="id1">
<h3>Hiển thị góc nghiêng<a class="headerlink" href="#id1" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
    <span class="n">roll</span> <span class="o">=</span> <span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">rollDeg</span>
    <span class="n">pitch</span> <span class="o">=</span> <span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">pitchDeg</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Roll: </span><span class="si">{</span><span class="n">roll</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Pitch: </span><span class="si">{</span><span class="n">pitch</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>
</pre></div>
</div>
<p>Khi nghiêng board, giá trị <strong>Roll</strong> và <strong>Pitch</strong> trên OLED thay đổi theo.</p>
</section>
<hr class="docutils" />
<section id="phat-hien-rung-shake-detection">
<h3>Phát hiện rung (Shake Detection)<a class="headerlink" href="#phat-hien-rung-shake-detection" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">is_shaking</span><span class="p">():</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">TONES</span><span class="p">[</span><span class="s1">&#39;C6&#39;</span><span class="p">])</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">be_quiet</span><span class="p">()</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">off</span><span class="p">()</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>
</pre></div>
</div>
<p>Khi rung nhẹ board → LED sáng đỏ + kêu “bíp” cảnh báo</p>
</section>
<hr class="docutils" />
<section id="id2">
<h3>Điều khiển Servo theo góc nghiêng<a class="headerlink" href="#id2" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">update</span><span class="p">()</span>
    <span class="n">angle</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="nb">min</span><span class="p">(</span><span class="mi">180</span><span class="p">,</span> <span class="nb">int</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">imu</span><span class="o">.</span><span class="n">rollDeg</span> <span class="o">+</span> <span class="mi">90</span><span class="p">)))</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">servo1</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="n">angle</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>
</pre></div>
</div>
<p>Khi nghiêng board sang trái/phải, servo xoay theo cùng hướng</p>
</section>
</section>
<hr class="docutils" />
<section id="thao-luan-va-mo-rong">
<h2>3. Thảo luận và mở rộng<a class="headerlink" href="#thao-luan-va-mo-rong" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Câu hỏi</p></th>
<th class="head"><p>Gợi ý</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Làm sao để đo độ rung mạnh?</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">bee.imu.shake_strength</span></code></p></td>
</tr>
<tr class="row-odd"><td><p>Có thể phát hiện hướng xoay không?</p></td>
<td><p>Dùng <code class="docutils literal notranslate"><span class="pre">bee.imu.gyroZ</span></code> để tính vận tốc quay</p></td>
</tr>
<tr class="row-even"><td><p>Làm sao để hiển thị đồ thị góc nghiêng trên OLED?</p></td>
<td><p>Dùng hàm <code class="docutils literal notranslate"><span class="pre">bee.oled.write_bar(value)</span></code> (phiên bản sắp phát hành)</p></td>
</tr>
<tr class="row-odd"><td><p>Có thể kết hợp IMU với robot di chuyển không?</p></td>
<td><p>Có! Dùng IMU để giúp robot giữ thăng bằng hoặc tự quay khi nghiêng</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="ket-luan">
<h2>4. Kết luận<a class="headerlink" href="#ket-luan" title="Link to this heading"></a></h2>
<p>Trong bài học này, bạn đã học cách:</p>
<ul class="simple">
<li><p>Đọc dữ liệu cảm biến IMU (Roll, Pitch, Shake)</p></li>
<li><p>Hiển thị dữ liệu trên OLED</p></li>
<li><p>Kết hợp IMU với LED, Buzzer, và Servo để tạo robot phản ứng thông minh</p></li>
</ul>
<blockquote>
<div><p>💡 Hãy thử làm <strong>robot giữ thăng bằng mini</strong> hoặc <strong>cảm biến phát hiện động đất mini</strong> nhé!</p>
</div></blockquote>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="7.servo-example.html" class="btn btn-neutral float-left" title="Servo" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../2.bee-ide/2.installation.html" class="btn btn-neutral float-right" title="Cài đặt" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>