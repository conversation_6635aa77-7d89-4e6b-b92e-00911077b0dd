

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Gi<PERSON><PERSON> thiệu &mdash; Tài <PERSON> BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Module Nút Nhấn" href="button.html" />
    <link rel="prev" title="Ví dụ lập trình" href="../3.examples/1.index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul class="current">
<li class="toctree-l1 current"><a class="current reference internal" href="#">Giới thiệu</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#id1">Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#chuan-bi">Chuẩn bị</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cach-cai-dat">Cách cài đặt</a></li>
<li class="toctree-l2"><a class="reference internal" href="#danh-sach-module-mo-rong">Danh sách module mở rộng</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#bee-starter-kit">BeE Starter Kit</a></li>
<li class="toctree-l3"><a class="reference internal" href="#grove-ecosystem">Grove Ecosystem</a></li>
<li class="toctree-l3"><a class="reference internal" href="#ohstem-ecosystem">OhStem Ecosystem</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#cong-mo-rong-tren-bee-board-v2">Cổng mở rộng trên BeE Board V2</a></li>
<li class="toctree-l2"><a class="reference internal" href="#cach-cai-dat-va-su-dung">Cách cài đặt và sử dụng</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#buoc-1-cam-module-vao-port-thich-hop">Bước 1. Cắm module vào PORT thích hợp</a></li>
<li class="toctree-l3"><a class="reference internal" href="#buoc-2-import-thu-vien-trong-chuong-trinh-python">Bước 2. Import thư viện trong chương trình Python</a></li>
<li class="toctree-l3"><a class="reference internal" href="#buoc-3-lap-trinh-voi-bee-ide">Bước 3. Lập trình với BeE IDE</a></li>
<li class="toctree-l3"><a class="reference internal" href="#buoc-4-lap-trinh-voi-bee-python">Bước 4. Lập trình với BeE Python</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#huong-dan-cai-dat-thu-vien-module">Hướng dẫn cài đặt thư viện module</a></li>
<li class="toctree-l2"><a class="reference internal" href="#meo-su-dung-hieu-qua">Mẹo sử dụng hiệu quả</a><ul>
<li class="toctree-l3"><a class="reference internal" href="button.html">Module Nút Nhấn</a></li>
<li class="toctree-l3"><a class="reference internal" href="color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l3"><a class="reference internal" href="dht11.html">Module DHT11</a></li>
<li class="toctree-l3"><a class="reference internal" href="led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l3"><a class="reference internal" href="line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l3"><a class="reference internal" href="rc522.html">BeeRC522</a></li>
<li class="toctree-l3"><a class="reference internal" href="ultrasonic.html">BeeUltrasonic</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#tai-lieu-lien-quan">Tài liệu liên quan</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
      <li class="breadcrumb-item active">Giới thiệu</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/4.extensions/1.index.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="gioi-thieu">
<h1>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h1>
<p align="center">
  <img 
    src="../_static/extensions/extensions.png" 
    alt="Extensions Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<hr class="docutils" />
<section id="id1">
<h2>Giới thiệu<a class="headerlink" href="#id1" title="Link to this heading"></a></h2>
<p>Bên cạnh các module tích hợp sẵn (LED, Buzzer, Button A/B, OLED, IMU, Motor, Servo),
<strong>BeE Board V2</strong> còn hỗ trợ <strong>nhiều module mở rộng (Extension Modules)</strong> thông qua 6 cổng <strong>PORT1 → PORT6</strong>.</p>
<p>Các module này giúp mở rộng khả năng của BeE Board để học sinh có thể:</p>
<ul class="simple">
<li><p>Đo cảm biến môi trường (nhiệt độ, độ ẩm, ánh sáng, khoảng cách, v.v.)</p></li>
<li><p>Điều khiển thiết bị bên ngoài (đèn, motor, relay)</p></li>
<li><p>Giao tiếp với IoT hoặc AI (wifi, camera, RFID, v.v.)</p></li>
</ul>
<blockquote>
<div><p>Mỗi module được kết nối dễ dàng qua dây Grove (chuẩn 4 chân: GND, VCC, SIG, SIG2/I2C).</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="chuan-bi">
<h2>Chuẩn bị<a class="headerlink" href="#chuan-bi" title="Link to this heading"></a></h2>
<p>Để sử dụng các module mở rộng, bạn cần:</p>
<ul class="simple">
<li><p><strong>BeE Board V2</strong> (với firmware mới nhất)</p></li>
<li><p><strong>Dây Grove 4 chân</strong> (chuẩn) để kết nối module vào PORT</p></li>
</ul>
<p align="center">
  <img 
    src="../_static/extensions/grove-cable.jpg" 
    alt="Grove Cable" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
<ul class="simple">
<li><p><strong>BeE IDE</strong> hoặc <strong>BeE Python</strong> để lập trình</p></li>
<li><p>Module có cổng kết nối chuẩn Grove</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="cach-cai-dat">
<h2>Cách cài đặt<a class="headerlink" href="#cach-cai-dat" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p>Cắm module vào PORT thích hợp</p></li>
<li><p>Click vào nút extension trên BeE IDE</p></li>
<li><p>Chọn module muốn sử dụng, click Install</p></li>
<li><p>Kéo thả và sử dụng các khối lệnh tương ứng vào Workspace</p></li>
</ol>
<p align="center">
  <img 
    src="../_static/extensions/install.jpg" 
    alt="Extension Button" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>
</section>
<hr class="docutils" />
<section id="danh-sach-module-mo-rong">
<h2>Danh sách module mở rộng<a class="headerlink" href="#danh-sach-module-mo-rong" title="Link to this heading"></a></h2>
<section id="bee-starter-kit">
<h3>BeE Starter Kit<a class="headerlink" href="#bee-starter-kit" title="Link to this heading"></a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Module</p></th>
<th class="head"><p>Loại tín hiệu</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Liên kết tài liệu</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Button</strong></p></td>
<td><p>Digital</p></td>
<td><p>Nút nhấn rời ngoài, hỗ trợ debounce &amp; long press</p></td>
<td><p><a class="reference internal" href="#../modules/button.md"><span class="xref myst">Button Module</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><strong>DHT11</strong></p></td>
<td><p>1-Wire</p></td>
<td><p>Cảm biến đo nhiệt độ &amp; độ ẩm</p></td>
<td><p><a class="reference internal" href="#../modules/dht11.md"><span class="xref myst">DHT11 Module</span></a></p></td>
</tr>
<tr class="row-even"><td><p><strong>Neopixel RGB LED</strong></p></td>
<td><p>1-Wire</p></td>
<td><p>Dải đèn RGB nhiều màu, điều khiển lập trình</p></td>
<td><p><a class="reference internal" href="#../modules/neopixel.md"><span class="xref myst">Neopixel Module</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><strong>BH1750</strong></p></td>
<td><p>I2C</p></td>
<td><p>Cảm biến ánh sáng</p></td>
<td><p>(Sắp ra mắt)</p></td>
</tr>
<tr class="row-even"><td><p><strong>VL53L0X</strong></p></td>
<td><p>I2C</p></td>
<td><p>Cảm biến đo khoảng cách chính xác</p></td>
<td><p>(Sắp ra mắt)</p></td>
</tr>
<tr class="row-odd"><td><p><strong>TCR5000</strong></p></td>
<td><p>ADC</p></td>
<td><p>Cảm biến dò line</p></td>
<td><p>(Sắp ra mắt)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="grove-ecosystem">
<h3>Grove Ecosystem<a class="headerlink" href="#grove-ecosystem" title="Link to this heading"></a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Module</p></th>
<th class="head"><p>Loại tín hiệu</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Liên kết tài liệu</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Ultrasonic</strong></p></td>
<td><p>Digital</p></td>
<td><p>Cảm biến đo khoảng cách (không chính xác)</p></td>
<td><p><a class="reference internal" href="#../modules/ultrasonic.md"><span class="xref myst">Ultrasonic Module</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><strong>Line Detect</strong></p></td>
<td><p>Digital</p></td>
<td><p>Cảm biến dò line</p></td>
<td><p><a class="reference internal" href="#../modules/line-detect.md"><span class="xref myst">Line Detect Module</span></a></p></td>
</tr>
<tr class="row-even"><td><p><strong>Led Bar</strong></p></td>
<td><p>Digital</p></td>
<td><p>Dải led hiển thị cấp độ</p></td>
<td><p>(Sắp ra mắt)</p></td>
</tr>
<tr class="row-odd"><td><p><strong>Light Sensor</strong></p></td>
<td><p>Analog</p></td>
<td><p>Cảm biến ánh sáng</p></td>
<td><p>(Sắp ra mắt)</p></td>
</tr>
<tr class="row-even"><td><p><strong>Rotation Sensor</strong></p></td>
<td><p>Analog</p></td>
<td><p>Cảm biến góc quay</p></td>
<td><p>(Sắp ra mắt)</p></td>
</tr>
<tr class="row-odd"><td><p><strong>PIR Sensor</strong></p></td>
<td><p>Digital</p></td>
<td><p>Cảm biến chuyển động</p></td>
<td><p>(Sắp ra mắt)</p></td>
</tr>
</tbody>
</table>
</section>
<section id="ohstem-ecosystem">
<h3>OhStem Ecosystem<a class="headerlink" href="#ohstem-ecosystem" title="Link to this heading"></a></h3>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Module</p></th>
<th class="head"><p>Loại tín hiệu</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Liên kết tài liệu</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><strong>Led Segment</strong></p></td>
<td><p>Digital</p></td>
<td><p>Dải led hiển thị số</p></td>
<td><p><a class="reference internal" href="#../modules/led-segment.md"><span class="xref myst">Led Segment Module</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><strong>RFID RC522</strong></p></td>
<td><p>I2C</p></td>
<td><p>Nhận dạng thẻ RFID / NFC</p></td>
<td><p><a class="reference internal" href="#../modules/rc522.md"><span class="xref myst">RFID RC522 Module</span></a></p></td>
</tr>
<tr class="row-even"><td><p><strong>Color Detect</strong></p></td>
<td><p>I2C</p></td>
<td><p>Cảm biến màu</p></td>
<td><p><a class="reference internal" href="#../modules/color-detect.md"><span class="xref myst">Color Detect Module</span></a></p></td>
</tr>
<tr class="row-odd"><td><p><strong>Microphone</strong></p></td>
<td><p>Analog</p></td>
<td><p>Cảm biến âm thanh</p></td>
<td><p>(Sắp ra mắt)</p></td>
</tr>
</tbody>
</table>
</section>
</section>
<hr class="docutils" />
<section id="cong-mo-rong-tren-bee-board-v2">
<h2>Cổng mở rộng trên BeE Board V2<a class="headerlink" href="#cong-mo-rong-tren-bee-board-v2" title="Link to this heading"></a></h2>
<p><img alt="BeE Board Ports Diagram" src="4.extensions/_static/bee-board-v2/extension/bee-board-ports.jpg" /></p>
<p>Mỗi cổng <strong>PORTx</strong> gồm 4 chân:</p>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Chân</p></th>
<th class="head"><p>Mô tả</p></th>
<th class="head"><p>Ghi chú</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>VCC</p></td>
<td><p>3.3V</p></td>
<td><p>Nguồn cấp cho module</p></td>
</tr>
<tr class="row-odd"><td><p>GND</p></td>
<td><p>Ground</p></td>
<td><p>Chung mass hệ thống</p></td>
</tr>
<tr class="row-even"><td><p>SIG</p></td>
<td><p>Digital/Analog</p></td>
<td><p>Dữ liệu hoặc điều khiển</p></td>
</tr>
<tr class="row-odd"><td><p>SCL/SDA</p></td>
<td><p>(với module I2C)</p></td>
<td><p>Giao tiếp I2C</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>Một số module (OLED, MPU6050) dùng giao tiếp <strong>I2C</strong> (chung SDA/SCL).
Các module Digital như Button, DHT11, Buzzer, Neopixel dùng <strong>từng cổng PORT riêng biệt</strong>.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="cach-cai-dat-va-su-dung">
<h2>Cách cài đặt và sử dụng<a class="headerlink" href="#cach-cai-dat-va-su-dung" title="Link to this heading"></a></h2>
<section id="buoc-1-cam-module-vao-port-thich-hop">
<h3>Bước 1. Cắm module vào PORT thích hợp<a class="headerlink" href="#buoc-1-cam-module-vao-port-thich-hop" title="Link to this heading"></a></h3>
<ul class="simple">
<li><p>Dùng dây Grove 4 chân (chuẩn) để kết nối module vào <strong>PORT1 – PORT6</strong>.</p></li>
<li><p>Đảm bảo hướng dây đúng: GND ↔ GND, VCC ↔ VCC, SIG ↔ SIG.</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="buoc-2-import-thu-vien-trong-chuong-trinh-python">
<h3>Bước 2. Import thư viện trong chương trình Python<a class="headerlink" href="#buoc-2-import-thu-vien-trong-chuong-trinh-python" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">DHT11</span><span class="w"> </span><span class="kn">import</span> <span class="n">DHT11</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">Button</span><span class="w"> </span><span class="kn">import</span> <span class="n">Button</span>

<span class="n">sensor</span> <span class="o">=</span> <span class="n">DHT11</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>
<span class="n">button</span> <span class="o">=</span> <span class="n">Button</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT2</span><span class="p">)</span>
</pre></div>
</div>
</section>
<hr class="docutils" />
<section id="buoc-3-lap-trinh-voi-bee-ide">
<h3>Bước 3. Lập trình với BeE IDE<a class="headerlink" href="#buoc-3-lap-trinh-voi-bee-ide" title="Link to this heading"></a></h3>
<p>Truy cập:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">https://beestemsolutions.com.vn/studio/bee-ide</a></p>
<ul class="simple">
<li><p>Chọn nhóm khối tương ứng trong <strong>Sensor</strong>, <strong>Input</strong>, hoặc <strong>Output</strong></p></li>
<li><p>Kéo thả khối: “Đọc nhiệt độ từ DHT11 ở PORT1”</p></li>
<li><p>Kết hợp hiển thị kết quả lên OLED hoặc LED RGB</p></li>
</ul>
<p><img alt="Blockly Extension Example" src="4.extensions/_static/bee-ide/blocks/extension-blocks.jpg" /></p>
</section>
<hr class="docutils" />
<section id="buoc-4-lap-trinh-voi-bee-python">
<h3>Bước 4. Lập trình với BeE Python<a class="headerlink" href="#buoc-4-lap-trinh-voi-bee-python" title="Link to this heading"></a></h3>
<p>Truy cập:
👉 <a class="reference external" href="https://beestemsolutions.com.vn/studio/python">https://beestemsolutions.com.vn/studio/python</a></p>
<p>Ví dụ đọc dữ liệu từ cảm biến và hiển thị OLED:</p>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">DHT11</span><span class="w"> </span><span class="kn">import</span> <span class="n">DHT11</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">sensor</span> <span class="o">=</span> <span class="n">DHT11</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">t</span><span class="p">,</span> <span class="n">h</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Nhiệt độ: </span><span class="si">{</span><span class="n">t</span><span class="si">}</span><span class="s2">°C&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Độ ẩm: </span><span class="si">{</span><span class="n">h</span><span class="si">}</span><span class="s2">%&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<hr class="docutils" />
<section id="huong-dan-cai-dat-thu-vien-module">
<h2>Hướng dẫn cài đặt thư viện module<a class="headerlink" href="#huong-dan-cai-dat-thu-vien-module" title="Link to this heading"></a></h2>
<p>Tất cả module mở rộng của BeE Board đều được tích hợp sẵn trong firmware <strong>BeeBrain</strong>.
Bạn <strong>không cần tải thủ công</strong>, chỉ cần đảm bảo firmware mới nhất.</p>
<blockquote>
<div><p>Cập nhật firmware mới tại:
<a class="reference internal" href="#../flashing-guide.md"><span class="xref myst">Hướng dẫn nạp firmware</span></a></p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="meo-su-dung-hieu-qua">
<h2>Mẹo sử dụng hiệu quả<a class="headerlink" href="#meo-su-dung-hieu-qua" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Tình huống</p></th>
<th class="head"><p>Giải pháp</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Dữ liệu cảm biến không ổn định</p></td>
<td><p>Đọc chậm hơn (1 giây/lần), tránh nhiễu</p></td>
</tr>
<tr class="row-odd"><td><p>Kết nối sai chiều</p></td>
<td><p>Kiểm tra dây Grove đúng chiều GND/VCC</p></td>
</tr>
<tr class="row-even"><td><p>Nhiều module I2C</p></td>
<td><p>Dùng chung SDA/SCL, đảm bảo địa chỉ khác nhau</p></td>
</tr>
<tr class="row-odd"><td><p>Cần thêm module mới</p></td>
<td><p>Liên hệ BeE STEM để cập nhật firmware mới</p></td>
</tr>
</tbody>
</table>
<hr class="docutils" />
<div class="toctree-wrapper compound">
</div>
</section>
<section id="tai-lieu-lien-quan">
<h2>Tài liệu liên quan<a class="headerlink" href="#tai-lieu-lien-quan" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html"><span class="std std-doc">Giới thiệu phần cứng BeE Board V2</span></a></p></li>
<li><p><a class="reference internal" href="../2.bee-ide/5.flashing-image.html"><span class="std std-doc">Hướng dẫn nạp firmware</span></a></p></li>
<li><p><a class="reference internal" href="#../../2.bee-ide/1.index.md"><span class="xref myst">Lập trình với BeE IDE</span></a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="../3.examples/1.index.html" class="btn btn-neutral float-left" title="Ví dụ lập trình" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="button.html" class="btn btn-neutral float-right" title="Module Nút Nhấn" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>