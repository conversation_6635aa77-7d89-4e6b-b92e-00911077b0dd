

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Module Nút Nhấn &mdash; Tài Liệu BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="BeeColorDetect" href="color-detect.html" />
    <link rel="prev" title="Giới thiệu" href="1.index.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Module Nút Nhấn</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#ung-dung-thuc-te">Ứng dụng thực tế</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thong-so-ky-thuat">Thông số kỹ thuật</a></li>
<li class="toctree-l2"><a class="reference internal" href="#so-do-ket-noi">Sơ đồ kết nối</a></li>
<li class="toctree-l2"><a class="reference internal" href="#giao-dien-lap-trinh-python">Giao diện lập trình Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#khoi-tao">Khởi tạo</a></li>
<li class="toctree-l3"><a class="reference internal" href="#kiem-tra-trang-thai-nhan">Kiểm tra trạng thái nhấn</a></li>
<li class="toctree-l3"><a class="reference internal" href="#kiem-tra-trang-thai-tha">Kiểm tra trạng thái thả</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dang-ky-su-kien-nhan">Đăng ký sự kiện nhấn</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dang-ky-su-kien-tha">Đăng ký sự kiện thả</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dang-ky-su-kien-nhan-giu-long-press">Đăng ký sự kiện nhấn giữ (long press)</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-ide">Lập trình với BeE IDE</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-khoi-lenh">Ví dụ khối lệnh:</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-python">Ví dụ Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban-bat-tat-led-khi-nhan-nut">Ví dụ cơ bản – Bật tắt LED khi nhấn nút</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao-tao-menu-dieu-huong-bang-2-nut">Ví dụ nâng cao – Tạo menu điều hướng bằng 2 nút</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#giai-thich-ma">Giải thích mã</a></li>
<li class="toctree-l2"><a class="reference internal" href="#bai-tap-mo-rong">Bài tập mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-thuong-gap">Lỗi thường gặp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-nguyen-tham-khao">Tài nguyên tham khảo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">Module Nút Nhấn</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/4.extensions/button.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="module-nut-nhan">
<h1>Module Nút Nhấn<a class="headerlink" href="#module-nut-nhan" title="Link to this heading"></a></h1>
<p><img alt="Button Module Cover" src="4.extensions/_static/bee-board-v2/modules/button-cover.jpg" /></p>
<hr class="docutils" />
<section id="gioi-thieu">
<h2>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p><strong>Module Nút Nhấn</strong> (External Button) là cảm biến <strong>đầu vào kỹ thuật số (Digital Input)</strong> giúp <strong>phát hiện thao tác nhấn của người dùng</strong>.
Nó có khả năng <strong>chống nhiễu (debounce)</strong> và <strong>xử lý sự kiện nhấn – thả – nhấn giữ</strong> thông qua callback function.</p>
<p>Trên BeE Board V2 đã có sẵn <strong>2 nút tích hợp (Button A, Button B)</strong>,
nhưng bạn có thể <strong>kết nối thêm nhiều nút gắn ngoài</strong> qua các cổng <strong>PORT1–PORT6</strong> để tạo giao diện điều khiển phong phú hơn.</p>
</section>
<hr class="docutils" />
<section id="ung-dung-thuc-te">
<h2>Ứng dụng thực tế<a class="headerlink" href="#ung-dung-thuc-te" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p>Điều khiển LED, động cơ, hoặc bật/tắt hệ thống</p></li>
<li><p>Trò chơi phản xạ hoặc điều khiển robot</p></li>
<li><p>Hệ thống báo động, còi cảnh báo</p></li>
<li><p>Tạo menu điều hướng trên màn hình OLED</p></li>
<li><p>Giao tiếp người dùng trong dự án IoT</p></li>
</ul>
</section>
<hr class="docutils" />
<section id="thong-so-ky-thuat">
<h2>Thông số kỹ thuật<a class="headerlink" href="#thong-so-ky-thuat" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thông số</p></th>
<th class="head"><p>Giá trị</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Điện áp hoạt động</p></td>
<td><p>3.3V</p></td>
</tr>
<tr class="row-odd"><td><p>Tín hiệu đầu ra</p></td>
<td><p>Digital HIGH/LOW</p></td>
</tr>
<tr class="row-even"><td><p>Chế độ Pull</p></td>
<td><p>PULL_UP (mặc định) hoặc PULL_DOWN</p></td>
</tr>
<tr class="row-odd"><td><p>Thời gian chống dội (debounce)</p></td>
<td><p>200 ms (mặc định, có thể tùy chỉnh)</p></td>
</tr>
<tr class="row-even"><td><p>Thời gian nhấn giữ</p></td>
<td><p>1000 ms (có thể tùy chỉnh)</p></td>
</tr>
<tr class="row-odd"><td><p>Giao tiếp</p></td>
<td><p>GPIO qua cổng PORT của BeE Board</p></td>
</tr>
<tr class="row-even"><td><p>Hỗ trợ</p></td>
<td><p>Callback và Interrupt-based</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="so-do-ket-noi">
<h2>Sơ đồ kết nối<a class="headerlink" href="#so-do-ket-noi" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Dây</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>GND</p></td>
<td><p>Nối đất</p></td>
</tr>
<tr class="row-odd"><td><p>VCC</p></td>
<td><p>3.3V</p></td>
</tr>
<tr class="row-even"><td><p>SIG</p></td>
<td><p>Tín hiệu Digital vào PORTx</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>💡 Cắm module vào <strong>PORT1 – PORT6</strong> của BeE Board V2.
Mỗi PORT bao gồm 1 chân tín hiệu (GPIO) và 1 chân GND, 1 chân 3V3.</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="giao-dien-lap-trinh-python">
<h2>Giao diện lập trình Python<a class="headerlink" href="#giao-dien-lap-trinh-python" title="Link to this heading"></a></h2>
<section id="khoi-tao">
<h3>Khởi tạo<a class="headerlink" href="#khoi-tao" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">Button</span><span class="w"> </span><span class="kn">import</span> <span class="n">Button</span>

<span class="c1"># Khởi tạo module nút nhấn ở PORT1</span>
<span class="n">ext_btn</span> <span class="o">=</span> <span class="n">Button</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>
</pre></div>
</div>
</section>
<hr class="docutils" />
<section id="kiem-tra-trang-thai-nhan">
<h3>Kiểm tra trạng thái nhấn<a class="headerlink" href="#kiem-tra-trang-thai-nhan" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">ext_btn</span><span class="o">.</span><span class="n">is_pressed</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Nút đang được nhấn&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="kiem-tra-trang-thai-tha">
<h3>Kiểm tra trạng thái thả<a class="headerlink" href="#kiem-tra-trang-thai-tha" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">ext_btn</span><span class="o">.</span><span class="n">is_released</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Nút vừa được thả ra&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<hr class="docutils" />
<section id="dang-ky-su-kien-nhan">
<h3>Đăng ký sự kiện nhấn<a class="headerlink" href="#dang-ky-su-kien-nhan" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">on_press</span><span class="p">(</span><span class="n">pin</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Nút được nhấn!&quot;</span><span class="p">)</span>

<span class="n">ext_btn</span><span class="o">.</span><span class="n">on_press</span><span class="p">(</span><span class="n">on_press</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="dang-ky-su-kien-tha">
<h3>Đăng ký sự kiện thả<a class="headerlink" href="#dang-ky-su-kien-tha" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">on_released</span><span class="p">(</span><span class="n">pin</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Nút được thả!&quot;</span><span class="p">)</span>

<span class="n">ext_btn</span><span class="o">.</span><span class="n">on_released</span><span class="p">(</span><span class="n">on_released</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="dang-ky-su-kien-nhan-giu-long-press">
<h3>Đăng ký sự kiện nhấn giữ (long press)<a class="headerlink" href="#dang-ky-su-kien-nhan-giu-long-press" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">def</span><span class="w"> </span><span class="nf">on_long_press</span><span class="p">(</span><span class="n">pin</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Nhấn giữ 2 giây!&quot;</span><span class="p">)</span>

<span class="n">ext_btn</span><span class="o">.</span><span class="n">on_long_press</span><span class="p">(</span><span class="n">on_long_press</span><span class="p">,</span> <span class="mi">2000</span><span class="p">)</span>
</pre></div>
</div>
<blockquote>
<div><p>💡 Bạn có thể tạo nhiều nút khác nhau ở các PORT khác nhau:
<code class="docutils literal notranslate"><span class="pre">btn1</span> <span class="pre">=</span> <span class="pre">Button(bee.PORT1)</span></code> – <code class="docutils literal notranslate"><span class="pre">btn2</span> <span class="pre">=</span> <span class="pre">Button(bee.PORT2)</span></code></p>
</div></blockquote>
</section>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-ide">
<h2>Lập trình với BeE IDE<a class="headerlink" href="#lap-trinh-voi-bee-ide" title="Link to this heading"></a></h2>
<p><img alt="Button Blocks Example" src="4.extensions/_static/bee-ide/blocks/button-blocks.jpg" /></p>
<section id="vi-du-khoi-lenh">
<h3>Ví dụ khối lệnh:<a class="headerlink" href="#vi-du-khoi-lenh" title="Link to this heading"></a></h3>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">khi</span> <span class="n">nút</span> <span class="n">ở</span> <span class="n">PORT1</span> <span class="n">được</span> <span class="n">nhấn</span><span class="p">:</span>
    <span class="n">bật</span> <span class="n">LED1</span>
<span class="n">khi</span> <span class="n">nút</span> <span class="n">ở</span> <span class="n">PORT1</span> <span class="n">được</span> <span class="n">thả</span><span class="p">:</span>
    <span class="n">tắt</span> <span class="n">LED1</span>
<span class="n">khi</span> <span class="n">nút</span> <span class="n">ở</span> <span class="n">PORT1</span> <span class="n">được</span> <span class="n">nhấn</span> <span class="n">giữ</span> <span class="mi">2</span> <span class="n">giây</span><span class="p">:</span>
    <span class="n">phát</span> <span class="n">âm</span> <span class="n">thanh</span> <span class="s2">&quot;beep&quot;</span>
</pre></div>
</div>
<blockquote>
<div><p>Kéo các khối trong nhóm <strong>Input → Button → PORT</strong> để sử dụng nút ngoài.</p>
</div></blockquote>
</section>
</section>
<hr class="docutils" />
<section id="vi-du-python">
<h2>Ví dụ Python<a class="headerlink" href="#vi-du-python" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban-bat-tat-led-khi-nhan-nut">
<h3>Ví dụ cơ bản – Bật tắt LED khi nhấn nút<a class="headerlink" href="#vi-du-co-ban-bat-tat-led-khi-nhan-nut" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">Button</span><span class="w"> </span><span class="kn">import</span> <span class="n">Button</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">led</span> <span class="o">=</span> <span class="n">bee</span><span class="o">.</span><span class="n">led1</span>
<span class="n">ext_btn</span> <span class="o">=</span> <span class="n">Button</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>

<span class="k">def</span><span class="w"> </span><span class="nf">toggle_led</span><span class="p">(</span><span class="n">pin</span><span class="p">):</span>
    <span class="n">led</span><span class="o">.</span><span class="n">toggle</span><span class="p">()</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;LED đổi trạng thái!&quot;</span><span class="p">)</span>

<span class="n">ext_btn</span><span class="o">.</span><span class="n">on_press</span><span class="p">(</span><span class="n">toggle_led</span><span class="p">)</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>
</pre></div>
</div>
</section>
<hr class="docutils" />
<section id="vi-du-nang-cao-tao-menu-dieu-huong-bang-2-nut">
<h3>Ví dụ nâng cao – Tạo menu điều hướng bằng 2 nút<a class="headerlink" href="#vi-du-nang-cao-tao-menu-dieu-huong-bang-2-nut" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">Button</span><span class="w"> </span><span class="kn">import</span> <span class="n">Button</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">oled</span> <span class="o">=</span> <span class="n">bee</span><span class="o">.</span><span class="n">oled</span>
<span class="n">btn_up</span> <span class="o">=</span> <span class="n">Button</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>
<span class="n">btn_down</span> <span class="o">=</span> <span class="n">Button</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT2</span><span class="p">)</span>

<span class="n">menu</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;LED Control&quot;</span><span class="p">,</span> <span class="s2">&quot;Motor Control&quot;</span><span class="p">,</span> <span class="s2">&quot;Sensor Read&quot;</span><span class="p">,</span> <span class="s2">&quot;Settings&quot;</span><span class="p">]</span>
<span class="n">current</span> <span class="o">=</span> <span class="mi">0</span>

<span class="k">def</span><span class="w"> </span><span class="nf">show_menu</span><span class="p">():</span>
    <span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">item</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">menu</span><span class="p">):</span>
        <span class="n">prefix</span> <span class="o">=</span> <span class="s2">&quot;&gt; &quot;</span> <span class="k">if</span> <span class="n">i</span> <span class="o">==</span> <span class="n">current</span> <span class="k">else</span> <span class="s2">&quot;  &quot;</span>
        <span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">prefix</span><span class="si">}{</span><span class="n">item</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="n">i</span><span class="p">)</span>

<span class="k">def</span><span class="w"> </span><span class="nf">move_up</span><span class="p">(</span><span class="n">pin</span><span class="p">):</span>
    <span class="k">global</span> <span class="n">current</span>
    <span class="n">current</span> <span class="o">=</span> <span class="p">(</span><span class="n">current</span> <span class="o">-</span> <span class="mi">1</span><span class="p">)</span> <span class="o">%</span> <span class="nb">len</span><span class="p">(</span><span class="n">menu</span><span class="p">)</span>
    <span class="n">show_menu</span><span class="p">()</span>

<span class="k">def</span><span class="w"> </span><span class="nf">move_down</span><span class="p">(</span><span class="n">pin</span><span class="p">):</span>
    <span class="k">global</span> <span class="n">current</span>
    <span class="n">current</span> <span class="o">=</span> <span class="p">(</span><span class="n">current</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">%</span> <span class="nb">len</span><span class="p">(</span><span class="n">menu</span><span class="p">)</span>
    <span class="n">show_menu</span><span class="p">()</span>

<span class="n">btn_up</span><span class="o">.</span><span class="n">on_press</span><span class="p">(</span><span class="n">move_up</span><span class="p">)</span>
<span class="n">btn_down</span><span class="o">.</span><span class="n">on_press</span><span class="p">(</span><span class="n">move_down</span><span class="p">)</span>

<span class="n">show_menu</span><span class="p">()</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<hr class="docutils" />
<section id="giai-thich-ma">
<h2>Giải thích mã<a class="headerlink" href="#giai-thich-ma" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">Button(PORTx)</span></code></p></td>
<td><p>Tạo đối tượng nút gắn ngoài tại cổng PORTx</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">on_press(callback)</span></code></p></td>
<td><p>Gọi hàm khi người dùng nhấn nút</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">on_long_press(callback,</span> <span class="pre">t)</span></code></p></td>
<td><p>Gọi hàm khi nhấn giữ trong t mili-giây</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">is_pressed()</span></code></p></td>
<td><p>Trả về <code class="docutils literal notranslate"><span class="pre">True</span></code> nếu nút đang được nhấn</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">debounce</span></code></p></td>
<td><p>Tránh nhiễu tín hiệu nhấn nhiều lần</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="bai-tap-mo-rong">
<h2>Bài tập mở rộng<a class="headerlink" href="#bai-tap-mo-rong" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p><strong>Đếm số lần nhấn</strong>
Hiển thị số lần nhấn nút trên OLED.</p></li>
<li><p><strong>Game phản xạ nhanh</strong>
LED sáng ngẫu nhiên → nhấn nút càng nhanh càng tốt!</p></li>
<li><p><strong>Khóa bảo mật đơn giản</strong>
Mở LED chỉ khi nhập đúng chuỗi nhấn 1–0–1 bằng 3 nút.</p></li>
</ol>
</section>
<hr class="docutils" />
<section id="loi-thuong-gap">
<h2>Lỗi thường gặp<a class="headerlink" href="#loi-thuong-gap" title="Link to this heading"></a></h2>
<div class="warning admonition">
<p class="admonition-title">Nút không phản hồi</p>
<p><strong>Nguyên nhân:</strong> Pull-up/pull-down không đúng hoặc debounce quá thấp
<strong>Giải pháp:</strong></p>
<ul class="simple">
<li><p>Đặt <code class="docutils literal notranslate"><span class="pre">PULL_UP=True</span></code> khi khởi tạo</p></li>
<li><p>Tăng debounce lên 200–300ms</p></li>
<li><p>Kiểm tra dây nối và chân PORT</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Callback bị gọi nhiều lần</p>
<p><strong>Nguyên nhân:</strong> Nhiễu phần cứng hoặc debounce ngắn
<strong>Giải pháp:</strong></p>
<ul class="simple">
<li><p>Tăng thời gian debounce</p></li>
<li><p>Dùng tụ lọc 10nF–100nF giữa tín hiệu và GND</p></li>
<li><p>Đảm bảo dây tín hiệu ngắn và ổn định</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Long press không hoạt động</p>
<p><strong>Nguyên nhân:</strong> Thời gian nhấn giữ quá ngắn
<strong>Giải pháp:</strong></p>
<ul class="simple">
<li><p>Dùng thời gian ≥ 1000ms</p></li>
<li><p>Kiểm tra callback có đúng cú pháp (có đối số <code class="docutils literal notranslate"><span class="pre">pin</span></code>)</p></li>
</ul>
</div>
</section>
<hr class="docutils" />
<section id="tai-nguyen-tham-khao">
<h2>Tài nguyên tham khảo<a class="headerlink" href="#tai-nguyen-tham-khao" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/docs/bee-board">BeE Board API Documentation</a></p></li>
<li><p><a class="reference external" href="https://docs.micropython.org/en/latest/library/machine.Pin.html">MicroPython machine.Pin</a></p></li>
<li><p><a class="reference external" href="https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/peripherals/gpio.html">ESP32 GPIO Reference</a></p></li>
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">BeE IDE Online</a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="1.index.html" class="btn btn-neutral float-left" title="Giới thiệu" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="color-detect.html" class="btn btn-neutral float-right" title="BeeColorDetect" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>