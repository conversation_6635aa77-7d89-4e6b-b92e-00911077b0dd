

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>BeeColorDetect &mdash; Tài <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="Module DHT11" href="dht11.html" />
    <link rel="prev" title="Module Nút Nhấn" href="button.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">BeeColorDetect</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thong-so-ky-thuat">Thông số kỹ thuật</a></li>
<li class="toctree-l2"><a class="reference internal" href="#giao-dien-lap-trinh">Giao diện lập trình</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#khoi-tao">Khởi tạo</a></li>
<li class="toctree-l3"><a class="reference internal" href="#cau-hinh-cam-bien">Cấu hình cảm biến</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#gain-thiet-lap-do-khuech-dai"><code class="docutils literal notranslate"><span class="pre">gain</span></code> - Thiết lập độ khuếch đại</a></li>
<li class="toctree-l4"><a class="reference internal" href="#integration-time-thiet-lap-thoi-gian-tich-hop"><code class="docutils literal notranslate"><span class="pre">integration_time</span></code> - Thiết lập thời gian tích hợp</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#doc-du-lieu-mau">Đọc dữ liệu màu</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#get-all-colors-in-rgb-doc-tat-ca-kenh-rgb"><code class="docutils literal notranslate"><span class="pre">get_all_colors_in_rgb()</span></code> - Đọc tất cả kênh RGB</a></li>
<li class="toctree-l4"><a class="reference internal" href="#get-color-nhan-dien-mau-co-ban"><code class="docutils literal notranslate"><span class="pre">get_color()</span></code> - Nhận diện màu cơ bản</a></li>
<li class="toctree-l4"><a class="reference internal" href="#is-color-color-name-kiem-tra-mau-cu-the"><code class="docutils literal notranslate"><span class="pre">is_color(color_name)</span></code> - Kiểm tra màu cụ thể</a></li>
<li class="toctree-l4"><a class="reference internal" href="#html-hex-chuyen-doi-sang-ma-hex-html"><code class="docutils literal notranslate"><span class="pre">html_hex()</span></code> - Chuyển đổi sang mã hex HTML</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-blockly">Ví dụ Blockly</a></li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-python">Ví dụ Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban-nhan-dien-mau-don-gian">Ví dụ cơ bản - Nhận diện màu đơn giản</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao-robot-phan-loai-mau">Ví dụ nâng cao - Robot phân loại màu</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#giai-thich-ma">Giải thích mã</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban">Ví dụ cơ bản:</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao">Ví dụ nâng cao:</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#bai-tap-mo-rong">Bài tập mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-thuong-gap">Lỗi thường gặp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-nguyen-tham-khao">Tài nguyên tham khảo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">BeeColorDetect</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/4.extensions/color-detect.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="beecolordetect">
<h1>BeeColorDetect<a class="headerlink" href="#beecolordetect" title="Link to this heading"></a></h1>
<section id="gioi-thieu">
<h2>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p>BeeColorDetect là module cảm biến màu sắc sử dụng chip TCS34725 với độ chính xác cao. Module có thể nhận diện màu sắc RGB, chuyển đổi sang HSV và xác định các màu cơ bản. Chip TCS34725 tích hợp LED trắng để chiếu sáng mẫu, giúp đo màu chính xác trong nhiều điều kiện ánh sáng khác nhau.</p>
<p>Module được thiết kế để dễ sử dụng với giao diện I2C, phù hợp cho các dự án robotics, automation và giáo dục STEM. Đặc biệt hữu ích cho robot phân loại màu, game tương tác và các ứng dụng IoT.</p>
<p><strong>Ứng dụng thực tế:</strong></p>
<ul class="simple">
<li><p>Robot phân loại vật phẩm theo màu</p></li>
<li><p>Hệ thống kiểm tra chất lượng sản phẩm</p></li>
<li><p>Game tương tác với màu sắc</p></li>
<li><p>Máy trộn màu tự động</p></li>
<li><p>Hệ thống nhận diện màu cho người khiếm thị</p></li>
<li><p>Dự án nghệ thuật tương tác</p></li>
<li><p>Cảm biến môi trường (đo độ đục nước)</p></li>
</ul>
</section>
<section id="thong-so-ky-thuat">
<h2>Thông số kỹ thuật<a class="headerlink" href="#thong-so-ky-thuat" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thông số</p></th>
<th class="head"><p>Giá trị</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Chip cảm biến</p></td>
<td><p>TCS34725</p></td>
</tr>
<tr class="row-odd"><td><p>Giao tiếp</p></td>
<td><p>I2C</p></td>
</tr>
<tr class="row-even"><td><p>Địa chỉ I2C</p></td>
<td><p>0x29 (mặc định)</p></td>
</tr>
<tr class="row-odd"><td><p>Điện áp hoạt động</p></td>
<td><p>3.3V</p></td>
</tr>
<tr class="row-even"><td><p>Dòng tiêu thụ</p></td>
<td><p>65μA (hoạt động), 2.5μA (sleep)</p></td>
</tr>
<tr class="row-odd"><td><p>Độ phân giải ADC</p></td>
<td><p>16-bit</p></td>
</tr>
<tr class="row-even"><td><p>Kênh màu</p></td>
<td><p>Red, Green, Blue, Clear</p></td>
</tr>
<tr class="row-odd"><td><p>LED tích hợp</p></td>
<td><p>LED trắng 3000K</p></td>
</tr>
<tr class="row-even"><td><p>Gain</p></td>
<td><p>1x, 4x, 16x, 60x (có thể điều chỉnh)</p></td>
</tr>
<tr class="row-odd"><td><p>Thời gian tích hợp</p></td>
<td><p>2.4ms - 614ms</p></td>
</tr>
<tr class="row-even"><td><p>Phạm vi nhiệt độ</p></td>
<td><p>-30°C đến +70°C</p></td>
</tr>
<tr class="row-odd"><td><p>Kích thước</p></td>
<td><p>20mm x 16mm</p></td>
</tr>
</tbody>
</table>
</section>
<section id="giao-dien-lap-trinh">
<h2>Giao diện lập trình<a class="headerlink" href="#giao-dien-lap-trinh" title="Link to this heading"></a></h2>
<section id="khoi-tao">
<h3>Khởi tạo<a class="headerlink" href="#khoi-tao" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeColorDetect</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeColorDetect</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>

<span class="c1"># Khởi tạo với cổng I2C mặc định</span>
<span class="n">color_sensor</span> <span class="o">=</span> <span class="n">BeeColorDetect</span><span class="p">()</span>

<span class="c1"># Khởi tạo với BeeBrain</span>
<span class="n">color_sensor</span> <span class="o">=</span> <span class="n">BeeColorDetect</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">i2c</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="cau-hinh-cam-bien">
<h3>Cấu hình cảm biến<a class="headerlink" href="#cau-hinh-cam-bien" title="Link to this heading"></a></h3>
<section id="gain-thiet-lap-do-khuech-dai">
<h4><code class="docutils literal notranslate"><span class="pre">gain</span></code> - Thiết lập độ khuếch đại<a class="headerlink" href="#gain-thiet-lap-do-khuech-dai" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Thiết lập gain (1, 4, 16, 60)</span>
<span class="n">color_sensor</span><span class="o">.</span><span class="n">gain</span> <span class="o">=</span> <span class="mi">16</span>  <span class="c1"># Gain 16x cho độ nhạy cao</span>

<span class="c1"># Đọc gain hiện tại</span>
<span class="n">current_gain</span> <span class="o">=</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">gain</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Current gain: </span><span class="si">{</span><span class="n">current_gain</span><span class="si">}</span><span class="s2">x&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="integration-time-thiet-lap-thoi-gian-tich-hop">
<h4><code class="docutils literal notranslate"><span class="pre">integration_time</span></code> - Thiết lập thời gian tích hợp<a class="headerlink" href="#integration-time-thiet-lap-thoi-gian-tich-hop" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Thiết lập thời gian tích hợp (ms)</span>
<span class="n">color_sensor</span><span class="o">.</span><span class="n">integration_time</span> <span class="o">=</span> <span class="mi">50</span>  <span class="c1"># 50ms</span>

<span class="c1"># Đọc thời gian tích hợp hiện tại</span>
<span class="n">current_time</span> <span class="o">=</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">integration_time</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Integration time: </span><span class="si">{</span><span class="n">current_time</span><span class="si">}</span><span class="s2">ms&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="doc-du-lieu-mau">
<h3>Đọc dữ liệu màu<a class="headerlink" href="#doc-du-lieu-mau" title="Link to this heading"></a></h3>
<section id="get-all-colors-in-rgb-doc-tat-ca-kenh-rgb">
<h4><code class="docutils literal notranslate"><span class="pre">get_all_colors_in_rgb()</span></code> - Đọc tất cả kênh RGB<a class="headerlink" href="#get-all-colors-in-rgb-doc-tat-ca-kenh-rgb" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Đọc giá trị RGB và Clear</span>
<span class="n">red</span><span class="p">,</span> <span class="n">green</span><span class="p">,</span> <span class="n">blue</span><span class="p">,</span> <span class="n">clear</span> <span class="o">=</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">get_all_colors_in_rgb</span><span class="p">()</span>

<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Red: </span><span class="si">{</span><span class="n">red</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Green: </span><span class="si">{</span><span class="n">green</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Blue: </span><span class="si">{</span><span class="n">blue</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Clear: </span><span class="si">{</span><span class="n">clear</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="get-color-nhan-dien-mau-co-ban">
<h4><code class="docutils literal notranslate"><span class="pre">get_color()</span></code> - Nhận diện màu cơ bản<a class="headerlink" href="#get-color-nhan-dien-mau-co-ban" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Nhận diện màu cơ bản</span>
<span class="n">color_name</span> <span class="o">=</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">get_color</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Detected color: </span><span class="si">{</span><span class="n">color_name</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="c1"># Các màu có thể nhận diện:</span>
<span class="c1"># &quot;RED&quot;, &quot;GREEN&quot;, &quot;BLUE&quot;, &quot;YELLOW&quot;, &quot;CYAN&quot;, &quot;MAGENTA&quot;, &quot;WHITE&quot;, &quot;BLACK&quot;</span>
</pre></div>
</div>
</section>
<section id="is-color-color-name-kiem-tra-mau-cu-the">
<h4><code class="docutils literal notranslate"><span class="pre">is_color(color_name)</span></code> - Kiểm tra màu cụ thể<a class="headerlink" href="#is-color-color-name-kiem-tra-mau-cu-the" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Kiểm tra có phải màu đỏ không</span>
<span class="k">if</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">is_color</span><span class="p">(</span><span class="s2">&quot;RED&quot;</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;This is RED!&quot;</span><span class="p">)</span>

<span class="c1"># Kiểm tra màu xanh lá</span>
<span class="k">if</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">is_color</span><span class="p">(</span><span class="s2">&quot;GREEN&quot;</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;This is GREEN!&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="html-hex-chuyen-doi-sang-ma-hex-html">
<h4><code class="docutils literal notranslate"><span class="pre">html_hex()</span></code> - Chuyển đổi sang mã hex HTML<a class="headerlink" href="#html-hex-chuyen-doi-sang-ma-hex-html" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Lấy mã màu hex (ví dụ: &quot;#FF0000&quot; cho đỏ)</span>
<span class="n">hex_color</span> <span class="o">=</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">html_hex</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;HTML color: </span><span class="si">{</span><span class="n">hex_color</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="vi-du-blockly">
<h2>Ví dụ Blockly<a class="headerlink" href="#vi-du-blockly" title="Link to this heading"></a></h2>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">when</span> <span class="n">program</span> <span class="n">starts</span><span class="p">:</span>
    <span class="nb">set</span> <span class="n">color_sensor</span> <span class="n">to</span> <span class="n">BeeColorDetect</span>
    <span class="nb">set</span> <span class="n">color_sensor</span> <span class="n">gain</span> <span class="n">to</span> <span class="mi">16</span>
    <span class="nb">set</span> <span class="n">color_sensor</span> <span class="n">integration_time</span> <span class="n">to</span> <span class="mi">50</span>

<span class="n">forever</span><span class="p">:</span>
    <span class="nb">set</span> <span class="n">detected_color</span> <span class="n">to</span> <span class="n">color_sensor</span> <span class="n">get_color</span>

    <span class="k">if</span> <span class="n">detected_color</span> <span class="n">equals</span> <span class="s2">&quot;RED&quot;</span><span class="p">:</span>
        <span class="nb">set</span> <span class="n">LED</span> <span class="n">to</span> <span class="n">RED</span> <span class="n">color</span>
        <span class="n">play</span> <span class="n">tone</span> <span class="n">C4</span>
    <span class="k">else</span> <span class="k">if</span> <span class="n">detected_color</span> <span class="n">equals</span> <span class="s2">&quot;GREEN&quot;</span><span class="p">:</span>
        <span class="nb">set</span> <span class="n">LED</span> <span class="n">to</span> <span class="n">GREEN</span> <span class="n">color</span>
        <span class="n">play</span> <span class="n">tone</span> <span class="n">E4</span>
    <span class="k">else</span> <span class="k">if</span> <span class="n">detected_color</span> <span class="n">equals</span> <span class="s2">&quot;BLUE&quot;</span><span class="p">:</span>
        <span class="nb">set</span> <span class="n">LED</span> <span class="n">to</span> <span class="n">BLUE</span> <span class="n">color</span>
        <span class="n">play</span> <span class="n">tone</span> <span class="n">G4</span>

    <span class="n">wait</span> <span class="mf">0.5</span> <span class="n">seconds</span>
</pre></div>
</div>
</section>
<section id="vi-du-python">
<h2>Ví dụ Python<a class="headerlink" href="#vi-du-python" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban-nhan-dien-mau-don-gian">
<h3>Ví dụ cơ bản - Nhận diện màu đơn giản<a class="headerlink" href="#vi-du-co-ban-nhan-dien-mau-don-gian" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeColorDetect</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeColorDetect</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">def</span><span class="w"> </span><span class="nf">setup</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Khởi tạo cảm biến màu&quot;&quot;&quot;</span>
    <span class="k">global</span> <span class="n">color_sensor</span>

    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Khởi tạo cảm biến</span>
        <span class="n">color_sensor</span> <span class="o">=</span> <span class="n">BeeColorDetect</span><span class="p">()</span>

        <span class="c1"># Cấu hình tối ưu</span>
        <span class="n">color_sensor</span><span class="o">.</span><span class="n">gain</span> <span class="o">=</span> <span class="mi">16</span>
        <span class="n">color_sensor</span><span class="o">.</span><span class="n">integration_time</span> <span class="o">=</span> <span class="mi">50</span>

        <span class="c1"># Test cảm biến</span>
        <span class="n">red</span><span class="p">,</span> <span class="n">green</span><span class="p">,</span> <span class="n">blue</span><span class="p">,</span> <span class="n">clear</span> <span class="o">=</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">get_all_colors_in_rgb</span><span class="p">()</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Color Sensor&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Ready!&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;R:</span><span class="si">{</span><span class="n">red</span><span class="si">}</span><span class="s2"> G:</span><span class="si">{</span><span class="n">green</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;B:</span><span class="si">{</span><span class="n">blue</span><span class="si">}</span><span class="s2"> C:</span><span class="si">{</span><span class="n">clear</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">45</span><span class="p">)</span>

        <span class="k">return</span> <span class="kc">True</span>

    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Sensor Error!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)[:</span><span class="mi">20</span><span class="p">],</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
        <span class="k">return</span> <span class="kc">False</span>

<span class="k">def</span><span class="w"> </span><span class="nf">simple_color_detection</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Nhận diện màu cơ bản&quot;&quot;&quot;</span>
    <span class="n">color_count</span> <span class="o">=</span> <span class="p">{</span><span class="s2">&quot;RED&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;GREEN&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;BLUE&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;OTHER&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">}</span>

    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Đọc màu</span>
            <span class="n">detected_color</span> <span class="o">=</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">get_color</span><span class="p">()</span>
            <span class="n">hex_color</span> <span class="o">=</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">html_hex</span><span class="p">()</span>

            <span class="c1"># Đếm màu</span>
            <span class="k">if</span> <span class="n">detected_color</span> <span class="ow">in</span> <span class="n">color_count</span><span class="p">:</span>
                <span class="n">color_count</span><span class="p">[</span><span class="n">detected_color</span><span class="p">]</span> <span class="o">+=</span> <span class="mi">1</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">color_count</span><span class="p">[</span><span class="s2">&quot;OTHER&quot;</span><span class="p">]</span> <span class="o">+=</span> <span class="mi">1</span>

            <span class="c1"># Hiển thị kết quả</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Color Detection&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Color: </span><span class="si">{</span><span class="n">detected_color</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Hex: </span><span class="si">{</span><span class="n">hex_color</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Count: </span><span class="si">{</span><span class="n">color_count</span><span class="p">[</span><span class="n">detected_color</span><span class="p">]</span><span class="w"> </span><span class="k">if</span><span class="w"> </span><span class="n">detected_color</span><span class="w"> </span><span class="ow">in</span><span class="w"> </span><span class="n">color_count</span><span class="w"> </span><span class="k">else</span><span class="w"> </span><span class="n">color_count</span><span class="p">[</span><span class="s1">&#39;OTHER&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">45</span><span class="p">)</span>

            <span class="c1"># LED phản hồi</span>
            <span class="k">if</span> <span class="n">detected_color</span> <span class="o">==</span> <span class="s2">&quot;RED&quot;</span><span class="p">:</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="s2">&quot;C4&quot;</span><span class="p">,</span> <span class="mf">0.1</span><span class="p">)</span>
            <span class="k">elif</span> <span class="n">detected_color</span> <span class="o">==</span> <span class="s2">&quot;GREEN&quot;</span><span class="p">:</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="s2">&quot;E4&quot;</span><span class="p">,</span> <span class="mf">0.1</span><span class="p">)</span>
            <span class="k">elif</span> <span class="n">detected_color</span> <span class="o">==</span> <span class="s2">&quot;BLUE&quot;</span><span class="p">:</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="s2">&quot;G4&quot;</span><span class="p">,</span> <span class="mf">0.1</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">50</span><span class="p">,</span> <span class="mi">50</span><span class="p">,</span> <span class="mi">50</span><span class="p">)</span>

            <span class="c1"># Kiểm tra nút thoát</span>
            <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
                <span class="k">break</span>

            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Read Error!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

<span class="k">def</span><span class="w"> </span><span class="nf">interactive_color_test</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Test màu tương tác&quot;&quot;&quot;</span>
    <span class="n">test_colors</span> <span class="o">=</span> <span class="p">[</span><span class="s2">&quot;RED&quot;</span><span class="p">,</span> <span class="s2">&quot;GREEN&quot;</span><span class="p">,</span> <span class="s2">&quot;BLUE&quot;</span><span class="p">,</span> <span class="s2">&quot;YELLOW&quot;</span><span class="p">,</span> <span class="s2">&quot;WHITE&quot;</span><span class="p">,</span> <span class="s2">&quot;BLACK&quot;</span><span class="p">]</span>
    <span class="n">current_test</span> <span class="o">=</span> <span class="mi">0</span>

    <span class="k">while</span> <span class="n">current_test</span> <span class="o">&lt;</span> <span class="nb">len</span><span class="p">(</span><span class="n">test_colors</span><span class="p">):</span>
        <span class="n">target_color</span> <span class="o">=</span> <span class="n">test_colors</span><span class="p">[</span><span class="n">current_test</span><span class="p">]</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Color Test&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Show: </span><span class="si">{</span><span class="n">target_color</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A: Check&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;B: Skip&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">45</span><span class="p">)</span>

        <span class="c1"># Chờ người dùng</span>
        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_A</span><span class="p">):</span>
                <span class="c1"># Kiểm tra màu</span>
                <span class="n">detected</span> <span class="o">=</span> <span class="n">color_sensor</span><span class="o">.</span><span class="n">get_color</span><span class="p">()</span>

                <span class="k">if</span> <span class="n">detected</span> <span class="o">==</span> <span class="n">target_color</span><span class="p">:</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;CORRECT!&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4&quot;</span><span class="p">)</span>
                    <span class="n">current_test</span> <span class="o">+=</span> <span class="mi">1</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;WRONG!&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Got: </span><span class="si">{</span><span class="n">detected</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="s2">&quot;C3&quot;</span><span class="p">,</span> <span class="mf">0.5</span><span class="p">)</span>

                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
                <span class="k">break</span>

            <span class="k">elif</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
                <span class="n">current_test</span> <span class="o">+=</span> <span class="mi">1</span>
                <span class="k">break</span>

            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

    <span class="c1"># Hoàn thành test</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Test Complete!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>

<span class="c1"># Chạy chương trình</span>
<span class="k">if</span> <span class="n">setup</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;1. Simple Color Detection&quot;</span><span class="p">)</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;2. Interactive Color Test&quot;</span><span class="p">)</span>

    <span class="c1"># Demo tự động</span>
    <span class="n">simple_color_detection</span><span class="p">()</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">interactive_color_test</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="vi-du-nang-cao-robot-phan-loai-mau">
<h3>Ví dụ nâng cao - Robot phân loại màu<a class="headerlink" href="#vi-du-nang-cao-robot-phan-loai-mau" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeColorDetect</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeColorDetect</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">json</span>

<span class="k">class</span><span class="w"> </span><span class="nc">ColorSortingRobot</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="c1"># Khởi tạo cảm biến màu</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">color_sensor</span> <span class="o">=</span> <span class="n">BeeColorDetect</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">color_sensor</span><span class="o">.</span><span class="n">gain</span> <span class="o">=</span> <span class="mi">16</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">color_sensor</span><span class="o">.</span><span class="n">integration_time</span> <span class="o">=</span> <span class="mi">50</span>

        <span class="c1"># Thống kê phân loại</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">color_stats</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;RED&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;GREEN&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;BLUE&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>
            <span class="s2">&quot;YELLOW&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;WHITE&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;BLACK&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span> <span class="s2">&quot;OTHER&quot;</span><span class="p">:</span> <span class="mi">0</span>
        <span class="p">}</span>

        <span class="c1"># Cấu hình servo cho phân loại</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">servo_positions</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;RED&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">,</span>      <span class="c1"># Góc 0° - hộp đỏ</span>
            <span class="s2">&quot;GREEN&quot;</span><span class="p">:</span> <span class="mi">45</span><span class="p">,</span>   <span class="c1"># Góc 45° - hộp xanh lá</span>
            <span class="s2">&quot;BLUE&quot;</span><span class="p">:</span> <span class="mi">90</span><span class="p">,</span>    <span class="c1"># Góc 90° - hộp xanh dương</span>
            <span class="s2">&quot;YELLOW&quot;</span><span class="p">:</span> <span class="mi">135</span><span class="p">,</span> <span class="c1"># Góc 135° - hộp vàng</span>
            <span class="s2">&quot;OTHER&quot;</span><span class="p">:</span> <span class="mi">180</span>   <span class="c1"># Góc 180° - hộp khác</span>
        <span class="p">}</span>

        <span class="c1"># Trạng thái robot</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">is_running</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">total_sorted</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">current_item</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">setup</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Khởi tạo hệ thống&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Test cảm biến màu</span>
            <span class="n">red</span><span class="p">,</span> <span class="n">green</span><span class="p">,</span> <span class="n">blue</span><span class="p">,</span> <span class="n">clear</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">color_sensor</span><span class="o">.</span><span class="n">get_all_colors_in_rgb</span><span class="p">()</span>

            <span class="c1"># Test servo phân loại</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">servo</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">90</span><span class="p">)</span>  <span class="c1"># Servo về vị trí giữa</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>

            <span class="c1"># Hiển thị thông tin khởi tạo</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Color Sorting&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Robot Ready!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;RGB: </span><span class="si">{</span><span class="n">red</span><span class="si">}</span><span class="s2">,</span><span class="si">{</span><span class="n">green</span><span class="si">}</span><span class="s2">,</span><span class="si">{</span><span class="n">blue</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A:Start B:Stop&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">45</span><span class="p">)</span>

            <span class="c1"># LED sẵn sàng</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4&quot;</span><span class="p">)</span>

            <span class="k">return</span> <span class="kc">True</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Setup Error!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)[:</span><span class="mi">20</span><span class="p">],</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">False</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">detect_and_classify_color</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Phát hiện và phân loại màu&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Đọc màu nhiều lần để chính xác</span>
            <span class="n">color_readings</span> <span class="o">=</span> <span class="p">[]</span>
            <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">):</span>
                <span class="n">color</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">color_sensor</span><span class="o">.</span><span class="n">get_color</span><span class="p">()</span>
                <span class="n">color_readings</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">color</span><span class="p">)</span>
                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

            <span class="c1"># Lấy màu xuất hiện nhiều nhất</span>
            <span class="n">most_common</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="nb">set</span><span class="p">(</span><span class="n">color_readings</span><span class="p">),</span> <span class="n">key</span><span class="o">=</span><span class="n">color_readings</span><span class="o">.</span><span class="n">count</span><span class="p">)</span>

            <span class="c1"># Lấy giá trị RGB để hiển thị</span>
            <span class="n">red</span><span class="p">,</span> <span class="n">green</span><span class="p">,</span> <span class="n">blue</span><span class="p">,</span> <span class="n">clear</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">color_sensor</span><span class="o">.</span><span class="n">get_all_colors_in_rgb</span><span class="p">()</span>
            <span class="n">hex_color</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">color_sensor</span><span class="o">.</span><span class="n">html_hex</span><span class="p">()</span>

            <span class="k">return</span> <span class="p">{</span>
                <span class="s2">&quot;color&quot;</span><span class="p">:</span> <span class="n">most_common</span><span class="p">,</span>
                <span class="s2">&quot;rgb&quot;</span><span class="p">:</span> <span class="p">(</span><span class="n">red</span><span class="p">,</span> <span class="n">green</span><span class="p">,</span> <span class="n">blue</span><span class="p">),</span>
                <span class="s2">&quot;hex&quot;</span><span class="p">:</span> <span class="n">hex_color</span><span class="p">,</span>
                <span class="s2">&quot;confidence&quot;</span><span class="p">:</span> <span class="n">color_readings</span><span class="o">.</span><span class="n">count</span><span class="p">(</span><span class="n">most_common</span><span class="p">)</span> <span class="o">/</span> <span class="nb">len</span><span class="p">(</span><span class="n">color_readings</span><span class="p">)</span>
            <span class="p">}</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="k">return</span> <span class="p">{</span><span class="s2">&quot;color&quot;</span><span class="p">:</span> <span class="s2">&quot;ERROR&quot;</span><span class="p">,</span> <span class="s2">&quot;rgb&quot;</span><span class="p">:</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span> <span class="s2">&quot;hex&quot;</span><span class="p">:</span> <span class="s2">&quot;#000000&quot;</span><span class="p">,</span> <span class="s2">&quot;confidence&quot;</span><span class="p">:</span> <span class="mi">0</span><span class="p">}</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">sort_item</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">color_info</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Phân loại vật phẩm&quot;&quot;&quot;</span>
        <span class="n">color</span> <span class="o">=</span> <span class="n">color_info</span><span class="p">[</span><span class="s2">&quot;color&quot;</span><span class="p">]</span>

        <span class="c1"># Xác định vị trí servo</span>
        <span class="k">if</span> <span class="n">color</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">servo_positions</span><span class="p">:</span>
            <span class="n">target_position</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">servo_positions</span><span class="p">[</span><span class="n">color</span><span class="p">]</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">target_position</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">servo_positions</span><span class="p">[</span><span class="s2">&quot;OTHER&quot;</span><span class="p">]</span>
            <span class="n">color</span> <span class="o">=</span> <span class="s2">&quot;OTHER&quot;</span>

        <span class="c1"># Di chuyển servo để phân loại</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">servo</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">target_position</span><span class="p">)</span>

        <span class="c1"># Cập nhật thống kê</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">color_stats</span><span class="p">[</span><span class="n">color</span><span class="p">]</span> <span class="o">+=</span> <span class="mi">1</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">total_sorted</span> <span class="o">+=</span> <span class="mi">1</span>

        <span class="c1"># Hiệu ứng LED theo màu</span>
        <span class="n">rgb</span> <span class="o">=</span> <span class="n">color_info</span><span class="p">[</span><span class="s2">&quot;rgb&quot;</span><span class="p">]</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="n">rgb</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">//</span><span class="mi">10</span><span class="p">,</span> <span class="n">rgb</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span><span class="o">//</span><span class="mi">10</span><span class="p">,</span> <span class="n">rgb</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span><span class="o">//</span><span class="mi">10</span><span class="p">)</span>

        <span class="c1"># Âm thanh xác nhận</span>
        <span class="k">if</span> <span class="n">color</span> <span class="o">==</span> <span class="s2">&quot;RED&quot;</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="s2">&quot;C4&quot;</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">color</span> <span class="o">==</span> <span class="s2">&quot;GREEN&quot;</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="s2">&quot;E4&quot;</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">color</span> <span class="o">==</span> <span class="s2">&quot;BLUE&quot;</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="s2">&quot;G4&quot;</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">)</span>
        <span class="k">elif</span> <span class="n">color</span> <span class="o">==</span> <span class="s2">&quot;YELLOW&quot;</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="s2">&quot;A4&quot;</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="s2">&quot;F4&quot;</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">)</span>

        <span class="c1"># Chờ vật phẩm rơi vào hộp</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

        <span class="c1"># Trở về vị trí chờ</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">servo</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">90</span><span class="p">)</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>

        <span class="k">return</span> <span class="n">color</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">display_status</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">color_info</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Hiển thị trạng thái hệ thống&quot;&quot;&quot;</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>

        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_running</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;SORTING...&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">color_info</span><span class="p">:</span>
                <span class="n">color</span> <span class="o">=</span> <span class="n">color_info</span><span class="p">[</span><span class="s2">&quot;color&quot;</span><span class="p">]</span>
                <span class="n">confidence</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">color_info</span><span class="p">[</span><span class="s2">&quot;confidence&quot;</span><span class="p">]</span> <span class="o">*</span> <span class="mi">100</span><span class="p">)</span>

                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Color: </span><span class="si">{</span><span class="n">color</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Conf: </span><span class="si">{</span><span class="n">confidence</span><span class="si">}</span><span class="s2">%&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Total: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">total_sorted</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">45</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Waiting item...&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Sorted: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">total_sorted</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">35</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;STOPPED&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Total: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">total_sorted</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A:Start B:Stats&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">40</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">show_statistics</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Hiển thị thống kê chi tiết&quot;&quot;&quot;</span>
        <span class="n">stats_pages</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="c1"># Trang 1: Tổng quan</span>
        <span class="n">page1</span> <span class="o">=</span> <span class="p">[</span>
            <span class="s2">&quot;=== STATISTICS ===&quot;</span><span class="p">,</span>
            <span class="sa">f</span><span class="s2">&quot;Total sorted: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">total_sorted</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="sa">f</span><span class="s2">&quot;Red: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">color_stats</span><span class="p">[</span><span class="s1">&#39;RED&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="sa">f</span><span class="s2">&quot;Green: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">color_stats</span><span class="p">[</span><span class="s1">&#39;GREEN&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span>
        <span class="p">]</span>
        <span class="n">stats_pages</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">page1</span><span class="p">)</span>

        <span class="c1"># Trang 2: Chi tiết</span>
        <span class="n">page2</span> <span class="o">=</span> <span class="p">[</span>
            <span class="s2">&quot;=== DETAILS ===&quot;</span><span class="p">,</span>
            <span class="sa">f</span><span class="s2">&quot;Blue: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">color_stats</span><span class="p">[</span><span class="s1">&#39;BLUE&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="sa">f</span><span class="s2">&quot;Yellow: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">color_stats</span><span class="p">[</span><span class="s1">&#39;YELLOW&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span>
            <span class="sa">f</span><span class="s2">&quot;Other: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">color_stats</span><span class="p">[</span><span class="s1">&#39;OTHER&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span>
        <span class="p">]</span>
        <span class="n">stats_pages</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">page2</span><span class="p">)</span>

        <span class="c1"># Hiển thị từng trang</span>
        <span class="k">for</span> <span class="n">page</span> <span class="ow">in</span> <span class="n">stats_pages</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">line</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">page</span><span class="p">):</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">line</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="n">i</span> <span class="o">*</span> <span class="mi">12</span><span class="p">)</span>

            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>

        <span class="c1"># Lưu thống kê vào file</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;/stats.json&quot;</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
                <span class="n">json</span><span class="o">.</span><span class="n">dump</span><span class="p">({</span>
                    <span class="s2">&quot;total_sorted&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">total_sorted</span><span class="p">,</span>
                    <span class="s2">&quot;color_stats&quot;</span><span class="p">:</span> <span class="bp">self</span><span class="o">.</span><span class="n">color_stats</span><span class="p">,</span>
                    <span class="s2">&quot;timestamp&quot;</span><span class="p">:</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
                <span class="p">},</span> <span class="n">f</span><span class="p">)</span>

            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Stats saved!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Save failed!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">detect_item_presence</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Phát hiện có vật phẩm cần phân loại&quot;&quot;&quot;</span>
        <span class="c1"># Sử dụng giá trị Clear để phát hiện vật phẩm</span>
        <span class="n">_</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">_</span><span class="p">,</span> <span class="n">clear</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">color_sensor</span><span class="o">.</span><span class="n">get_all_colors_in_rgb</span><span class="p">()</span>

        <span class="c1"># Ngưỡng phát hiện vật phẩm (có thể điều chỉnh)</span>
        <span class="k">return</span> <span class="n">clear</span> <span class="o">&gt;</span> <span class="mi">100</span>  <span class="c1"># Có vật phẩm khi clear &gt; 100</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">run_sorting_cycle</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Chu trình phân loại tự động&quot;&quot;&quot;</span>
        <span class="k">while</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_running</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="c1"># Kiểm tra có vật phẩm không</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">detect_item_presence</span><span class="p">():</span>
                    <span class="c1"># Phát hiện màu</span>
                    <span class="n">color_info</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">detect_and_classify_color</span><span class="p">()</span>

                    <span class="c1"># Hiển thị thông tin</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">display_status</span><span class="p">(</span><span class="n">color_info</span><span class="p">)</span>

                    <span class="c1"># Phân loại nếu độ tin cậy đủ cao</span>
                    <span class="k">if</span> <span class="n">color_info</span><span class="p">[</span><span class="s2">&quot;confidence&quot;</span><span class="p">]</span> <span class="o">&gt;=</span> <span class="mf">0.6</span><span class="p">:</span>  <span class="c1"># 60% confidence</span>
                        <span class="n">sorted_color</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">sort_item</span><span class="p">(</span><span class="n">color_info</span><span class="p">)</span>

                        <span class="c1"># Hiển thị kết quả</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;SORTED!&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Color: </span><span class="si">{</span><span class="n">sorted_color</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
                        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="c1"># Độ tin cậy thấp</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;LOW CONFIDENCE&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Try again...&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Vàng cảnh báo</span>
                        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="c1"># Không có vật phẩm</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">display_status</span><span class="p">()</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># Xanh chờ</span>

                <span class="c1"># Kiểm tra nút dừng</span>
                <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">is_running</span> <span class="o">=</span> <span class="kc">False</span>
                    <span class="k">break</span>

                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>

            <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Cycle Error!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)[:</span><span class="mi">20</span><span class="p">],</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Vòng lặp chính&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">setup</span><span class="p">():</span>
            <span class="k">return</span>

        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">display_status</span><span class="p">()</span>

                <span class="c1"># Kiểm tra nút bấm</span>
                <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_A</span><span class="p">):</span>
                    <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_running</span><span class="p">:</span>
                        <span class="c1"># Bắt đầu phân loại</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">is_running</span> <span class="o">=</span> <span class="kc">True</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Starting...&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
                        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

                        <span class="c1"># Chạy chu trình phân loại</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">run_sorting_cycle</span><span class="p">()</span>

                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.3</span><span class="p">)</span>  <span class="c1"># Debounce</span>

                <span class="k">elif</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
                    <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">is_running</span><span class="p">:</span>
                        <span class="c1"># Hiển thị thống kê</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">show_statistics</span><span class="p">()</span>

                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.3</span><span class="p">)</span>  <span class="c1"># Debounce</span>

                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

            <span class="k">except</span> <span class="ne">KeyboardInterrupt</span><span class="p">:</span>
                <span class="k">break</span>

        <span class="c1"># Cleanup</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">is_running</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">servo</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">90</span><span class="p">)</span>  <span class="c1"># Servo về giữa</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Tắt LED</span>

<span class="c1"># Chạy robot phân loại màu</span>
<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="n">robot</span> <span class="o">=</span> <span class="n">ColorSortingRobot</span><span class="p">()</span>
    <span class="n">robot</span><span class="o">.</span><span class="n">run</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="giai-thich-ma">
<h2>Giải thích mã<a class="headerlink" href="#giai-thich-ma" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban">
<h3>Ví dụ cơ bản:<a class="headerlink" href="#vi-du-co-ban" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Color detection</strong>: Nhận diện màu cơ bản với feedback LED và âm thanh</p></li>
<li><p><strong>RGB reading</strong>: Đọc giá trị RGB thô và chuyển đổi hex</p></li>
<li><p><strong>Interactive testing</strong>: Test màu tương tác với người dùng</p></li>
<li><p><strong>Error handling</strong>: Xử lý lỗi cảm biến và hiển thị</p></li>
</ol>
</section>
<section id="vi-du-nang-cao">
<h3>Ví dụ nâng cao:<a class="headerlink" href="#vi-du-nang-cao" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Multi-sample detection</strong>: Đọc màu nhiều lần để tăng độ chính xác</p></li>
<li><p><strong>Confidence scoring</strong>: Tính độ tin cậy của kết quả nhận diện</p></li>
<li><p><strong>Servo control</strong>: Điều khiển servo để phân loại vật phẩm</p></li>
<li><p><strong>Statistics tracking</strong>: Theo dõi và lưu thống kê phân loại</p></li>
<li><p><strong>Item presence detection</strong>: Phát hiện vật phẩm bằng cảm biến Clear</p></li>
</ol>
</section>
</section>
<section id="bai-tap-mo-rong">
<h2>Bài tập mở rộng<a class="headerlink" href="#bai-tap-mo-rong" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p><strong>Color mixing system</strong>: Hệ thống trộn màu tự động dựa trên cảm biến</p></li>
<li><p><strong>Quality control robot</strong>: Robot kiểm tra chất lượng sản phẩm theo màu</p></li>
<li><p><strong>Interactive art installation</strong>: Tác phẩm nghệ thuật tương tác với màu sắc</p></li>
</ol>
</section>
<section id="loi-thuong-gap">
<h2>Lỗi thường gặp<a class="headerlink" href="#loi-thuong-gap" title="Link to this heading"></a></h2>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Cảm biến không phát hiện màu chính xác</p>
<p><strong>Nguyên nhân</strong>: Ánh sáng môi trường không ổn định hoặc cấu hình sai</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Điều chỉnh gain phù hợp (1x cho ánh sáng mạnh, 60x cho ánh sáng yếu)</p></li>
<li><p>Tăng integration_time cho độ chính xác cao hơn (50-200ms)</p></li>
<li><p>Sử dụng LED tích hợp của cảm biến</p></li>
<li><p>Che chắn ánh sáng ngoài khi đo</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: I2C communication failed</p>
<p><strong>Nguyên nhân</strong>: Kết nối I2C không ổn định hoặc địa chỉ sai</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Kiểm tra kết nối SDA, SCL và nguồn điện</p></li>
<li><p>Xác nhận địa chỉ I2C là 0x29</p></li>
<li><p>Thêm pull-up resistor 4.7kΩ cho SDA/SCL</p></li>
<li><p>Kiểm tra không có xung đột địa chỉ I2C</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Màu nhận diện không ổn định</p>
<p><strong>Nguyên nhân</strong>: Nhiễu ánh sáng hoặc vật liệu phản xạ kém</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Đọc màu nhiều lần và lấy giá trị trung bình</p></li>
<li><p>Sử dụng ngưỡng confidence để lọc kết quả</p></li>
<li><p>Đảm bảo khoảng cách ổn định giữa cảm biến và vật thể</p></li>
<li><p>Sử dụng vật liệu có độ phản xạ tốt</p></li>
</ul>
</div>
</section>
<section id="tai-nguyen-tham-khao">
<h2>Tài nguyên tham khảo<a class="headerlink" href="#tai-nguyen-tham-khao" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference external" href="https://cdn-shop.adafruit.com/datasheets/TCS34725.pdf">TCS34725 Datasheet</a></p></li>
<li><p><a class="reference external" href="https://en.wikipedia.org/wiki/Color_theory">Color Theory Guide</a></p></li>
<li><p><a class="reference external" href="https://en.wikipedia.org/wiki/HSL_and_HSV">RGB to HSV Conversion</a></p></li>
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">BeE Block IDE Online</a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="button.html" class="btn btn-neutral float-left" title="Module Nút Nhấn" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="dht11.html" class="btn btn-neutral float-right" title="Module DHT11" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>