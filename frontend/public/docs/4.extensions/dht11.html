

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Module DHT11 &mdash; Tài <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="BeeLedSegment" href="led-segment.html" />
    <link rel="prev" title="BeeColorDetect" href="color-detect.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">Module DHT11</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thong-so-ky-thuat">Thông số kỹ thuật</a></li>
<li class="toctree-l2"><a class="reference internal" href="#so-do-ket-noi">Sơ đồ kết nối</a></li>
<li class="toctree-l2"><a class="reference internal" href="#giao-dien-lap-trinh-python">Giao diện lập trình Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#khoi-tao-module">Khởi tạo module</a></li>
<li class="toctree-l3"><a class="reference internal" href="#doc-nhiet-do-va-do-am">Đọc nhiệt độ và độ ẩm</a></li>
<li class="toctree-l3"><a class="reference internal" href="#doc-cung-luc-ca-hai-gia-tri">Đọc cùng lúc cả hai giá trị</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#lap-trinh-voi-bee-ide">Lập trình với BeE IDE</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#cac-khoi-lenh-pho-bien">Các khối lệnh phổ biến:</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-python">Ví dụ Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#hien-thi-nhiet-do-do-am-len-oled">Hiển thị nhiệt độ &amp; độ ẩm lên OLED</a></li>
<li class="toctree-l3"><a class="reference internal" href="#bat-led-khi-qua-nong">Bật LED khi quá nóng</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#giai-thich-ma">Giải thích mã</a></li>
<li class="toctree-l2"><a class="reference internal" href="#bai-tap-mo-rong">Bài tập mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-thuong-gap">Lỗi thường gặp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-nguyen-tham-khao">Tài nguyên tham khảo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">Module DHT11</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/4.extensions/dht11.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="module-dht11">
<h1>Module DHT11<a class="headerlink" href="#module-dht11" title="Link to this heading"></a></h1>
<p><img alt="DHT11 Module Cover" src="4.extensions/_static/bee-board-v2/modules/dht11-cover.jpg" /></p>
<hr class="docutils" />
<section id="gioi-thieu">
<h2>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p><strong>Module DHT11</strong> là cảm biến đo <strong>nhiệt độ và độ ẩm</strong> kỹ thuật số,
phổ biến trong các dự án IoT và STEM.
Trên <strong>BeE Board V2</strong>, module này có thể kết nối dễ dàng qua các cổng <strong>PORT1–PORT6</strong>,
và được hỗ trợ sẵn trong thư viện <strong>BeeBrain</strong>.</p>
<blockquote>
<div><p>💡 DHT11 phù hợp cho các dự án:</p>
<ul class="simple">
<li><p>Hiển thị nhiệt độ phòng trên OLED</p></li>
<li><p>Cảnh báo nóng/lạnh bằng LED</p></li>
<li><p>Gửi dữ liệu lên dashboard IoT</p></li>
</ul>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="thong-so-ky-thuat">
<h2>Thông số kỹ thuật<a class="headerlink" href="#thong-so-ky-thuat" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thông số</p></th>
<th class="head"><p>Giá trị</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Điện áp hoạt động</p></td>
<td><p>3.3V – 5V</p></td>
</tr>
<tr class="row-odd"><td><p>Dải đo nhiệt độ</p></td>
<td><p>0°C → 50°C (±2°C)</p></td>
</tr>
<tr class="row-even"><td><p>Dải đo độ ẩm</p></td>
<td><p>20% → 90% RH (±5%)</p></td>
</tr>
<tr class="row-odd"><td><p>Tín hiệu ra</p></td>
<td><p>Digital</p></td>
</tr>
<tr class="row-even"><td><p>Chu kỳ đọc</p></td>
<td><p>≥ 1 giây</p></td>
</tr>
<tr class="row-odd"><td><p>Giao tiếp</p></td>
<td><p>1-wire Digital</p></td>
</tr>
<tr class="row-even"><td><p>Tương thích</p></td>
<td><p>PORTx (GPIO) của BeE Board</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="so-do-ket-noi">
<h2>Sơ đồ kết nối<a class="headerlink" href="#so-do-ket-noi" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Dây</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>VCC</p></td>
<td><p>3.3V</p></td>
</tr>
<tr class="row-odd"><td><p>GND</p></td>
<td><p>Nối đất</p></td>
</tr>
<tr class="row-even"><td><p>SIG</p></td>
<td><p>Tín hiệu Digital (cắm vào PORTx)</p></td>
</tr>
</tbody>
</table>
<blockquote>
<div><p>Cắm module DHT11 vào <strong>PORT1 – PORT6</strong>
(tuỳ cổng được cấu hình trong phần mềm).</p>
</div></blockquote>
</section>
<hr class="docutils" />
<section id="giao-dien-lap-trinh-python">
<h2>Giao diện lập trình Python<a class="headerlink" href="#giao-dien-lap-trinh-python" title="Link to this heading"></a></h2>
<section id="khoi-tao-module">
<h3>Khởi tạo module<a class="headerlink" href="#khoi-tao-module" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">DHT11</span><span class="w"> </span><span class="kn">import</span> <span class="n">DHT11</span>

<span class="n">sensor</span> <span class="o">=</span> <span class="n">DHT11</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="doc-nhiet-do-va-do-am">
<h3>Đọc nhiệt độ và độ ẩm<a class="headerlink" href="#doc-nhiet-do-va-do-am" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">temperature</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">temperature</span><span class="p">()</span>
<span class="n">humidity</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">humidity</span><span class="p">()</span>

<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Nhiệt độ:&quot;</span><span class="p">,</span> <span class="n">temperature</span><span class="p">,</span> <span class="s2">&quot;°C&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Độ ẩm:&quot;</span><span class="p">,</span> <span class="n">humidity</span><span class="p">,</span> <span class="s2">&quot;%&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="doc-cung-luc-ca-hai-gia-tri">
<h3>Đọc cùng lúc cả hai giá trị<a class="headerlink" href="#doc-cung-luc-ca-hai-gia-tri" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">t</span><span class="p">,</span> <span class="n">h</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Nhiệt độ: </span><span class="si">{</span><span class="n">t</span><span class="si">}</span><span class="s2">°C | Độ ẩm: </span><span class="si">{</span><span class="n">h</span><span class="si">}</span><span class="s2">%&quot;</span><span class="p">)</span>
</pre></div>
</div>
<blockquote>
<div><p>Mỗi lần đọc nên cách nhau ít nhất <strong>1 giây</strong>, tránh lỗi <code class="docutils literal notranslate"><span class="pre">Checksum</span> <span class="pre">Error</span></code>.</p>
</div></blockquote>
</section>
</section>
<hr class="docutils" />
<section id="lap-trinh-voi-bee-ide">
<h2>Lập trình với BeE IDE<a class="headerlink" href="#lap-trinh-voi-bee-ide" title="Link to this heading"></a></h2>
<p><img alt="DHT11 Blocks Example" src="4.extensions/_static/bee-ide/blocks/dht11-blocks.jpg" /></p>
<section id="cac-khoi-lenh-pho-bien">
<h3>Các khối lệnh phổ biến:<a class="headerlink" href="#cac-khoi-lenh-pho-bien" title="Link to this heading"></a></h3>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">lấy</span> <span class="n">nhiệt</span> <span class="n">độ</span> <span class="n">từ</span> <span class="n">cảm</span> <span class="n">biến</span> <span class="n">DHT11</span> <span class="n">ở</span> <span class="n">PORT1</span>
<span class="n">lấy</span> <span class="n">độ</span> <span class="n">ẩm</span> <span class="n">từ</span> <span class="n">cảm</span> <span class="n">biến</span> <span class="n">DHT11</span> <span class="n">ở</span> <span class="n">PORT1</span>
<span class="n">hiển</span> <span class="n">thị</span> <span class="n">nhiệt</span> <span class="n">độ</span> <span class="n">và</span> <span class="n">độ</span> <span class="n">ẩm</span> <span class="n">lên</span> <span class="n">màn</span> <span class="n">hình</span> <span class="n">OLED</span>
</pre></div>
</div>
<blockquote>
<div><p>💡 Nhóm khối <strong>Sensor → DHT11</strong> cho phép hiển thị trực tiếp dữ liệu cảm biến.</p>
</div></blockquote>
</section>
</section>
<hr class="docutils" />
<section id="vi-du-python">
<h2>Ví dụ Python<a class="headerlink" href="#vi-du-python" title="Link to this heading"></a></h2>
<section id="hien-thi-nhiet-do-do-am-len-oled">
<h3>Hiển thị nhiệt độ &amp; độ ẩm lên OLED<a class="headerlink" href="#hien-thi-nhiet-do-do-am-len-oled" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">DHT11</span><span class="w"> </span><span class="kn">import</span> <span class="n">DHT11</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">sensor</span> <span class="o">=</span> <span class="n">DHT11</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">t</span><span class="p">,</span> <span class="n">h</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Nhiệt độ: </span><span class="si">{</span><span class="n">t</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">°C&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Độ ẩm: </span><span class="si">{</span><span class="n">h</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">%&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
</section>
<hr class="docutils" />
<section id="bat-led-khi-qua-nong">
<h3>Bật LED khi quá nóng<a class="headerlink" href="#bat-led-khi-qua-nong" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">DHT11</span><span class="w"> </span><span class="kn">import</span> <span class="n">DHT11</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="n">sensor</span> <span class="o">=</span> <span class="n">DHT11</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT2</span><span class="p">)</span>

<span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
    <span class="n">t</span><span class="p">,</span> <span class="n">h</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">read</span><span class="p">()</span>
    <span class="k">if</span> <span class="n">t</span> <span class="o">&gt;</span> <span class="mi">30</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Đỏ – nóng</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">led1</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># Xanh – mát</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<hr class="docutils" />
<section id="giai-thich-ma">
<h2>Giải thích mã<a class="headerlink" href="#giai-thich-ma" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thành phần</p></th>
<th class="head"><p>Mô tả</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">DHT11(PORTx)</span></code></p></td>
<td><p>Tạo cảm biến ở cổng PORTx</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">read()</span></code></p></td>
<td><p>Trả về bộ đôi <code class="docutils literal notranslate"><span class="pre">(temperature,</span> <span class="pre">humidity)</span></code></p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">temperature()</span></code> / <code class="docutils literal notranslate"><span class="pre">humidity()</span></code></p></td>
<td><p>Trả về từng giá trị riêng</p></td>
</tr>
<tr class="row-odd"><td><p><code class="docutils literal notranslate"><span class="pre">bee.oled.write()</span></code></p></td>
<td><p>Hiển thị thông tin lên màn hình OLED</p></td>
</tr>
<tr class="row-even"><td><p><code class="docutils literal notranslate"><span class="pre">bee.led1.set_rgb()</span></code></p></td>
<td><p>Báo trạng thái bằng màu LED</p></td>
</tr>
</tbody>
</table>
</section>
<hr class="docutils" />
<section id="bai-tap-mo-rong">
<h2>Bài tập mở rộng<a class="headerlink" href="#bai-tap-mo-rong" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p><strong>Cảnh báo môi trường</strong>
Hiển thị thông điệp “Nóng quá!” khi nhiệt độ &gt; 32°C.</p></li>
<li><p><strong>Biểu đồ nhiệt độ OLED</strong>
Hiển thị đồ thị cột theo thời gian bằng <code class="docutils literal notranslate"><span class="pre">bee.oled.plot_bar()</span></code>.</p></li>
<li><p><strong>IoT Dashboard</strong>
Gửi dữ liệu nhiệt độ/độ ẩm lên MQTT broker và hiển thị trên web.</p></li>
</ol>
</section>
<hr class="docutils" />
<section id="loi-thuong-gap">
<h2>Lỗi thường gặp<a class="headerlink" href="#loi-thuong-gap" title="Link to this heading"></a></h2>
<div class="warning admonition">
<p class="admonition-title">Dữ liệu trả về None hoặc 0</p>
<p><strong>Nguyên nhân:</strong> Đọc cảm biến quá nhanh hoặc kết nối sai chân.
<strong>Giải pháp:</strong></p>
<ul class="simple">
<li><p>Đảm bảo mỗi lần đọc cách nhau ≥ 1 giây</p></li>
<li><p>Kiểm tra lại dây tín hiệu và cổng PORT</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Giá trị nhiệt độ sai lệch</p>
<p><strong>Nguyên nhân:</strong> Cảm biến đặt gần nguồn nhiệt (board, LED, motor)
<strong>Giải pháp:</strong></p>
<ul class="simple">
<li><p>Đặt cảm biến xa nguồn nhiệt &gt; 5cm</p></li>
<li><p>Đợi 10–20 giây để DHT11 ổn định</p></li>
</ul>
</div>
</section>
<hr class="docutils" />
<section id="tai-nguyen-tham-khao">
<h2>Tài nguyên tham khảo<a class="headerlink" href="#tai-nguyen-tham-khao" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/docs/bee-board">BeE Board API Documentation</a></p></li>
<li><p><a class="reference external" href="https://docs.micropython.org/en/latest/library/dht.html">MicroPython DHT Library</a></p></li>
<li><p><a class="reference external" href="https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/peripherals/gpio.html">ESP32 GPIO Reference</a></p></li>
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">BeE IDE Online</a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="color-detect.html" class="btn btn-neutral float-left" title="BeeColorDetect" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="led-segment.html" class="btn btn-neutral float-right" title="BeeLedSegment" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>