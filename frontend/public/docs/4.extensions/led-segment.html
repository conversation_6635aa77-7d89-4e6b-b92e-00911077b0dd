

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>BeeLedSegment &mdash; Tài <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="BeeLineDetect" href="line-detect.html" />
    <link rel="prev" title="Module DHT11" href="dht11.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="dht11.html">Module DHT11</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">BeeLedSegment</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thong-so-ky-thuat">Thông số kỹ thuật</a></li>
<li class="toctree-l2"><a class="reference internal" href="#giao-dien-lap-trinh">Giao diện lập trình</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#khoi-tao">Khởi tạo</a></li>
<li class="toctree-l3"><a class="reference internal" href="#dieu-khien-do-sang">Điều khiển độ sáng</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#brightness-value-thiet-lap-doc-do-sang"><code class="docutils literal notranslate"><span class="pre">brightness(value)</span></code> - Thiết lập/đọc độ sáng</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#hien-thi-so">Hiển thị số</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#show-number-number-hien-thi-so-nguyen"><code class="docutils literal notranslate"><span class="pre">show_number(number)</span></code> - Hiển thị số nguyên</a></li>
<li class="toctree-l4"><a class="reference internal" href="#show-numbers-num1-num2-colon-hien-thi-hai-so-voi-dau-hai-cham"><code class="docutils literal notranslate"><span class="pre">show_numbers(num1,</span> <span class="pre">num2,</span> <span class="pre">colon)</span></code> - Hiển thị hai số với dấu hai chấm</a></li>
<li class="toctree-l4"><a class="reference internal" href="#show-hex-value-hien-thi-so-hex"><code class="docutils literal notranslate"><span class="pre">show_hex(value)</span></code> - Hiển thị số hex</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#hien-thi-text">Hiển thị text</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#show-text-colon-hien-thi-text"><code class="docutils literal notranslate"><span class="pre">show(text,</span> <span class="pre">colon)</span></code> - Hiển thị text</a></li>
<li class="toctree-l4"><a class="reference internal" href="#scroll-text-delay-cuon-text"><code class="docutils literal notranslate"><span class="pre">scroll(text,</span> <span class="pre">delay)</span></code> - Cuộn text</a></li>
<li class="toctree-l4"><a class="reference internal" href="#display-text-hien-thi-thong-minh"><code class="docutils literal notranslate"><span class="pre">display(text)</span></code> - Hiển thị thông minh</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#hien-thi-nhiet-do">Hiển thị nhiệt độ</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#show-temperature-temp-hien-thi-nhiet-do"><code class="docutils literal notranslate"><span class="pre">show_temperature(temp)</span></code> - Hiển thị nhiệt độ</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#dieu-khien-co-ban">Điều khiển cơ bản</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#clear-xoa-man-hinh"><code class="docutils literal notranslate"><span class="pre">clear()</span></code> - Xóa màn hình</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-blockly">Ví dụ Blockly</a></li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-python">Ví dụ Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban-dong-ho-so">Ví dụ cơ bản - Đồng hồ số</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao-he-thong-hien-thi-da-chuc-nang">Ví dụ nâng cao - Hệ thống hiển thị đa chức năng</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#giai-thich-ma">Giải thích mã</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban">Ví dụ cơ bản:</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao">Ví dụ nâng cao:</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#bai-tap-mo-rong">Bài tập mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-thuong-gap">Lỗi thường gặp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-nguyen-tham-khao">Tài nguyên tham khảo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">BeeLedSegment</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/4.extensions/led-segment.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="beeledsegment">
<h1>BeeLedSegment<a class="headerlink" href="#beeledsegment" title="Link to this heading"></a></h1>
<section id="gioi-thieu">
<h2>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p>BeeLedSegment là module hiển thị LED 7 đoạn 4 chữ số sử dụng chip TM1637, hỗ trợ hiển thị số, chữ cái, ký tự đặc biệt và có thể điều chỉnh độ sáng. Module này lý tưởng cho việc hiển thị thời gian, nhiệt độ, điểm số và các thông tin số học.</p>
<p>Module tích hợp dấu hai chấm (:) ở giữa để hiển thị thời gian, có thể hiển thị text cuộn và hỗ trợ nhiều chế độ hiển thị khác nhau. Giao tiếp đơn giản chỉ cần 2 dây (CLK và DIO).</p>
<p><strong>Ứng dụng thực tế:</strong></p>
<ul class="simple">
<li><p>Đồng hồ số hiển thị giờ:phút</p></li>
<li><p>Hiển thị nhiệt độ môi trường</p></li>
<li><p>Bảng điểm số trò chơi</p></li>
<li><p>Đồng hồ đếm ngược</p></li>
<li><p>Hiển thị thông số cảm biến</p></li>
<li><p>Máy tính đơn giản</p></li>
<li><p>Hệ thống thông báo số thứ tự</p></li>
</ul>
</section>
<section id="thong-so-ky-thuat">
<h2>Thông số kỹ thuật<a class="headerlink" href="#thong-so-ky-thuat" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thông số</p></th>
<th class="head"><p>Giá trị</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Chip điều khiển</p></td>
<td><p>TM1637</p></td>
</tr>
<tr class="row-odd"><td><p>Số chữ số</p></td>
<td><p>4</p></td>
</tr>
<tr class="row-even"><td><p>Loại LED</p></td>
<td><p>7-segment + dấu hai chấm</p></td>
</tr>
<tr class="row-odd"><td><p>Giao tiếp</p></td>
<td><p>2-wire (CLK, DIO)</p></td>
</tr>
<tr class="row-even"><td><p>Điện áp hoạt động</p></td>
<td><p>3.3V - 5V</p></td>
</tr>
<tr class="row-odd"><td><p>Dòng tiêu thụ</p></td>
<td><p>80mA (max brightness)</p></td>
</tr>
<tr class="row-even"><td><p>Độ sáng</p></td>
<td><p>8 mức (0-7)</p></td>
</tr>
<tr class="row-odd"><td><p>Tần số quét</p></td>
<td><p>1kHz</p></td>
</tr>
<tr class="row-even"><td><p>Màu LED</p></td>
<td><p>Đỏ</p></td>
</tr>
<tr class="row-odd"><td><p>Kích thước hiển thị</p></td>
<td><p>0.56 inch</p></td>
</tr>
<tr class="row-even"><td><p>Ký tự hỗ trợ</p></td>
<td><p>0-9, A-Z, space, dash, star</p></td>
</tr>
</tbody>
</table>
</section>
<section id="giao-dien-lap-trinh">
<h2>Giao diện lập trình<a class="headerlink" href="#giao-dien-lap-trinh" title="Link to this heading"></a></h2>
<section id="khoi-tao">
<h3>Khởi tạo<a class="headerlink" href="#khoi-tao" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeLedSegment</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeLedSegment</span>

<span class="c1"># Khởi tạo với độ sáng mặc định (7)</span>
<span class="n">segment</span> <span class="o">=</span> <span class="n">BeeLedSegment</span><span class="p">(</span><span class="n">PORT1</span><span class="p">)</span>

<span class="c1"># Khởi tạo với độ sáng tùy chỉnh</span>
<span class="n">segment</span> <span class="o">=</span> <span class="n">BeeLedSegment</span><span class="p">(</span><span class="n">PORT2</span><span class="p">,</span> <span class="n">brightness</span><span class="o">=</span><span class="mi">3</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="dieu-khien-do-sang">
<h3>Điều khiển độ sáng<a class="headerlink" href="#dieu-khien-do-sang" title="Link to this heading"></a></h3>
<section id="brightness-value-thiet-lap-doc-do-sang">
<h4><code class="docutils literal notranslate"><span class="pre">brightness(value)</span></code> - Thiết lập/đọc độ sáng<a class="headerlink" href="#brightness-value-thiet-lap-doc-do-sang" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Thiết lập độ sáng</span>
<span class="n">segment</span><span class="o">.</span><span class="n">brightness</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>  <span class="c1"># Độ sáng trung bình</span>

<span class="c1"># Đọc độ sáng hiện tại</span>
<span class="n">current_brightness</span> <span class="o">=</span> <span class="n">segment</span><span class="o">.</span><span class="n">brightness</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Brightness: </span><span class="si">{</span><span class="n">current_brightness</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="hien-thi-so">
<h3>Hiển thị số<a class="headerlink" href="#hien-thi-so" title="Link to this heading"></a></h3>
<section id="show-number-number-hien-thi-so-nguyen">
<h4><code class="docutils literal notranslate"><span class="pre">show_number(number)</span></code> - Hiển thị số nguyên<a class="headerlink" href="#show-number-number-hien-thi-so-nguyen" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Hiển thị số dương</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_number</span><span class="p">(</span><span class="mi">1234</span><span class="p">)</span>

<span class="c1"># Hiển thị số âm</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_number</span><span class="p">(</span><span class="o">-</span><span class="mi">123</span><span class="p">)</span>

<span class="c1"># Số ngoài phạm vi sẽ được giới hạn</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_number</span><span class="p">(</span><span class="mi">99999</span><span class="p">)</span>  <span class="c1"># Hiển thị 9999</span>
</pre></div>
</div>
</section>
<section id="show-numbers-num1-num2-colon-hien-thi-hai-so-voi-dau-hai-cham">
<h4><code class="docutils literal notranslate"><span class="pre">show_numbers(num1,</span> <span class="pre">num2,</span> <span class="pre">colon)</span></code> - Hiển thị hai số với dấu hai chấm<a class="headerlink" href="#show-numbers-num1-num2-colon-hien-thi-hai-so-voi-dau-hai-cham" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Hiển thị giờ:phút</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_numbers</span><span class="p">(</span><span class="mi">14</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>  <span class="c1"># &quot;14:30&quot;</span>

<span class="c1"># Hiển thị hai số không có dấu hai chấm</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_numbers</span><span class="p">(</span><span class="mi">12</span><span class="p">,</span> <span class="mi">34</span><span class="p">,</span> <span class="kc">False</span><span class="p">)</span>  <span class="c1"># &quot;1234&quot;</span>
</pre></div>
</div>
</section>
<section id="show-hex-value-hien-thi-so-hex">
<h4><code class="docutils literal notranslate"><span class="pre">show_hex(value)</span></code> - Hiển thị số hex<a class="headerlink" href="#show-hex-value-hien-thi-so-hex" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Hiển thị giá trị hex</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_hex</span><span class="p">(</span><span class="mh">0xABCD</span><span class="p">)</span>  <span class="c1"># &quot;ABCD&quot;</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_hex</span><span class="p">(</span><span class="mi">255</span><span class="p">)</span>     <span class="c1"># &quot;00FF&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="hien-thi-text">
<h3>Hiển thị text<a class="headerlink" href="#hien-thi-text" title="Link to this heading"></a></h3>
<section id="show-text-colon-hien-thi-text">
<h4><code class="docutils literal notranslate"><span class="pre">show(text,</span> <span class="pre">colon)</span></code> - Hiển thị text<a class="headerlink" href="#show-text-colon-hien-thi-text" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Hiển thị text đơn giản</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show</span><span class="p">(</span><span class="s2">&quot;Hi&quot;</span><span class="p">)</span>

<span class="c1"># Hiển thị với dấu hai chấm</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show</span><span class="p">(</span><span class="s2">&quot;12:34&quot;</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>

<span class="c1"># Hiển thị ký tự đặc biệt</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show</span><span class="p">(</span><span class="s2">&quot;A-b*&quot;</span><span class="p">)</span>  <span class="c1"># A, dash, b, star</span>
</pre></div>
</div>
</section>
<section id="scroll-text-delay-cuon-text">
<h4><code class="docutils literal notranslate"><span class="pre">scroll(text,</span> <span class="pre">delay)</span></code> - Cuộn text<a class="headerlink" href="#scroll-text-delay-cuon-text" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Cuộn text dài</span>
<span class="n">segment</span><span class="o">.</span><span class="n">scroll</span><span class="p">(</span><span class="s2">&quot;Hello World&quot;</span><span class="p">)</span>

<span class="c1"># Cuộn với tốc độ tùy chỉnh</span>
<span class="n">segment</span><span class="o">.</span><span class="n">scroll</span><span class="p">(</span><span class="s2">&quot;BeE Board&quot;</span><span class="p">,</span> <span class="mi">150</span><span class="p">)</span>  <span class="c1"># Cuộn nhanh hơn</span>
</pre></div>
</div>
</section>
<section id="display-text-hien-thi-thong-minh">
<h4><code class="docutils literal notranslate"><span class="pre">display(text)</span></code> - Hiển thị thông minh<a class="headerlink" href="#display-text-hien-thi-thong-minh" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Tự động chọn hiển thị tĩnh hoặc cuộn</span>
<span class="n">segment</span><span class="o">.</span><span class="n">display</span><span class="p">(</span><span class="s2">&quot;Hi&quot;</span><span class="p">)</span>          <span class="c1"># Hiển thị tĩnh</span>
<span class="n">segment</span><span class="o">.</span><span class="n">display</span><span class="p">(</span><span class="s2">&quot;Long text&quot;</span><span class="p">)</span>   <span class="c1"># Tự động cuộn</span>
</pre></div>
</div>
</section>
</section>
<section id="hien-thi-nhiet-do">
<h3>Hiển thị nhiệt độ<a class="headerlink" href="#hien-thi-nhiet-do" title="Link to this heading"></a></h3>
<section id="show-temperature-temp-hien-thi-nhiet-do">
<h4><code class="docutils literal notranslate"><span class="pre">show_temperature(temp)</span></code> - Hiển thị nhiệt độ<a class="headerlink" href="#show-temperature-temp-hien-thi-nhiet-do" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Hiển thị nhiệt độ bình thường</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_temperature</span><span class="p">(</span><span class="mi">25</span><span class="p">)</span>    <span class="c1"># &quot;25°C&quot;</span>

<span class="c1"># Nhiệt độ quá thấp/cao</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_temperature</span><span class="p">(</span><span class="o">-</span><span class="mi">15</span><span class="p">)</span>   <span class="c1"># &quot;LO°C&quot;</span>
<span class="n">segment</span><span class="o">.</span><span class="n">show_temperature</span><span class="p">(</span><span class="mi">150</span><span class="p">)</span>   <span class="c1"># &quot;HI°C&quot;</span>
</pre></div>
</div>
</section>
</section>
<section id="dieu-khien-co-ban">
<h3>Điều khiển cơ bản<a class="headerlink" href="#dieu-khien-co-ban" title="Link to this heading"></a></h3>
<section id="clear-xoa-man-hinh">
<h4><code class="docutils literal notranslate"><span class="pre">clear()</span></code> - Xóa màn hình<a class="headerlink" href="#clear-xoa-man-hinh" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">segment</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="vi-du-blockly">
<h2>Ví dụ Blockly<a class="headerlink" href="#vi-du-blockly" title="Link to this heading"></a></h2>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">when</span> <span class="n">program</span> <span class="n">starts</span><span class="p">:</span>
    <span class="nb">set</span> <span class="n">display</span> <span class="n">to</span> <span class="n">BeeLedSegment</span> <span class="n">at</span> <span class="n">PORT1</span>
    <span class="nb">set</span> <span class="n">brightness</span> <span class="n">to</span> <span class="mi">5</span>

<span class="n">forever</span><span class="p">:</span>
    <span class="n">show</span> <span class="n">current</span> <span class="n">time</span> <span class="n">on</span> <span class="n">display</span> <span class="k">with</span> <span class="n">colon</span>
    <span class="n">wait</span> <span class="mi">1</span> <span class="n">second</span>

    <span class="k">if</span> <span class="n">button</span> <span class="n">A</span> <span class="n">pressed</span><span class="p">:</span>
        <span class="n">show</span> <span class="n">temperature</span> <span class="n">on</span> <span class="n">display</span>
        <span class="n">wait</span> <span class="mi">2</span> <span class="n">seconds</span>

    <span class="k">if</span> <span class="n">button</span> <span class="n">B</span> <span class="n">pressed</span><span class="p">:</span>
        <span class="n">scroll</span> <span class="s2">&quot;BeE Board STEM Kit&quot;</span> <span class="n">on</span> <span class="n">display</span>
</pre></div>
</div>
</section>
<section id="vi-du-python">
<h2>Ví dụ Python<a class="headerlink" href="#vi-du-python" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban-dong-ho-so">
<h3>Ví dụ cơ bản - Đồng hồ số<a class="headerlink" href="#vi-du-co-ban-dong-ho-so" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeLedSegment</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeLedSegment</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">def</span><span class="w"> </span><span class="nf">setup</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Khởi tạo màn hình LED 7 đoạn&quot;&quot;&quot;</span>
    <span class="k">global</span> <span class="n">display</span>

    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Khởi tạo display</span>
        <span class="n">display</span> <span class="o">=</span> <span class="n">BeeLedSegment</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">,</span> <span class="n">brightness</span><span class="o">=</span><span class="mi">5</span><span class="p">)</span>

        <span class="c1"># Test hiển thị</span>
        <span class="n">display</span><span class="o">.</span><span class="n">show</span><span class="p">(</span><span class="s2">&quot;8888&quot;</span><span class="p">)</span>  <span class="c1"># Test tất cả segment</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
        <span class="n">display</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>

        <span class="c1"># Hiển thị thông báo khởi động</span>
        <span class="n">display</span><span class="o">.</span><span class="n">scroll</span><span class="p">(</span><span class="s2">&quot;Clock Ready&quot;</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Digital Clock&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;7-Segment&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>

        <span class="k">return</span> <span class="kc">True</span>

    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Display Error!&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="k">return</span> <span class="kc">False</span>

<span class="k">def</span><span class="w"> </span><span class="nf">digital_clock</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Đồng hồ số với LED 7 đoạn&quot;&quot;&quot;</span>
    <span class="n">colon_state</span> <span class="o">=</span> <span class="kc">True</span>
    <span class="n">last_second</span> <span class="o">=</span> <span class="o">-</span><span class="mi">1</span>

    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="c1"># Lấy thời gian hiện tại</span>
        <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">localtime</span><span class="p">()</span>
        <span class="n">hour</span> <span class="o">=</span> <span class="n">current_time</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span>
        <span class="n">minute</span> <span class="o">=</span> <span class="n">current_time</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span>
        <span class="n">second</span> <span class="o">=</span> <span class="n">current_time</span><span class="p">[</span><span class="mi">5</span><span class="p">]</span>

        <span class="c1"># Cập nhật mỗi giây</span>
        <span class="k">if</span> <span class="n">second</span> <span class="o">!=</span> <span class="n">last_second</span><span class="p">:</span>
            <span class="n">last_second</span> <span class="o">=</span> <span class="n">second</span>

            <span class="c1"># Hiển thị giờ:phút</span>
            <span class="n">display</span><span class="o">.</span><span class="n">show_numbers</span><span class="p">(</span><span class="n">hour</span><span class="p">,</span> <span class="n">minute</span><span class="p">,</span> <span class="n">colon_state</span><span class="p">)</span>

            <span class="c1"># Nhấp nháy dấu hai chấm mỗi giây</span>
            <span class="n">colon_state</span> <span class="o">=</span> <span class="ow">not</span> <span class="n">colon_state</span>

            <span class="c1"># Hiển thị thông tin trên OLED</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Digital Clock&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">hour</span><span class="si">:</span><span class="s2">02d</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">minute</span><span class="si">:</span><span class="s2">02d</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">second</span><span class="si">:</span><span class="s2">02d</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A: Brightness&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">35</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;B: Temperature&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">50</span><span class="p">)</span>

        <span class="c1"># Kiểm tra nút điều chỉnh độ sáng</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_A</span><span class="p">):</span>
            <span class="n">adjust_brightness</span><span class="p">()</span>

        <span class="c1"># Kiểm tra nút hiển thị nhiệt độ</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
            <span class="n">show_temperature_demo</span><span class="p">()</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

<span class="k">def</span><span class="w"> </span><span class="nf">adjust_brightness</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Điều chỉnh độ sáng&quot;&quot;&quot;</span>
    <span class="n">current_brightness</span> <span class="o">=</span> <span class="n">display</span><span class="o">.</span><span class="n">brightness</span><span class="p">()</span>
    <span class="n">new_brightness</span> <span class="o">=</span> <span class="p">(</span><span class="n">current_brightness</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">%</span> <span class="mi">8</span>

    <span class="n">display</span><span class="o">.</span><span class="n">brightness</span><span class="p">(</span><span class="n">new_brightness</span><span class="p">)</span>

    <span class="c1"># Hiển thị mức độ sáng</span>
    <span class="n">display</span><span class="o">.</span><span class="n">show_number</span><span class="p">(</span><span class="n">new_brightness</span><span class="p">)</span>

    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Brightness&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Level: </span><span class="si">{</span><span class="n">new_brightness</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>

    <span class="c1"># Hiệu ứng LED</span>
    <span class="n">intensity</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="n">new_brightness</span> <span class="o">*</span> <span class="mi">255</span> <span class="o">/</span> <span class="mi">7</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="n">intensity</span><span class="p">,</span> <span class="n">intensity</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>

    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>

<span class="k">def</span><span class="w"> </span><span class="nf">show_temperature_demo</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Demo hiển thị nhiệt độ&quot;&quot;&quot;</span>
    <span class="c1"># Giả lập dữ liệu nhiệt độ</span>
    <span class="n">temperatures</span> <span class="o">=</span> <span class="p">[</span><span class="mi">25</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="o">-</span><span class="mi">5</span><span class="p">,</span> <span class="mi">100</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">22</span><span class="p">]</span>

    <span class="k">for</span> <span class="n">temp</span> <span class="ow">in</span> <span class="n">temperatures</span><span class="p">:</span>
        <span class="n">display</span><span class="o">.</span><span class="n">show_temperature</span><span class="p">(</span><span class="n">temp</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Temperature&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">temp</span><span class="si">}</span><span class="s2">°C&quot;</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">temp</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># Xanh dương = lạnh</span>
        <span class="k">elif</span> <span class="n">temp</span> <span class="o">&gt;</span> <span class="mi">35</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Đỏ = nóng</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Xanh = bình thường</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">1.5</span><span class="p">)</span>

    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>

<span class="c1"># Chạy chương trình</span>
<span class="k">if</span> <span class="n">setup</span><span class="p">():</span>
    <span class="n">digital_clock</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="vi-du-nang-cao-he-thong-hien-thi-da-chuc-nang">
<h3>Ví dụ nâng cao - Hệ thống hiển thị đa chức năng<a class="headerlink" href="#vi-du-nang-cao-he-thong-hien-thi-da-chuc-nang" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeLedSegment</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeLedSegment</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">random</span>

<span class="k">class</span><span class="w"> </span><span class="nc">MultiDisplaySystem</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">display</span> <span class="o">=</span> <span class="n">BeeLedSegment</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">,</span> <span class="n">brightness</span><span class="o">=</span><span class="mi">6</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">current_mode</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">modes</span> <span class="o">=</span> <span class="p">[</span>
            <span class="s2">&quot;clock&quot;</span><span class="p">,</span> <span class="s2">&quot;counter&quot;</span><span class="p">,</span> <span class="s2">&quot;timer&quot;</span><span class="p">,</span> <span class="s2">&quot;game&quot;</span><span class="p">,</span> <span class="s2">&quot;sensor&quot;</span><span class="p">,</span> <span class="s2">&quot;calculator&quot;</span>
        <span class="p">]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">mode_names</span> <span class="o">=</span> <span class="p">[</span>
            <span class="s2">&quot;Clock&quot;</span><span class="p">,</span> <span class="s2">&quot;Counter&quot;</span><span class="p">,</span> <span class="s2">&quot;Timer&quot;</span><span class="p">,</span> <span class="s2">&quot;Game&quot;</span><span class="p">,</span> <span class="s2">&quot;Sensor&quot;</span><span class="p">,</span> <span class="s2">&quot;Calc&quot;</span>
        <span class="p">]</span>

        <span class="c1"># Biến trạng thái cho các chế độ</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">counter_value</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">timer_seconds</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">timer_running</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">game_score</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">calculator_value</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">calculator_operation</span> <span class="o">=</span> <span class="kc">None</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">setup</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Khởi tạo hệ thống&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Test display</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">scroll</span><span class="p">(</span><span class="s2">&quot;Multi Display System&quot;</span><span class="p">)</span>

            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Multi Display&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;System Ready&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>

            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">True</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;System Error!&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">False</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">switch_mode</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Chuyển đổi chế độ hiển thị&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">current_mode</span> <span class="o">=</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">current_mode</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">%</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">modes</span><span class="p">)</span>
        <span class="n">mode_name</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">mode_names</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">current_mode</span><span class="p">]</span>

        <span class="c1"># Hiển thị tên chế độ</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">scroll</span><span class="p">(</span><span class="n">mode_name</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Mode:&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">mode_name</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>

        <span class="c1"># LED màu khác nhau cho mỗi chế độ</span>
        <span class="n">colors</span> <span class="o">=</span> <span class="p">[</span>
            <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>    <span class="c1"># Clock - Xanh</span>
            <span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>  <span class="c1"># Counter - Vàng</span>
            <span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>    <span class="c1"># Timer - Đỏ</span>
            <span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">),</span>  <span class="c1"># Game - Tím</span>
            <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">),</span>  <span class="c1"># Sensor - Cyan</span>
            <span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">165</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>   <span class="c1"># Calculator - Cam</span>
        <span class="p">]</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="o">*</span><span class="n">colors</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">current_mode</span><span class="p">])</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">mode_clock</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Chế độ đồng hồ&quot;&quot;&quot;</span>
        <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">localtime</span><span class="p">()</span>
        <span class="n">hour</span> <span class="o">=</span> <span class="n">current_time</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span>
        <span class="n">minute</span> <span class="o">=</span> <span class="n">current_time</span><span class="p">[</span><span class="mi">4</span><span class="p">]</span>
        <span class="n">second</span> <span class="o">=</span> <span class="n">current_time</span><span class="p">[</span><span class="mi">5</span><span class="p">]</span>

        <span class="c1"># Nhấp nháy dấu hai chấm</span>
        <span class="n">colon</span> <span class="o">=</span> <span class="p">(</span><span class="n">second</span> <span class="o">%</span> <span class="mi">2</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">show_numbers</span><span class="p">(</span><span class="n">hour</span><span class="p">,</span> <span class="n">minute</span><span class="p">,</span> <span class="n">colon</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;CLOCK MODE&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">hour</span><span class="si">:</span><span class="s2">02d</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">minute</span><span class="si">:</span><span class="s2">02d</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">second</span><span class="si">:</span><span class="s2">02d</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A: Mode  B: Bright&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">40</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">mode_counter</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Chế độ đếm số&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">show_number</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">counter_value</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;COUNTER MODE&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Value: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">counter_value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A: Mode  B: +1&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">40</span><span class="p">)</span>

        <span class="c1"># Nút B để tăng counter</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">counter_value</span> <span class="o">=</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">counter_value</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">%</span> <span class="mi">10000</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>  <span class="c1"># Debounce</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">mode_timer</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Chế độ đếm ngược&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">timer_running</span> <span class="ow">and</span> <span class="bp">self</span><span class="o">.</span><span class="n">timer_seconds</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">timer_seconds</span> <span class="o">-=</span> <span class="mi">1</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">timer_seconds</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                <span class="c1"># Hết giờ</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">timer_running</span> <span class="o">=</span> <span class="kc">False</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;A4:0.2 SIL:0.1 A4:0.2 SIL:0.1 A4:0.2&quot;</span><span class="p">)</span>
                <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">):</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>

        <span class="c1"># Hiển thị phút:giây</span>
        <span class="n">minutes</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">timer_seconds</span> <span class="o">//</span> <span class="mi">60</span>
        <span class="n">seconds</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">timer_seconds</span> <span class="o">%</span> <span class="mi">60</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">show_numbers</span><span class="p">(</span><span class="n">minutes</span><span class="p">,</span> <span class="n">seconds</span><span class="p">,</span> <span class="kc">True</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;TIMER MODE&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">minutes</span><span class="si">:</span><span class="s2">02d</span><span class="si">}</span><span class="s2">:</span><span class="si">{</span><span class="n">seconds</span><span class="si">:</span><span class="s2">02d</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
        <span class="n">status</span> <span class="o">=</span> <span class="s2">&quot;Running&quot;</span> <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">timer_running</span> <span class="k">else</span> <span class="s2">&quot;Stopped&quot;</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Status: </span><span class="si">{</span><span class="n">status</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">35</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A: Mode  B: Start/Stop&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">50</span><span class="p">)</span>

        <span class="c1"># Nút B để start/stop timer</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
            <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">timer_running</span><span class="p">:</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">timer_seconds</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">timer_seconds</span> <span class="o">=</span> <span class="mi">60</span>  <span class="c1"># Mặc định 1 phút</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">timer_running</span> <span class="o">=</span> <span class="kc">True</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">timer_running</span> <span class="o">=</span> <span class="kc">False</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.3</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">mode_game</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Chế độ trò chơi đơn giản&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">show_number</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">game_score</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;GAME MODE&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Score: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">game_score</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A: Mode  B: Play&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">40</span><span class="p">)</span>

        <span class="c1"># Nút B để chơi (random điểm)</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
            <span class="n">points</span> <span class="o">=</span> <span class="n">random</span><span class="o">.</span><span class="n">randint</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">100</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">game_score</span> <span class="o">+=</span> <span class="n">points</span>

            <span class="c1"># Hiệu ứng</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">scroll</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;+</span><span class="si">{</span><span class="n">points</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4&quot;</span><span class="p">)</span>

            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">mode_sensor</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Chế độ hiển thị cảm biến&quot;&quot;&quot;</span>
        <span class="c1"># Giả lập đọc cảm biến (có thể thay bằng cảm biến thật)</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Đọc analog từ PORT2</span>
            <span class="n">sensor_value</span> <span class="o">=</span> <span class="n">bee</span><span class="o">.</span><span class="n">analog_read</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT2</span><span class="p">,</span> <span class="n">bee</span><span class="o">.</span><span class="n">PERCENT</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">show_number</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="n">sensor_value</span><span class="p">))</span>

            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;SENSOR MODE&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Value: </span><span class="si">{</span><span class="n">sensor_value</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">%&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A: Mode  B: Reset&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">40</span><span class="p">)</span>

        <span class="k">except</span><span class="p">:</span>
            <span class="c1"># Không có cảm biến - hiển thị giá trị giả</span>
            <span class="n">fake_value</span> <span class="o">=</span> <span class="n">random</span><span class="o">.</span><span class="n">randint</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">100</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">show_number</span><span class="p">(</span><span class="n">fake_value</span><span class="p">)</span>

            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;SENSOR MODE&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Demo: </span><span class="si">{</span><span class="n">fake_value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A: Mode  B: Refresh&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">40</span><span class="p">)</span>

        <span class="c1"># Nút B để refresh</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">mode_calculator</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Chế độ máy tính đơn giản&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">show_number</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">calculator_value</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;CALC MODE&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Value: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">calculator_value</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;A: Mode  B: +10&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">40</span><span class="p">)</span>

        <span class="c1"># Nút B để cộng 10</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">calculator_value</span> <span class="o">=</span> <span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">calculator_value</span> <span class="o">+</span> <span class="mi">10</span><span class="p">)</span> <span class="o">%</span> <span class="mi">10000</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">adjust_brightness</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Điều chỉnh độ sáng&quot;&quot;&quot;</span>
        <span class="n">current</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">brightness</span><span class="p">()</span>
        <span class="n">new_brightness</span> <span class="o">=</span> <span class="p">(</span><span class="n">current</span> <span class="o">+</span> <span class="mi">1</span><span class="p">)</span> <span class="o">%</span> <span class="mi">8</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">brightness</span><span class="p">(</span><span class="n">new_brightness</span><span class="p">)</span>

        <span class="c1"># Hiển thị mức độ sáng</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">show</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;br</span><span class="si">{</span><span class="n">new_brightness</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Vòng lặp chính&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">setup</span><span class="p">():</span>
            <span class="k">return</span>

        <span class="n">last_update</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

                <span class="c1"># Cập nhật hiển thị mỗi giây (trừ timer)</span>
                <span class="k">if</span> <span class="p">(</span><span class="n">current_time</span> <span class="o">-</span> <span class="n">last_update</span><span class="p">)</span> <span class="o">&gt;=</span> <span class="mi">1</span> <span class="ow">or</span> <span class="bp">self</span><span class="o">.</span><span class="n">modes</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">current_mode</span><span class="p">]</span> <span class="o">==</span> <span class="s2">&quot;timer&quot;</span><span class="p">:</span>
                    <span class="n">last_update</span> <span class="o">=</span> <span class="n">current_time</span>

                    <span class="c1"># Gọi hàm tương ứng với chế độ hiện tại</span>
                    <span class="n">mode_function</span> <span class="o">=</span> <span class="nb">getattr</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="sa">f</span><span class="s2">&quot;mode_</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">modes</span><span class="p">[</span><span class="bp">self</span><span class="o">.</span><span class="n">current_mode</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
                    <span class="n">mode_function</span><span class="p">()</span>

                <span class="c1"># Kiểm tra nút chuyển chế độ</span>
                <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_A</span><span class="p">):</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">switch_mode</span><span class="p">()</span>
                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.3</span><span class="p">)</span>  <span class="c1"># Debounce</span>

                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

            <span class="k">except</span> <span class="ne">KeyboardInterrupt</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">display</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;System&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Stopped&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
                <span class="k">break</span>

<span class="c1"># Chạy hệ thống đa chức năng</span>
<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="n">system</span> <span class="o">=</span> <span class="n">MultiDisplaySystem</span><span class="p">()</span>
    <span class="n">system</span><span class="o">.</span><span class="n">run</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="giai-thich-ma">
<h2>Giải thích mã<a class="headerlink" href="#giai-thich-ma" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban">
<h3>Ví dụ cơ bản:<a class="headerlink" href="#vi-du-co-ban" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Digital clock</strong>: Đồng hồ số với dấu hai chấm nhấp nháy</p></li>
<li><p><strong>Brightness control</strong>: Điều chỉnh độ sáng bằng nút bấm</p></li>
<li><p><strong>Temperature display</strong>: Demo hiển thị nhiệt độ với ký tự đặc biệt</p></li>
<li><p><strong>Visual feedback</strong>: Kết hợp OLED và LED RGB</p></li>
</ol>
</section>
<section id="vi-du-nang-cao">
<h3>Ví dụ nâng cao:<a class="headerlink" href="#vi-du-nang-cao" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Multi-mode system</strong>: Hệ thống đa chức năng với 6 chế độ</p></li>
<li><p><strong>State management</strong>: Quản lý trạng thái cho từng chế độ</p></li>
<li><p><strong>Interactive features</strong>: Tương tác với nút bấm cho mỗi chế độ</p></li>
<li><p><strong>Dynamic display</strong>: Hiển thị thông minh tùy theo nội dung</p></li>
</ol>
</section>
</section>
<section id="bai-tap-mo-rong">
<h2>Bài tập mở rộng<a class="headerlink" href="#bai-tap-mo-rong" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p><strong>Stopwatch</strong>: Tạo đồng hồ bấm giờ với chức năng lap time</p></li>
<li><p><strong>Score board</strong>: Bảng điểm cho trò chơi với nhiều người chơi</p></li>
<li><p><strong>Environmental monitor</strong>: Hiển thị nhiệt độ, độ ẩm từ cảm biến thật</p></li>
</ol>
</section>
<section id="loi-thuong-gap">
<h2>Lỗi thường gặp<a class="headerlink" href="#loi-thuong-gap" title="Link to this heading"></a></h2>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Một số segment không sáng</p>
<p><strong>Nguyên nhân</strong>: Kết nối lỏng hoặc chip TM1637 bị lỗi</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Kiểm tra kết nối CLK và DIO</p></li>
<li><p>Đảm bảo nguồn điện ổn định</p></li>
<li><p>Thử giảm độ sáng</p></li>
<li><p>Kiểm tra module có bị hỏng không</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Hiển thị nhấp nháy hoặc không ổn định</p>
<p><strong>Nguyên nhân</strong>: Nhiễu điện hoặc tần số cập nhật quá cao</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Thêm delay giữa các lần cập nhật</p></li>
<li><p>Kiểm tra nguồn điện có ổn định</p></li>
<li><p>Tránh cập nhật quá nhanh (&lt;10Hz)</p></li>
<li><p>Sử dụng dây kết nối ngắn và chất lượng</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Ký tự hiển thị sai</p>
<p><strong>Nguyên nhân</strong>: Ký tự không được hỗ trợ hoặc encoding sai</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Chỉ sử dụng ký tự 0-9, A-Z, space, dash, star</p></li>
<li><p>Kiểm tra bảng mã ký tự trong datasheet</p></li>
<li><p>Sử dụng hàm encode_char() để test</p></li>
<li><p>Tránh ký tự Unicode hoặc đặc biệt</p></li>
</ul>
</div>
</section>
<section id="tai-nguyen-tham-khao">
<h2>Tài nguyên tham khảo<a class="headerlink" href="#tai-nguyen-tham-khao" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference external" href="https://www.titanmec.com/index.php/en/project/download/id/302.html">TM1637 Datasheet</a></p></li>
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/docs/bee-board/tutorials/7segment-display">7-Segment Display Guide</a></p></li>
<li><p><a class="reference external" href="https://www.electronics-tutorials.ws/blog/7-segment-display-tutorial.html">ASCII to 7-Segment Converter</a></p></li>
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">BeE Block IDE Online</a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="dht11.html" class="btn btn-neutral float-left" title="Module DHT11" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="line-detect.html" class="btn btn-neutral float-right" title="BeeLineDetect" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>