

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>BeeLineDetect &mdash; Tài Li<PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="BeeRC522" href="rc522.html" />
    <link rel="prev" title="BeeLedSegment" href="led-segment.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">BeeLineDetect</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thong-so-ky-thuat">Thông số kỹ thuật</a></li>
<li class="toctree-l2"><a class="reference internal" href="#giao-dien-lap-trinh">Giao diện lập trình</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#khoi-tao">Khởi tạo</a></li>
<li class="toctree-l3"><a class="reference internal" href="#doc-cam-bien-don-le">Đọc cảm biến đơn lẻ</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#pin-pin-number-doc-trang-thai-mot-cam-bien"><code class="docutils literal notranslate"><span class="pre">pin(pin_number)</span></code> - Đọc trạng thái một cảm biến</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#doc-tat-ca-cam-bien">Đọc tất cả cảm biến</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#read-all-doc-trang-thai-tat-ca-cam-bien"><code class="docutils literal notranslate"><span class="pre">read_all()</span></code> - Đọc trạng thái tất cả cảm biến</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#kiem-tra-ket-noi">Kiểm tra kết nối</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#check-kiem-tra-module-co-hoat-dong-khong"><code class="docutils literal notranslate"><span class="pre">check()</span></code> - Kiểm tra module có hoạt động không</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#thuoc-tinh-port">Thuộc tính port</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#port-doc-trang-thai-port-4-bit-cao"><code class="docutils literal notranslate"><span class="pre">port</span></code> - Đọc trạng thái port (4 bit cao)</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-blockly">Ví dụ Blockly</a></li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-python">Ví dụ Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban-theo-duong-don-gian">Ví dụ cơ bản - Theo đường đơn giản</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao-robot-theo-duong-thong-minh-voi-pid">Ví dụ nâng cao - Robot theo đường thông minh với PID</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#giai-thich-ma">Giải thích mã</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban">Ví dụ cơ bản:</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao">Ví dụ nâng cao:</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#bai-tap-mo-rong">Bài tập mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-thuong-gap">Lỗi thường gặp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-nguyen-tham-khao">Tài nguyên tham khảo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="rc522.html">BeeRC522</a></li>
<li class="toctree-l1"><a class="reference internal" href="ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">BeeLineDetect</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/4.extensions/line-detect.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="beelinedetect">
<h1>BeeLineDetect<a class="headerlink" href="#beelinedetect" title="Link to this heading"></a></h1>
<section id="gioi-thieu">
<h2>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p>BeeLineDetect là module cảm biến hồng ngoại chuyên dụng cho việc dò đường và theo dõi vạch kẻ. Module sử dụng chip PCF8574 để giao tiếp I2C và tích hợp 4 cảm biến hồng ngoại (IR1-IR4) được sắp xếp theo hàng ngang.</p>
<p>Module này được thiết kế đặc biệt cho các ứng dụng robot theo đường, có thể phát hiện vạch đen trên nền trắng hoặc ngược lại. Với 4 cảm biến được bố trí hợp lý, robot có thể xác định chính xác vị trí đường và điều chỉnh hướng di chuyển.</p>
<p><strong>Ứng dụng thực tế:</strong></p>
<ul class="simple">
<li><p>Robot theo đường tự động</p></li>
<li><p>Xe AGV (Automated Guided Vehicle)</p></li>
<li><p>Robot dọn dẹp theo lộ trình</p></li>
<li><p>Hệ thống vận chuyển tự động</p></li>
<li><p>Cuộc thi robot sumo/line following</p></li>
<li><p>Dự án giáo dục STEM về automation</p></li>
</ul>
</section>
<section id="thong-so-ky-thuat">
<h2>Thông số kỹ thuật<a class="headerlink" href="#thong-so-ky-thuat" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thông số</p></th>
<th class="head"><p>Giá trị</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Chip điều khiển</p></td>
<td><p>PCF8574</p></td>
</tr>
<tr class="row-odd"><td><p>Giao tiếp</p></td>
<td><p>I2C</p></td>
</tr>
<tr class="row-even"><td><p>Địa chỉ I2C mặc định</p></td>
<td><p>0x23</p></td>
</tr>
<tr class="row-odd"><td><p>Số lượng cảm biến</p></td>
<td><p>4 (IR1, IR2, IR3, IR4)</p></td>
</tr>
<tr class="row-even"><td><p>Khoảng cách phát hiện</p></td>
<td><p>2-10mm</p></td>
</tr>
<tr class="row-odd"><td><p>Điện áp hoạt động</p></td>
<td><p>3.3V - 5V</p></td>
</tr>
<tr class="row-even"><td><p>Dòng tiêu thụ</p></td>
<td><p>&lt;20mA</p></td>
</tr>
<tr class="row-odd"><td><p>Tần số hoạt động</p></td>
<td><p>38kHz (IR)</p></td>
</tr>
<tr class="row-even"><td><p>Góc phát hiện</p></td>
<td><p>±15°</p></td>
</tr>
<tr class="row-odd"><td><p>Thời gian phản hồi</p></td>
<td><p>&lt;1ms</p></td>
</tr>
</tbody>
</table>
</section>
<section id="giao-dien-lap-trinh">
<h2>Giao diện lập trình<a class="headerlink" href="#giao-dien-lap-trinh" title="Link to this heading"></a></h2>
<section id="khoi-tao">
<h3>Khởi tạo<a class="headerlink" href="#khoi-tao" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeLineDetect</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeLineDetect</span>

<span class="c1"># Khởi tạo với địa chỉ mặc định</span>
<span class="n">sensor</span> <span class="o">=</span> <span class="n">BeeLineDetect</span><span class="p">(</span><span class="n">PORT1</span><span class="p">)</span>

<span class="c1"># Khởi tạo với địa chỉ tùy chỉnh</span>
<span class="n">sensor</span> <span class="o">=</span> <span class="n">BeeLineDetect</span><span class="p">(</span><span class="n">PORT2</span><span class="p">,</span> <span class="n">address</span><span class="o">=</span><span class="mh">0x24</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="doc-cam-bien-don-le">
<h3>Đọc cảm biến đơn lẻ<a class="headerlink" href="#doc-cam-bien-don-le" title="Link to this heading"></a></h3>
<section id="pin-pin-number-doc-trang-thai-mot-cam-bien">
<h4><code class="docutils literal notranslate"><span class="pre">pin(pin_number)</span></code> - Đọc trạng thái một cảm biến<a class="headerlink" href="#pin-pin-number-doc-trang-thai-mot-cam-bien" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Đọc cảm biến IR1 (trái nhất)</span>
<span class="n">left_sensor</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">pin</span><span class="p">(</span><span class="n">sensor</span><span class="o">.</span><span class="n">IR1</span><span class="p">)</span>

<span class="c1"># Đọc cảm biến IR4 (phải nhất)</span>
<span class="n">right_sensor</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">pin</span><span class="p">(</span><span class="n">sensor</span><span class="o">.</span><span class="n">IR4</span><span class="p">)</span>

<span class="c1"># Kiểm tra phát hiện đường</span>
<span class="k">if</span> <span class="n">sensor</span><span class="o">.</span><span class="n">pin</span><span class="p">(</span><span class="n">sensor</span><span class="o">.</span><span class="n">IR2</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Phát hiện đường ở cảm biến trái-giữa&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="doc-tat-ca-cam-bien">
<h3>Đọc tất cả cảm biến<a class="headerlink" href="#doc-tat-ca-cam-bien" title="Link to this heading"></a></h3>
<section id="read-all-doc-trang-thai-tat-ca-cam-bien">
<h4><code class="docutils literal notranslate"><span class="pre">read_all()</span></code> - Đọc trạng thái tất cả cảm biến<a class="headerlink" href="#read-all-doc-trang-thai-tat-ca-cam-bien" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Đọc tất cả 4 cảm biến</span>
<span class="n">sensors</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">read_all</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;IR1: </span><span class="si">{</span><span class="n">sensors</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="si">}</span><span class="s2">, IR2: </span><span class="si">{</span><span class="n">sensors</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span><span class="si">}</span><span class="s2">, IR3: </span><span class="si">{</span><span class="n">sensors</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span><span class="si">}</span><span class="s2">, IR4: </span><span class="si">{</span><span class="n">sensors</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="c1"># Sử dụng unpacking</span>
<span class="n">ir1</span><span class="p">,</span> <span class="n">ir2</span><span class="p">,</span> <span class="n">ir3</span><span class="p">,</span> <span class="n">ir4</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">read_all</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="kiem-tra-ket-noi">
<h3>Kiểm tra kết nối<a class="headerlink" href="#kiem-tra-ket-noi" title="Link to this heading"></a></h3>
<section id="check-kiem-tra-module-co-hoat-dong-khong">
<h4><code class="docutils literal notranslate"><span class="pre">check()</span></code> - Kiểm tra module có hoạt động không<a class="headerlink" href="#check-kiem-tra-module-co-hoat-dong-khong" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">try</span><span class="p">:</span>
    <span class="k">if</span> <span class="n">sensor</span><span class="o">.</span><span class="n">check</span><span class="p">():</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Module line detect sẵn sàng&quot;</span><span class="p">)</span>
<span class="k">except</span> <span class="ne">OSError</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Không tìm thấy module line detect&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="thuoc-tinh-port">
<h3>Thuộc tính port<a class="headerlink" href="#thuoc-tinh-port" title="Link to this heading"></a></h3>
<section id="port-doc-trang-thai-port-4-bit-cao">
<h4><code class="docutils literal notranslate"><span class="pre">port</span></code> - Đọc trạng thái port (4 bit cao)<a class="headerlink" href="#port-doc-trang-thai-port-4-bit-cao" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Đọc trạng thái port</span>
<span class="n">port_state</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">port</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Port state: </span><span class="si">{</span><span class="n">port_state</span><span class="si">:</span><span class="s2">04b</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="vi-du-blockly">
<h2>Ví dụ Blockly<a class="headerlink" href="#vi-du-blockly" title="Link to this heading"></a></h2>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">when</span> <span class="n">program</span> <span class="n">starts</span><span class="p">:</span>
    <span class="nb">set</span> <span class="n">line_sensor</span> <span class="n">to</span> <span class="n">BeeLineDetect</span> <span class="n">at</span> <span class="n">PORT1</span>

<span class="n">forever</span><span class="p">:</span>
    <span class="nb">set</span> <span class="n">sensors</span> <span class="n">to</span> <span class="n">read</span> <span class="nb">all</span> <span class="n">line</span> <span class="n">sensors</span>

    <span class="k">if</span> <span class="n">IR1</span> <span class="ow">and</span> <span class="n">IR2</span> <span class="n">detect</span> <span class="n">line</span><span class="p">:</span>
        <span class="n">turn</span> <span class="n">left</span>
    <span class="k">else</span> <span class="k">if</span> <span class="n">IR3</span> <span class="ow">and</span> <span class="n">IR4</span> <span class="n">detect</span> <span class="n">line</span><span class="p">:</span>
        <span class="n">turn</span> <span class="n">right</span>
    <span class="k">else</span> <span class="k">if</span> <span class="n">IR2</span> <span class="ow">and</span> <span class="n">IR3</span> <span class="n">detect</span> <span class="n">line</span><span class="p">:</span>
        <span class="n">move</span> <span class="n">forward</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">stop</span> <span class="ow">and</span> <span class="n">search</span> <span class="k">for</span> <span class="n">line</span>
</pre></div>
</div>
</section>
<section id="vi-du-python">
<h2>Ví dụ Python<a class="headerlink" href="#vi-du-python" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban-theo-duong-don-gian">
<h3>Ví dụ cơ bản - Theo đường đơn giản<a class="headerlink" href="#vi-du-co-ban-theo-duong-don-gian" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeLineDetect</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeLineDetect</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">def</span><span class="w"> </span><span class="nf">setup</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Khởi tạo cảm biến và robot&quot;&quot;&quot;</span>
    <span class="k">global</span> <span class="n">line_sensor</span>

    <span class="c1"># Khởi tạo cảm biến dò đường</span>
    <span class="n">line_sensor</span> <span class="o">=</span> <span class="n">BeeLineDetect</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>

    <span class="c1"># Kiểm tra kết nối</span>
    <span class="k">try</span><span class="p">:</span>
        <span class="n">line_sensor</span><span class="o">.</span><span class="n">check</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Line Sensor OK&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED xanh</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4&quot;</span><span class="p">)</span>
    <span class="k">except</span> <span class="ne">OSError</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Sensor Error!&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED đỏ</span>
        <span class="k">return</span> <span class="kc">False</span>

    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
    <span class="k">return</span> <span class="kc">True</span>

<span class="k">def</span><span class="w"> </span><span class="nf">follow_line</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Thuật toán theo đường cơ bản&quot;&quot;&quot;</span>
    <span class="n">base_speed</span> <span class="o">=</span> <span class="mi">30</span>
    <span class="n">turn_speed</span> <span class="o">=</span> <span class="mi">25</span>

    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="c1"># Đọc tất cả cảm biến</span>
        <span class="n">ir1</span><span class="p">,</span> <span class="n">ir2</span><span class="p">,</span> <span class="n">ir3</span><span class="p">,</span> <span class="n">ir4</span> <span class="o">=</span> <span class="n">line_sensor</span><span class="o">.</span><span class="n">read_all</span><span class="p">()</span>

        <span class="c1"># Hiển thị trạng thái cảm biến trên OLED</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;IR: </span><span class="si">{</span><span class="n">ir1</span><span class="si">}{</span><span class="n">ir2</span><span class="si">}{</span><span class="n">ir3</span><span class="si">}{</span><span class="n">ir4</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>

        <span class="c1"># Logic theo đường</span>
        <span class="k">if</span> <span class="n">ir2</span> <span class="ow">and</span> <span class="n">ir3</span><span class="p">:</span>
            <span class="c1"># Đường thẳng - di chuyển tiến</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;FORWARD&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Xanh</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">move_forward</span><span class="p">(</span><span class="n">base_speed</span><span class="p">)</span>

        <span class="k">elif</span> <span class="n">ir1</span> <span class="ow">or</span> <span class="n">ir2</span><span class="p">:</span>
            <span class="c1"># Đường lệch trái - rẽ trái</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;TURN LEFT&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Vàng</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">turn_left</span><span class="p">(</span><span class="n">turn_speed</span><span class="p">)</span>

        <span class="k">elif</span> <span class="n">ir3</span> <span class="ow">or</span> <span class="n">ir4</span><span class="p">:</span>
            <span class="c1"># Đường lệch phải - rẽ phải</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;TURN RIGHT&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># Cyan</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">turn_right</span><span class="p">(</span><span class="n">turn_speed</span><span class="p">)</span>

        <span class="k">elif</span> <span class="n">ir1</span> <span class="ow">and</span> <span class="n">ir2</span> <span class="ow">and</span> <span class="n">ir3</span> <span class="ow">and</span> <span class="n">ir4</span><span class="p">:</span>
            <span class="c1"># Giao lộ hoặc kết thúc - dừng</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;INTERSECTION&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># Tím</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;G4 E4 C4&quot;</span><span class="p">)</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Mất đường - tìm kiếm</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;SEARCHING&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Đỏ</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
            <span class="n">search_line</span><span class="p">()</span>

        <span class="c1"># Hiển thị tốc độ</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Speed: </span><span class="si">{</span><span class="n">base_speed</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

        <span class="c1"># Kiểm tra nút dừng</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;STOPPED&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
            <span class="k">break</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.05</span><span class="p">)</span>  <span class="c1"># 20Hz update rate</span>

<span class="k">def</span><span class="w"> </span><span class="nf">search_line</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Tìm kiếm đường khi bị mất&quot;&quot;&quot;</span>
    <span class="n">search_time</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="n">max_search_time</span> <span class="o">=</span> <span class="mi">2</span>  <span class="c1"># 2 giây</span>

    <span class="k">while</span> <span class="n">search_time</span> <span class="o">&lt;</span> <span class="n">max_search_time</span><span class="p">:</span>
        <span class="c1"># Quay trái tìm đường</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">turn_left</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

        <span class="c1"># Kiểm tra có tìm thấy đường không</span>
        <span class="n">sensors</span> <span class="o">=</span> <span class="n">line_sensor</span><span class="o">.</span><span class="n">read_all</span><span class="p">()</span>
        <span class="k">if</span> <span class="nb">any</span><span class="p">(</span><span class="n">sensors</span><span class="p">):</span>
            <span class="k">return</span>  <span class="c1"># Tìm thấy đường, thoát</span>

        <span class="n">search_time</span> <span class="o">+=</span> <span class="mf">0.1</span>

    <span class="c1"># Không tìm thấy đường - dừng hẳn</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;A4:0.2 SIL:0.1 A4:0.2 SIL:0.1 A4:0.2&quot;</span><span class="p">)</span>

<span class="c1"># Chạy chương trình</span>
<span class="k">if</span> <span class="n">setup</span><span class="p">():</span>
    <span class="n">follow_line</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="vi-du-nang-cao-robot-theo-duong-thong-minh-voi-pid">
<h3>Ví dụ nâng cao - Robot theo đường thông minh với PID<a class="headerlink" href="#vi-du-nang-cao-robot-theo-duong-thong-minh-voi-pid" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeLineDetect</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeLineDetect</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">class</span><span class="w"> </span><span class="nc">SmartLineFollower</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">line_sensor</span> <span class="o">=</span> <span class="n">BeeLineDetect</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">base_speed</span> <span class="o">=</span> <span class="mi">35</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">max_speed</span> <span class="o">=</span> <span class="mi">50</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">min_speed</span> <span class="o">=</span> <span class="mi">15</span>

        <span class="c1"># PID parameters</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">kp</span> <span class="o">=</span> <span class="mf">1.5</span>  <span class="c1"># Proportional gain</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">ki</span> <span class="o">=</span> <span class="mf">0.1</span>  <span class="c1"># Integral gain</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">kd</span> <span class="o">=</span> <span class="mf">0.8</span>  <span class="c1"># Derivative gain</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">last_error</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">integral</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">last_position</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="c1"># Statistics</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">total_distance</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">start_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">intersections</span> <span class="o">=</span> <span class="mi">0</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">setup</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Khởi tạo và kiểm tra hệ thống&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">line_sensor</span><span class="o">.</span><span class="n">check</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Smart Line&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Follower Ready&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4 C5&quot;</span><span class="p">)</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">True</span>
        <span class="k">except</span> <span class="ne">OSError</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Sensor Error!&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">False</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">read_line_position</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Tính toán vị trí đường (weighted average)&quot;&quot;&quot;</span>
        <span class="n">sensors</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">line_sensor</span><span class="o">.</span><span class="n">read_all</span><span class="p">()</span>

        <span class="c1"># Trọng số cho từng cảm biến (trái -&gt; phải: -3, -1, 1, 3)</span>
        <span class="n">weights</span> <span class="o">=</span> <span class="p">[</span><span class="o">-</span><span class="mi">3</span><span class="p">,</span> <span class="o">-</span><span class="mi">1</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">3</span><span class="p">]</span>
        <span class="n">weighted_sum</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="n">total_sensors</span> <span class="o">=</span> <span class="mi">0</span>

        <span class="k">for</span> <span class="n">i</span><span class="p">,</span> <span class="n">sensor_value</span> <span class="ow">in</span> <span class="nb">enumerate</span><span class="p">(</span><span class="n">sensors</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">sensor_value</span><span class="p">:</span>
                <span class="n">weighted_sum</span> <span class="o">+=</span> <span class="n">weights</span><span class="p">[</span><span class="n">i</span><span class="p">]</span>
                <span class="n">total_sensors</span> <span class="o">+=</span> <span class="mi">1</span>

        <span class="k">if</span> <span class="n">total_sensors</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">None</span>  <span class="c1"># Không phát hiện đường</span>

        <span class="c1"># Vị trí từ -3 (trái) đến 3 (phải), 0 là giữa</span>
        <span class="n">position</span> <span class="o">=</span> <span class="n">weighted_sum</span> <span class="o">/</span> <span class="n">total_sensors</span>
        <span class="k">return</span> <span class="n">position</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">calculate_pid</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">position</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Tính toán điều khiển PID&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">position</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
            <span class="k">return</span> <span class="mi">0</span>

        <span class="c1"># Error = vị trí mong muốn (0) - vị trí hiện tại</span>
        <span class="n">error</span> <span class="o">=</span> <span class="mi">0</span> <span class="o">-</span> <span class="n">position</span>

        <span class="c1"># Proportional term</span>
        <span class="n">proportional</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">kp</span> <span class="o">*</span> <span class="n">error</span>

        <span class="c1"># Integral term</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">integral</span> <span class="o">+=</span> <span class="n">error</span>
        <span class="n">integral</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">ki</span> <span class="o">*</span> <span class="bp">self</span><span class="o">.</span><span class="n">integral</span>

        <span class="c1"># Derivative term</span>
        <span class="n">derivative</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">kd</span> <span class="o">*</span> <span class="p">(</span><span class="n">error</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">last_error</span><span class="p">)</span>

        <span class="c1"># PID output</span>
        <span class="n">pid_output</span> <span class="o">=</span> <span class="n">proportional</span> <span class="o">+</span> <span class="n">integral</span> <span class="o">+</span> <span class="n">derivative</span>

        <span class="bp">self</span><span class="o">.</span><span class="n">last_error</span> <span class="o">=</span> <span class="n">error</span>
        <span class="k">return</span> <span class="n">pid_output</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">motor_control</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">pid_output</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Điều khiển motor dựa trên PID output&quot;&quot;&quot;</span>
        <span class="c1"># Tính tốc độ cho từng motor</span>
        <span class="n">left_speed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">base_speed</span> <span class="o">+</span> <span class="n">pid_output</span>
        <span class="n">right_speed</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">base_speed</span> <span class="o">-</span> <span class="n">pid_output</span>

        <span class="c1"># Giới hạn tốc độ</span>
        <span class="n">left_speed</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">min_speed</span><span class="p">,</span> <span class="nb">min</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">max_speed</span><span class="p">,</span> <span class="n">left_speed</span><span class="p">))</span>
        <span class="n">right_speed</span> <span class="o">=</span> <span class="nb">max</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">min_speed</span><span class="p">,</span> <span class="nb">min</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">max_speed</span><span class="p">,</span> <span class="n">right_speed</span><span class="p">))</span>

        <span class="c1"># Điều khiển motor (giả sử có hàm điều khiển từng motor)</span>
        <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">bee</span><span class="p">,</span> <span class="s1">&#39;dcmotor&#39;</span><span class="p">):</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">dcmotor</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="nb">int</span><span class="p">(</span><span class="n">left_speed</span><span class="p">))</span>   <span class="c1"># Motor trái</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">dcmotor</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="nb">int</span><span class="p">(</span><span class="n">right_speed</span><span class="p">))</span>  <span class="c1"># Motor phải</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Fallback: sử dụng hàm di chuyển cơ bản</span>
            <span class="k">if</span> <span class="nb">abs</span><span class="p">(</span><span class="n">pid_output</span><span class="p">)</span> <span class="o">&lt;</span> <span class="mf">0.5</span><span class="p">:</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">move_forward</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">base_speed</span><span class="p">)</span>
            <span class="k">elif</span> <span class="n">pid_output</span> <span class="o">&gt;</span> <span class="mi">0</span><span class="p">:</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">turn_left</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="nb">abs</span><span class="p">(</span><span class="n">pid_output</span><span class="p">)</span> <span class="o">*</span> <span class="mi">10</span><span class="p">))</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">turn_right</span><span class="p">(</span><span class="nb">int</span><span class="p">(</span><span class="nb">abs</span><span class="p">(</span><span class="n">pid_output</span><span class="p">)</span> <span class="o">*</span> <span class="mi">10</span><span class="p">))</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">detect_intersection</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Phát hiện giao lộ&quot;&quot;&quot;</span>
        <span class="n">sensors</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">line_sensor</span><span class="o">.</span><span class="n">read_all</span><span class="p">()</span>
        <span class="n">active_sensors</span> <span class="o">=</span> <span class="nb">sum</span><span class="p">(</span><span class="n">sensors</span><span class="p">)</span>

        <span class="c1"># Giao lộ khi có 3 hoặc 4 cảm biến phát hiện đường</span>
        <span class="k">if</span> <span class="n">active_sensors</span> <span class="o">&gt;=</span> <span class="mi">3</span><span class="p">:</span>
            <span class="k">return</span> <span class="kc">True</span>
        <span class="k">return</span> <span class="kc">False</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">handle_intersection</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Xử lý giao lộ&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">intersections</span> <span class="o">+=</span> <span class="mi">1</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Intersection #</span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">intersections</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># Tím</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C5:0.1 E5:0.1 G5:0.1&quot;</span><span class="p">)</span>

        <span class="c1"># Dừng một chút</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>

        <span class="c1"># Tiếp tục đi thẳng qua giao lộ</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">move_forward</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">base_speed</span><span class="p">,</span> <span class="mf">0.3</span><span class="p">)</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.3</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">update_display</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">position</span><span class="p">,</span> <span class="n">pid_output</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Cập nhật thông tin hiển thị&quot;&quot;&quot;</span>
        <span class="n">runtime</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">start_time</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Pos: </span><span class="si">{</span><span class="n">position</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">&quot;</span> <span class="k">if</span> <span class="n">position</span> <span class="k">else</span> <span class="s2">&quot;Lost&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;PID: </span><span class="si">{</span><span class="n">pid_output</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">12</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Speed: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">base_speed</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">24</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Time: </span><span class="si">{</span><span class="n">runtime</span><span class="si">:</span><span class="s2">.0f</span><span class="si">}</span><span class="s2">s&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">36</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cross: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">intersections</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">48</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Vòng lặp chính&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">setup</span><span class="p">():</span>
            <span class="k">return</span>

        <span class="n">lost_count</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="n">max_lost_count</span> <span class="o">=</span> <span class="mi">10</span>  <span class="c1"># Số lần đọc liên tiếp không thấy đường</span>

        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="c1"># Đọc vị trí đường</span>
                <span class="n">position</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">read_line_position</span><span class="p">()</span>

                <span class="k">if</span> <span class="n">position</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
                    <span class="c1"># Tìm thấy đường</span>
                    <span class="n">lost_count</span> <span class="o">=</span> <span class="mi">0</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">last_position</span> <span class="o">=</span> <span class="n">position</span>

                    <span class="c1"># Kiểm tra giao lộ</span>
                    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">detect_intersection</span><span class="p">():</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">handle_intersection</span><span class="p">()</span>
                        <span class="k">continue</span>

                    <span class="c1"># Tính PID và điều khiển</span>
                    <span class="n">pid_output</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">calculate_pid</span><span class="p">(</span><span class="n">position</span><span class="p">)</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">motor_control</span><span class="p">(</span><span class="n">pid_output</span><span class="p">)</span>

                    <span class="c1"># LED xanh = bình thường</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>

                    <span class="c1"># Cập nhật hiển thị</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">update_display</span><span class="p">(</span><span class="n">position</span><span class="p">,</span> <span class="n">pid_output</span><span class="p">)</span>

                <span class="k">else</span><span class="p">:</span>
                    <span class="c1"># Mất đường</span>
                    <span class="n">lost_count</span> <span class="o">+=</span> <span class="mi">1</span>

                    <span class="k">if</span> <span class="n">lost_count</span> <span class="o">&lt;</span> <span class="n">max_lost_count</span><span class="p">:</span>
                        <span class="c1"># Tiếp tục với PID dựa trên vị trí cuối</span>
                        <span class="n">pid_output</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">calculate_pid</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">last_position</span><span class="p">)</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">motor_control</span><span class="p">(</span><span class="n">pid_output</span><span class="p">)</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Vàng</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="c1"># Mất đường hoàn toàn - dừng</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Đỏ</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;A4:0.2 SIL:0.1 A4:0.2&quot;</span><span class="p">)</span>

                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;LINE LOST!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Press A to retry&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>

                        <span class="c1"># Chờ nút A để thử lại</span>
                        <span class="k">while</span> <span class="ow">not</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_A</span><span class="p">):</span>
                            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

                        <span class="n">lost_count</span> <span class="o">=</span> <span class="mi">0</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">integral</span> <span class="o">=</span> <span class="mi">0</span>  <span class="c1"># Reset integral</span>

                <span class="c1"># Kiểm tra nút dừng</span>
                <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
                    <span class="k">break</span>

                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.02</span><span class="p">)</span>  <span class="c1"># 50Hz control loop</span>

            <span class="k">except</span> <span class="ne">KeyboardInterrupt</span><span class="p">:</span>
                <span class="k">break</span>

        <span class="c1"># Dừng robot và hiển thị thống kê</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">stop_robot</span><span class="p">()</span>
        <span class="n">runtime</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">start_time</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;FINISHED!&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Time: </span><span class="si">{</span><span class="n">runtime</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">s&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Intersections: </span><span class="si">{</span><span class="bp">self</span><span class="o">.</span><span class="n">intersections</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># Xanh dương</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4 C5 G4 E4 C4&quot;</span><span class="p">)</span>

<span class="c1"># Chạy robot thông minh</span>
<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="n">robot</span> <span class="o">=</span> <span class="n">SmartLineFollower</span><span class="p">()</span>
    <span class="n">robot</span><span class="o">.</span><span class="n">run</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="giai-thich-ma">
<h2>Giải thích mã<a class="headerlink" href="#giai-thich-ma" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban">
<h3>Ví dụ cơ bản:<a class="headerlink" href="#vi-du-co-ban" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Sensor reading</strong>: Đọc 4 cảm biến và phân tích pattern</p></li>
<li><p><strong>Simple logic</strong>: Logic if-else đơn giản cho các trường hợp</p></li>
<li><p><strong>Visual feedback</strong>: Hiển thị trạng thái trên OLED và LED</p></li>
<li><p><strong>Error handling</strong>: Xử lý trường hợp mất đường</p></li>
</ol>
</section>
<section id="vi-du-nang-cao">
<h3>Ví dụ nâng cao:<a class="headerlink" href="#vi-du-nang-cao" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>PID control</strong>: Điều khiển PID cho chuyển động mượt mà</p></li>
<li><p><strong>Position calculation</strong>: Tính toán vị trí đường bằng weighted average</p></li>
<li><p><strong>Intersection detection</strong>: Phát hiện và xử lý giao lộ</p></li>
<li><p><strong>Statistics tracking</strong>: Theo dõi thống kê hiệu suất</p></li>
</ol>
</section>
</section>
<section id="bai-tap-mo-rong">
<h2>Bài tập mở rộng<a class="headerlink" href="#bai-tap-mo-rong" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p><strong>Robot maze solver</strong>: Kết hợp line following với thuật toán giải mê cung</p></li>
<li><p><strong>Multi-path navigator</strong>: Robot có thể chọn đường đi tối ưu tại giao lộ</p></li>
<li><p><strong>Speed optimization</strong>: Tự động điều chỉnh tốc độ dựa trên độ cong của đường</p></li>
</ol>
</section>
<section id="loi-thuong-gap">
<h2>Lỗi thường gặp<a class="headerlink" href="#loi-thuong-gap" title="Link to this heading"></a></h2>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Cảm biến không phát hiện đường</p>
<p><strong>Nguyên nhân</strong>: Khoảng cách không phù hợp hoặc độ tương phản thấp</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Điều chỉnh khoảng cách 2-8mm từ mặt đất</p></li>
<li><p>Đảm bảo đường có độ tương phản cao (đen/trắng)</p></li>
<li><p>Kiểm tra ánh sáng môi trường (tránh ánh sáng mạnh)</p></li>
<li><p>Làm sạch cảm biến IR</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Robot dao động khi theo đường</p>
<p><strong>Nguyên nhân</strong>: Tham số PID không phù hợp hoặc tốc độ quá cao</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Giảm tốc độ cơ bản</p></li>
<li><p>Điều chỉnh tham số Kp (giảm nếu dao động)</p></li>
<li><p>Tăng tham số Kd để giảm overshoot</p></li>
<li><p>Kiểm tra tần số đọc cảm biến (20-50Hz)</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Không giao tiếp được I2C</p>
<p><strong>Nguyên nhân</strong>: Địa chỉ I2C sai hoặc kết nối lỏng</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Kiểm tra địa chỉ I2C (mặc định 0x23)</p></li>
<li><p>Đảm bảo kết nối SDA, SCL chắc chắn</p></li>
<li><p>Thử scan I2C để tìm địa chỉ đúng</p></li>
<li><p>Kiểm tra pull-up resistor (4.7kΩ)</p></li>
</ul>
</div>
</section>
<section id="tai-nguyen-tham-khao">
<h2>Tài nguyên tham khảo<a class="headerlink" href="#tai-nguyen-tham-khao" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference external" href="https://www.ti.com/lit/ds/symlink/pcf8574.pdf">PCF8574 Datasheet</a></p></li>
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/docs/bee-board/tutorials/line-following">Line Following Robot Tutorial</a></p></li>
<li><p><a class="reference external" href="https://en.wikipedia.org/wiki/PID_controller">PID Control Theory</a></p></li>
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">BeE Block IDE Online</a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="led-segment.html" class="btn btn-neutral float-left" title="BeeLedSegment" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="rc522.html" class="btn btn-neutral float-right" title="BeeRC522" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>