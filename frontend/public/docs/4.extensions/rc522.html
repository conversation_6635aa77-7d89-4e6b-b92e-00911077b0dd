

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>BeeRC522 &mdash; Tài <PERSON>u BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="BeeUltrasonic" href="ultrasonic.html" />
    <link rel="prev" title="BeeLineDetect" href="line-detect.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">BeeRC522</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thong-so-ky-thuat">Thông số kỹ thuật</a></li>
<li class="toctree-l2"><a class="reference internal" href="#giao-dien-lap-trinh">Giao diện lập trình</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#khoi-tao">Khởi tạo</a></li>
<li class="toctree-l3"><a class="reference internal" href="#doc-the-co-ban">Đọc thẻ cơ bản</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#readid-detail-false-doc-id-the"><code class="docutils literal notranslate"><span class="pre">readID(detail=False)</span></code> - Đọc ID thẻ</a></li>
<li class="toctree-l4"><a class="reference internal" href="#tagpresent-kiem-tra-co-the-khong"><code class="docutils literal notranslate"><span class="pre">tagPresent()</span></code> - Kiểm tra có thẻ không</a></li>
<li class="toctree-l4"><a class="reference internal" href="#scan-card-quet-the-nhanh"><code class="docutils literal notranslate"><span class="pre">scan_card()</span></code> - Quét thẻ nhanh</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="#quan-ly-danh-sach-the">Quản lý danh sách thẻ</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#load-list-list-name-tai-danh-sach-the"><code class="docutils literal notranslate"><span class="pre">load_list(list_name)</span></code> - Tải danh sách thẻ</a></li>
<li class="toctree-l4"><a class="reference internal" href="#scan-and-add-card-list-name-quet-va-them-the"><code class="docutils literal notranslate"><span class="pre">scan_and_add_card(list_name)</span></code> - Quét và thêm thẻ</a></li>
<li class="toctree-l4"><a class="reference internal" href="#scan-and-check-list-name-kiem-tra-the-co-trong-danh-sach"><code class="docutils literal notranslate"><span class="pre">scan_and_check(list_name)</span></code> - Kiểm tra thẻ có trong danh sách</a></li>
<li class="toctree-l4"><a class="reference internal" href="#scan-and-remove-card-list-name-xoa-the-khoi-danh-sach"><code class="docutils literal notranslate"><span class="pre">scan_and_remove_card(list_name)</span></code> - Xóa thẻ khỏi danh sách</a></li>
<li class="toctree-l4"><a class="reference internal" href="#clear-list-list-name-xoa-toan-bo-danh-sach"><code class="docutils literal notranslate"><span class="pre">clear_list(list_name)</span></code> - Xóa toàn bộ danh sách</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-blockly">Ví dụ Blockly</a></li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-python">Ví dụ Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban-he-thong-kiem-soat-truy-cap">Ví dụ cơ bản - Hệ thống kiểm soát truy cập</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao-he-thong-diem-danh-thong-minh">Ví dụ nâng cao - Hệ thống điểm danh thông minh</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#giai-thich-ma">Giải thích mã</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban">Ví dụ cơ bản:</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao">Ví dụ nâng cao:</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#bai-tap-mo-rong">Bài tập mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-thuong-gap">Lỗi thường gặp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-nguyen-tham-khao">Tài nguyên tham khảo</a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="ultrasonic.html">BeeUltrasonic</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">BeeRC522</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/4.extensions/rc522.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="beerc522">
<h1>BeeRC522<a class="headerlink" href="#beerc522" title="Link to this heading"></a></h1>
<section id="gioi-thieu">
<h2>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p>BeeRC522 là module đọc thẻ RFID/NFC sử dụng chip RC522, hỗ trợ giao tiếp I2C và có thể đọc các loại thẻ MIFARE Classic và NTAG. Module này cho phép nhận diện thẻ từ xa, lưu trữ danh sách thẻ và thực hiện các chức năng kiểm soát truy cập.</p>
<p>Module tích hợp antenna 13.56MHz và có thể đọc thẻ ở khoảng cách lên đến 5cm. Đây là giải pháp lý tưởng cho các hệ thống bảo mật, điểm danh, thanh toán và quản lý tài sản.</p>
<p><strong>Ứng dụng thực tế:</strong></p>
<ul class="simple">
<li><p>Hệ thống kiểm soát ra vào</p></li>
<li><p>Máy chấm công tự động</p></li>
<li><p>Hệ thống thanh toán không tiếp xúc</p></li>
<li><p>Quản lý thư viện/tài sản</p></li>
<li><p>Trò chơi tương tác với thẻ</p></li>
<li><p>Hệ thống điểm danh học sinh</p></li>
<li><p>Robot pet với nhận diện chủ nhân</p></li>
</ul>
</section>
<section id="thong-so-ky-thuat">
<h2>Thông số kỹ thuật<a class="headerlink" href="#thong-so-ky-thuat" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thông số</p></th>
<th class="head"><p>Giá trị</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Chip điều khiển</p></td>
<td><p>RC522</p></td>
</tr>
<tr class="row-odd"><td><p>Tần số hoạt động</p></td>
<td><p>13.56MHz</p></td>
</tr>
<tr class="row-even"><td><p>Giao tiếp</p></td>
<td><p>I2C</p></td>
</tr>
<tr class="row-odd"><td><p>Địa chỉ I2C mặc định</p></td>
<td><p>0x2C</p></td>
</tr>
<tr class="row-even"><td><p>Khoảng cách đọc</p></td>
<td><p>0-50mm</p></td>
</tr>
<tr class="row-odd"><td><p>Loại thẻ hỗ trợ</p></td>
<td><p>MIFARE Classic, NTAG213/215/216</p></td>
</tr>
<tr class="row-even"><td><p>Tốc độ truyền</p></td>
<td><p>106 kbit/s</p></td>
</tr>
<tr class="row-odd"><td><p>Điện áp hoạt động</p></td>
<td><p>3.3V</p></td>
</tr>
<tr class="row-even"><td><p>Dòng tiêu thụ</p></td>
<td><p>13-26mA</p></td>
</tr>
<tr class="row-odd"><td><p>Thời gian đọc thẻ</p></td>
<td><p>&lt;100ms</p></td>
</tr>
<tr class="row-even"><td><p>Antenna</p></td>
<td><p>Tích hợp PCB antenna</p></td>
</tr>
</tbody>
</table>
</section>
<section id="giao-dien-lap-trinh">
<h2>Giao diện lập trình<a class="headerlink" href="#giao-dien-lap-trinh" title="Link to this heading"></a></h2>
<section id="khoi-tao">
<h3>Khởi tạo<a class="headerlink" href="#khoi-tao" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeRC522</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeRC522</span>

<span class="c1"># Khởi tạo với địa chỉ mặc định</span>
<span class="n">rfid</span> <span class="o">=</span> <span class="n">BeeRC522</span><span class="p">(</span><span class="n">PORT1</span><span class="p">)</span>

<span class="c1"># Khởi tạo với địa chỉ tùy chỉnh</span>
<span class="n">rfid</span> <span class="o">=</span> <span class="n">BeeRC522</span><span class="p">(</span><span class="n">PORT2</span><span class="p">,</span> <span class="n">address</span><span class="o">=</span><span class="mh">0x2D</span><span class="p">)</span>

<span class="c1"># Khởi tạo với ASW switch</span>
<span class="n">rfid</span> <span class="o">=</span> <span class="n">BeeRC522</span><span class="p">(</span><span class="n">PORT1</span><span class="p">,</span> <span class="n">asw</span><span class="o">=</span><span class="p">[</span><span class="mi">0</span><span class="p">,</span> <span class="mi">1</span><span class="p">])</span>  <span class="c1"># Địa chỉ = 0x2C + 0 + 2*1 = 0x2E</span>
</pre></div>
</div>
</section>
<section id="doc-the-co-ban">
<h3>Đọc thẻ cơ bản<a class="headerlink" href="#doc-the-co-ban" title="Link to this heading"></a></h3>
<section id="readid-detail-false-doc-id-the">
<h4><code class="docutils literal notranslate"><span class="pre">readID(detail=False)</span></code> - Đọc ID thẻ<a class="headerlink" href="#readid-detail-false-doc-id-the" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Đọc ID dạng chuỗi</span>
<span class="n">card_id</span> <span class="o">=</span> <span class="n">rfid</span><span class="o">.</span><span class="n">readID</span><span class="p">()</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Card ID: </span><span class="si">{</span><span class="n">card_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>

<span class="c1"># Đọc thông tin chi tiết</span>
<span class="n">card_info</span> <span class="o">=</span> <span class="n">rfid</span><span class="o">.</span><span class="n">readID</span><span class="p">(</span><span class="n">detail</span><span class="o">=</span><span class="kc">True</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Success: </span><span class="si">{</span><span class="n">card_info</span><span class="p">[</span><span class="s1">&#39;success&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;ID: </span><span class="si">{</span><span class="n">card_info</span><span class="p">[</span><span class="s1">&#39;id_formatted&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Type: </span><span class="si">{</span><span class="n">card_info</span><span class="p">[</span><span class="s1">&#39;type&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="tagpresent-kiem-tra-co-the-khong">
<h4><code class="docutils literal notranslate"><span class="pre">tagPresent()</span></code> - Kiểm tra có thẻ không<a class="headerlink" href="#tagpresent-kiem-tra-co-the-khong" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="n">rfid</span><span class="o">.</span><span class="n">tagPresent</span><span class="p">():</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Có thẻ trong vùng đọc&quot;</span><span class="p">)</span>
    <span class="n">card_id</span> <span class="o">=</span> <span class="n">rfid</span><span class="o">.</span><span class="n">readID</span><span class="p">()</span>
<span class="k">else</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Không có thẻ&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="scan-card-quet-the-nhanh">
<h4><code class="docutils literal notranslate"><span class="pre">scan_card()</span></code> - Quét thẻ nhanh<a class="headerlink" href="#scan-card-quet-the-nhanh" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="n">card_id</span> <span class="o">=</span> <span class="n">rfid</span><span class="o">.</span><span class="n">scan_card</span><span class="p">()</span>
<span class="k">if</span> <span class="n">card_id</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Quét được thẻ: </span><span class="si">{</span><span class="n">card_id</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Không có thẻ&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
<section id="quan-ly-danh-sach-the">
<h3>Quản lý danh sách thẻ<a class="headerlink" href="#quan-ly-danh-sach-the" title="Link to this heading"></a></h3>
<section id="load-list-list-name-tai-danh-sach-the">
<h4><code class="docutils literal notranslate"><span class="pre">load_list(list_name)</span></code> - Tải danh sách thẻ<a class="headerlink" href="#load-list-list-name-tai-danh-sach-the" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Tải danh sách từ file</span>
<span class="n">authorized_cards</span> <span class="o">=</span> <span class="n">rfid</span><span class="o">.</span><span class="n">load_list</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Có </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">authorized_cards</span><span class="p">)</span><span class="si">}</span><span class="s2"> thẻ được phép&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="scan-and-add-card-list-name-quet-va-them-the">
<h4><code class="docutils literal notranslate"><span class="pre">scan_and_add_card(list_name)</span></code> - Quét và thêm thẻ<a class="headerlink" href="#scan-and-add-card-list-name-quet-va-them-the" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Thêm thẻ vào danh sách</span>
<span class="k">if</span> <span class="n">rfid</span><span class="o">.</span><span class="n">scan_and_add_card</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Đã thêm thẻ mới&quot;</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Không có thẻ hoặc thẻ đã tồn tại&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="scan-and-check-list-name-kiem-tra-the-co-trong-danh-sach">
<h4><code class="docutils literal notranslate"><span class="pre">scan_and_check(list_name)</span></code> - Kiểm tra thẻ có trong danh sách<a class="headerlink" href="#scan-and-check-list-name-kiem-tra-the-co-trong-danh-sach" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Kiểm tra thẻ có được phép không</span>
<span class="k">if</span> <span class="n">rfid</span><span class="o">.</span><span class="n">scan_and_check</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">):</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Thẻ hợp lệ - Cho phép truy cập&quot;</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Thẻ không hợp lệ - Từ chối truy cập&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="scan-and-remove-card-list-name-xoa-the-khoi-danh-sach">
<h4><code class="docutils literal notranslate"><span class="pre">scan_and_remove_card(list_name)</span></code> - Xóa thẻ khỏi danh sách<a class="headerlink" href="#scan-and-remove-card-list-name-xoa-the-khoi-danh-sach" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Xóa thẻ khỏi danh sách</span>
<span class="n">rfid</span><span class="o">.</span><span class="n">scan_and_remove_card</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Đã xóa thẻ khỏi danh sách&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="clear-list-list-name-xoa-toan-bo-danh-sach">
<h4><code class="docutils literal notranslate"><span class="pre">clear_list(list_name)</span></code> - Xóa toàn bộ danh sách<a class="headerlink" href="#clear-list-list-name-xoa-toan-bo-danh-sach" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Xóa tất cả thẻ trong danh sách</span>
<span class="n">rfid</span><span class="o">.</span><span class="n">clear_list</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">)</span>
<span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Đã xóa toàn bộ danh sách&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="vi-du-blockly">
<h2>Ví dụ Blockly<a class="headerlink" href="#vi-du-blockly" title="Link to this heading"></a></h2>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">when</span> <span class="n">program</span> <span class="n">starts</span><span class="p">:</span>
    <span class="nb">set</span> <span class="n">rfid_reader</span> <span class="n">to</span> <span class="n">BeeRC522</span> <span class="n">at</span> <span class="n">PORT1</span>
    <span class="n">load</span> <span class="n">authorized_cards</span> <span class="nb">list</span>
    <span class="n">show</span> <span class="s2">&quot;RFID Ready&quot;</span> <span class="n">on</span> <span class="n">OLED</span>

<span class="n">forever</span><span class="p">:</span>
    <span class="k">if</span> <span class="n">card</span> <span class="ow">is</span> <span class="n">present</span><span class="p">:</span>
        <span class="nb">set</span> <span class="n">card_id</span> <span class="n">to</span> <span class="n">scan</span> <span class="n">card</span>

        <span class="k">if</span> <span class="n">card_id</span> <span class="ow">is</span> <span class="ow">in</span> <span class="n">authorized_cards</span><span class="p">:</span>
            <span class="n">show</span> <span class="s2">&quot;ACCESS GRANTED&quot;</span> <span class="n">on</span> <span class="n">OLED</span>
            <span class="nb">set</span> <span class="n">LED</span> <span class="n">to</span> <span class="n">GREEN</span>
            <span class="n">play</span> <span class="n">success</span> <span class="n">sound</span>
            <span class="n">wait</span> <span class="mi">2</span> <span class="n">seconds</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">show</span> <span class="s2">&quot;ACCESS DENIED&quot;</span> <span class="n">on</span> <span class="n">OLED</span>
            <span class="nb">set</span> <span class="n">LED</span> <span class="n">to</span> <span class="n">RED</span>
            <span class="n">play</span> <span class="n">error</span> <span class="n">sound</span>
            <span class="n">wait</span> <span class="mi">1</span> <span class="n">second</span>

    <span class="n">when</span> <span class="n">button</span> <span class="n">A</span> <span class="n">pressed</span><span class="p">:</span>
        <span class="n">show</span> <span class="s2">&quot;Add new card...&quot;</span> <span class="n">on</span> <span class="n">OLED</span>
        <span class="k">if</span> <span class="n">scan</span> <span class="ow">and</span> <span class="n">add</span> <span class="n">card</span> <span class="n">to</span> <span class="n">authorized_cards</span><span class="p">:</span>
            <span class="n">show</span> <span class="s2">&quot;Card added!&quot;</span> <span class="n">on</span> <span class="n">OLED</span>
            <span class="n">play</span> <span class="n">confirmation</span> <span class="n">sound</span>
</pre></div>
</div>
</section>
<section id="vi-du-python">
<h2>Ví dụ Python<a class="headerlink" href="#vi-du-python" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban-he-thong-kiem-soat-truy-cap">
<h3>Ví dụ cơ bản - Hệ thống kiểm soát truy cập<a class="headerlink" href="#vi-du-co-ban-he-thong-kiem-soat-truy-cap" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeRC522</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeRC522</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="k">def</span><span class="w"> </span><span class="nf">setup</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Khởi tạo hệ thống RFID&quot;&quot;&quot;</span>
    <span class="k">global</span> <span class="n">rfid</span>

    <span class="k">try</span><span class="p">:</span>
        <span class="c1"># Khởi tạo RFID reader</span>
        <span class="n">rfid</span> <span class="o">=</span> <span class="n">BeeRC522</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>

        <span class="c1"># Tải danh sách thẻ được phép</span>
        <span class="n">authorized_cards</span> <span class="o">=</span> <span class="n">rfid</span><span class="o">.</span><span class="n">load_list</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">)</span>

        <span class="c1"># Hiển thị thông tin khởi tạo</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;RFID Access&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Control System&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Cards: </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="n">authorized_cards</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># LED xanh dương</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4&quot;</span><span class="p">)</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
        <span class="k">return</span> <span class="kc">True</span>

    <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;RFID Error!&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="nb">str</span><span class="p">(</span><span class="n">e</span><span class="p">)[:</span><span class="mi">16</span><span class="p">],</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED đỏ</span>
        <span class="k">return</span> <span class="kc">False</span>

<span class="k">def</span><span class="w"> </span><span class="nf">access_control</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Vòng lặp kiểm soát truy cập&quot;&quot;&quot;</span>
    <span class="n">last_card_time</span> <span class="o">=</span> <span class="mi">0</span>
    <span class="n">card_cooldown</span> <span class="o">=</span> <span class="mi">2</span>  <span class="c1"># 2 giây cooldown giữa các lần đọc</span>

    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

        <span class="c1"># Hiển thị trạng thái chờ</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Please scan&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;your card&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED vàng</span>

        <span class="c1"># Kiểm tra có thẻ không</span>
        <span class="k">if</span> <span class="n">rfid</span><span class="o">.</span><span class="n">tagPresent</span><span class="p">()</span> <span class="ow">and</span> <span class="p">(</span><span class="n">current_time</span> <span class="o">-</span> <span class="n">last_card_time</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">card_cooldown</span><span class="p">:</span>
            <span class="n">card_id</span> <span class="o">=</span> <span class="n">rfid</span><span class="o">.</span><span class="n">readID</span><span class="p">()</span>

            <span class="k">if</span> <span class="n">card_id</span><span class="p">:</span>
                <span class="n">last_card_time</span> <span class="o">=</span> <span class="n">current_time</span>

                <span class="c1"># Kiểm tra thẻ có trong danh sách không</span>
                <span class="k">if</span> <span class="n">rfid</span><span class="o">.</span><span class="n">scan_and_check</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">):</span>
                    <span class="c1"># Truy cập được phép</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;ACCESS&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">5</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;GRANTED&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">card_id</span><span class="p">[:</span><span class="mi">12</span><span class="p">],</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">35</span><span class="p">)</span>  <span class="c1"># Hiển thị ID (12 ký tự đầu)</span>

                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED xanh</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4 C5&quot;</span><span class="p">)</span>

                    <span class="c1"># Mô phỏng mở cửa</span>
                    <span class="k">if</span> <span class="nb">hasattr</span><span class="p">(</span><span class="n">bee</span><span class="p">,</span> <span class="s1">&#39;servo&#39;</span><span class="p">):</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">servo</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">90</span><span class="p">)</span>  <span class="c1"># Mở cửa</span>
                        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>
                        <span class="n">bee</span><span class="o">.</span><span class="n">servo</span><span class="o">.</span><span class="n">position</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>   <span class="c1"># Đóng cửa</span>

                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>

                <span class="k">else</span><span class="p">:</span>
                    <span class="c1"># Truy cập bị từ chối</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;ACCESS&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">5</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;DENIED&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Unknown Card&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">35</span><span class="p">)</span>

                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED đỏ</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;A4:0.2 SIL:0.1 A4:0.2 SIL:0.1 A4:0.2&quot;</span><span class="p">)</span>

                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>

        <span class="c1"># Kiểm tra nút thêm thẻ mới</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_A</span><span class="p">):</span>
            <span class="n">add_new_card</span><span class="p">()</span>

        <span class="c1"># Kiểm tra nút xóa thẻ</span>
        <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
            <span class="n">remove_card</span><span class="p">()</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

<span class="k">def</span><span class="w"> </span><span class="nf">add_new_card</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Thêm thẻ mới vào danh sách&quot;&quot;&quot;</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;ADD NEW CARD&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Scan card to&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;add...&quot;</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># LED cyan</span>

    <span class="n">timeout</span> <span class="o">=</span> <span class="mi">10</span>  <span class="c1"># 10 giây timeout</span>
    <span class="n">start_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

    <span class="k">while</span> <span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">start_time</span><span class="p">)</span> <span class="o">&lt;</span> <span class="n">timeout</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">rfid</span><span class="o">.</span><span class="n">scan_and_add_card</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">):</span>
            <span class="c1"># Thêm thành công</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;CARD ADDED&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Successfully!&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>

            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED xanh</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4 C5 G4&quot;</span><span class="p">)</span>

            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
            <span class="k">return</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

    <span class="c1"># Timeout</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;TIMEOUT&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;No card added&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED vàng</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

<span class="k">def</span><span class="w"> </span><span class="nf">remove_card</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Xóa thẻ khỏi danh sách&quot;&quot;&quot;</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;REMOVE CARD&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Scan card to&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;remove...&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># LED tím</span>

    <span class="n">timeout</span> <span class="o">=</span> <span class="mi">10</span>  <span class="c1"># 10 giây timeout</span>
    <span class="n">start_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

    <span class="k">while</span> <span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">start_time</span><span class="p">)</span> <span class="o">&lt;</span> <span class="n">timeout</span><span class="p">:</span>
        <span class="k">if</span> <span class="n">rfid</span><span class="o">.</span><span class="n">tagPresent</span><span class="p">():</span>
            <span class="n">card_id</span> <span class="o">=</span> <span class="n">rfid</span><span class="o">.</span><span class="n">readID</span><span class="p">()</span>
            <span class="k">if</span> <span class="n">card_id</span> <span class="ow">and</span> <span class="n">rfid</span><span class="o">.</span><span class="n">scan_and_check</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">):</span>
                <span class="c1"># Thẻ có trong danh sách - xóa</span>
                <span class="n">rfid</span><span class="o">.</span><span class="n">scan_and_remove_card</span><span class="p">(</span><span class="s2">&quot;authorized&quot;</span><span class="p">)</span>

                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;CARD REMOVED&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Successfully!&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>

                <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">165</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED cam</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;G4 E4 C4&quot;</span><span class="p">)</span>

                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
                <span class="k">return</span>
            <span class="k">elif</span> <span class="n">card_id</span><span class="p">:</span>
                <span class="c1"># Thẻ không có trong danh sách</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;CARD NOT&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;IN LIST&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED đỏ</span>
                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

    <span class="c1"># Timeout</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;TIMEOUT&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;No card removed&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># LED vàng</span>
    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

<span class="c1"># Chạy chương trình</span>
<span class="k">if</span> <span class="n">setup</span><span class="p">():</span>
    <span class="n">access_control</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="vi-du-nang-cao-he-thong-diem-danh-thong-minh">
<h3>Ví dụ nâng cao - Hệ thống điểm danh thông minh<a class="headerlink" href="#vi-du-nang-cao-he-thong-diem-danh-thong-minh" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeRC522</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeRC522</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBrain</span><span class="w"> </span><span class="kn">import</span> <span class="n">bee</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">json</span>

<span class="k">class</span><span class="w"> </span><span class="nc">AttendanceSystem</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">rfid</span> <span class="o">=</span> <span class="n">BeeRC522</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">PORT1</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">students</span> <span class="o">=</span> <span class="p">{}</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">attendance_log</span> <span class="o">=</span> <span class="p">[]</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">session_active</span> <span class="o">=</span> <span class="kc">False</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">setup</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Khởi tạo hệ thống điểm danh&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="c1"># Tải danh sách học sinh</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">load_students</span><span class="p">()</span>

            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Attendance&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;System Ready&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Students: </span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">students</span><span class="p">)</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4 C5&quot;</span><span class="p">)</span>

            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">True</span>

        <span class="k">except</span> <span class="ne">Exception</span> <span class="k">as</span> <span class="n">e</span><span class="p">:</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;System Error!&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="k">return</span> <span class="kc">False</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">load_students</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Tải danh sách học sinh từ file&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;students.json&quot;</span><span class="p">,</span> <span class="s2">&quot;r&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">students</span> <span class="o">=</span> <span class="n">json</span><span class="o">.</span><span class="n">load</span><span class="p">(</span><span class="n">f</span><span class="p">)</span>
        <span class="k">except</span><span class="p">:</span>
            <span class="c1"># File không tồn tại - tạo danh sách mẫu</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">students</span> <span class="o">=</span> <span class="p">{</span>
                <span class="s2">&quot;04:A3:B2:C1&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Nguyen Van A&quot;</span><span class="p">,</span> <span class="s2">&quot;class&quot;</span><span class="p">:</span> <span class="s2">&quot;10A1&quot;</span><span class="p">},</span>
                <span class="s2">&quot;05:B4:C3:D2&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Tran Thi B&quot;</span><span class="p">,</span> <span class="s2">&quot;class&quot;</span><span class="p">:</span> <span class="s2">&quot;10A1&quot;</span><span class="p">},</span>
                <span class="s2">&quot;06:C5:D4:E3&quot;</span><span class="p">:</span> <span class="p">{</span><span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="s2">&quot;Le Van C&quot;</span><span class="p">,</span> <span class="s2">&quot;class&quot;</span><span class="p">:</span> <span class="s2">&quot;10A2&quot;</span><span class="p">}</span>
            <span class="p">}</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">save_students</span><span class="p">()</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">save_students</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Lưu danh sách học sinh&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="s2">&quot;students.json&quot;</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
                <span class="n">json</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">students</span><span class="p">,</span> <span class="n">f</span><span class="p">)</span>
        <span class="k">except</span><span class="p">:</span>
            <span class="k">pass</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">save_attendance_log</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Lưu log điểm danh&quot;&quot;&quot;</span>
        <span class="k">try</span><span class="p">:</span>
            <span class="n">filename</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;attendance_</span><span class="si">{</span><span class="n">time</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s1">&#39;%Y%m</span><span class="si">%d</span><span class="s1">&#39;</span><span class="p">)</span><span class="si">}</span><span class="s2">.json&quot;</span>
            <span class="k">with</span> <span class="nb">open</span><span class="p">(</span><span class="n">filename</span><span class="p">,</span> <span class="s2">&quot;w&quot;</span><span class="p">)</span> <span class="k">as</span> <span class="n">f</span><span class="p">:</span>
                <span class="n">json</span><span class="o">.</span><span class="n">dump</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">attendance_log</span><span class="p">,</span> <span class="n">f</span><span class="p">)</span>
        <span class="k">except</span><span class="p">:</span>
            <span class="k">pass</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">start_session</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Bắt đầu phiên điểm danh&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">session_active</span> <span class="o">=</span> <span class="kc">True</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">attendance_log</span> <span class="o">=</span> <span class="p">[]</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;SESSION&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;STARTED&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Scan your card&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4&quot;</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">end_session</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Kết thúc phiên điểm danh&quot;&quot;&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">session_active</span> <span class="o">=</span> <span class="kc">False</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">save_attendance_log</span><span class="p">()</span>

        <span class="n">present_count</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">attendance_log</span><span class="p">)</span>
        <span class="n">total_count</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">students</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;SESSION&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;ENDED&quot;</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Present: </span><span class="si">{</span><span class="n">present_count</span><span class="si">}</span><span class="s2">/</span><span class="si">{</span><span class="n">total_count</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;G4 E4 C4&quot;</span><span class="p">)</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">3</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">check_attendance</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">card_id</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Kiểm tra và ghi nhận điểm danh&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="n">card_id</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">students</span><span class="p">:</span>
            <span class="n">student</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">students</span><span class="p">[</span><span class="n">card_id</span><span class="p">]</span>

            <span class="c1"># Kiểm tra đã điểm danh chưa</span>
            <span class="n">already_present</span> <span class="o">=</span> <span class="nb">any</span><span class="p">(</span><span class="n">log</span><span class="p">[</span><span class="s1">&#39;card_id&#39;</span><span class="p">]</span> <span class="o">==</span> <span class="n">card_id</span> <span class="k">for</span> <span class="n">log</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">attendance_log</span><span class="p">)</span>

            <span class="k">if</span> <span class="ow">not</span> <span class="n">already_present</span><span class="p">:</span>
                <span class="c1"># Ghi nhận điểm danh</span>
                <span class="n">attendance_record</span> <span class="o">=</span> <span class="p">{</span>
                    <span class="s1">&#39;card_id&#39;</span><span class="p">:</span> <span class="n">card_id</span><span class="p">,</span>
                    <span class="s1">&#39;name&#39;</span><span class="p">:</span> <span class="n">student</span><span class="p">[</span><span class="s1">&#39;name&#39;</span><span class="p">],</span>
                    <span class="s1">&#39;class&#39;</span><span class="p">:</span> <span class="n">student</span><span class="p">[</span><span class="s1">&#39;class&#39;</span><span class="p">],</span>
                    <span class="s1">&#39;time&#39;</span><span class="p">:</span> <span class="n">time</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s1">&#39;%H:%M:%S&#39;</span><span class="p">),</span>
                    <span class="s1">&#39;timestamp&#39;</span><span class="p">:</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>
                <span class="p">}</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">attendance_log</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">attendance_record</span><span class="p">)</span>

                <span class="c1"># Hiển thị thông tin</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;PRESENT&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">student</span><span class="p">[</span><span class="s1">&#39;name&#39;</span><span class="p">][:</span><span class="mi">16</span><span class="p">],</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">student</span><span class="p">[</span><span class="s1">&#39;class&#39;</span><span class="p">],</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Time: </span><span class="si">{</span><span class="n">attendance_record</span><span class="p">[</span><span class="s1">&#39;time&#39;</span><span class="p">]</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">45</span><span class="p">)</span>

                <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Xanh</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C5 E5&quot;</span><span class="p">)</span>

                <span class="k">return</span> <span class="kc">True</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="c1"># Đã điểm danh rồi</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;ALREADY&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;PRESENT&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">student</span><span class="p">[</span><span class="s1">&#39;name&#39;</span><span class="p">][:</span><span class="mi">16</span><span class="p">],</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

                <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Vàng</span>
                <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;E4 E4&quot;</span><span class="p">)</span>

                <span class="k">return</span> <span class="kc">False</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Thẻ không hợp lệ</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;UNKNOWN&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;STUDENT&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">card_id</span><span class="p">[:</span><span class="mi">12</span><span class="p">],</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

            <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Đỏ</span>
            <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;A4:0.2 A4:0.2&quot;</span><span class="p">)</span>

            <span class="k">return</span> <span class="kc">False</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">add_student</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Thêm học sinh mới&quot;&quot;&quot;</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;ADD STUDENT&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Scan card...&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>

        <span class="n">timeout</span> <span class="o">=</span> <span class="mi">15</span>
        <span class="n">start_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

        <span class="k">while</span> <span class="p">(</span><span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span> <span class="o">-</span> <span class="n">start_time</span><span class="p">)</span> <span class="o">&lt;</span> <span class="n">timeout</span><span class="p">:</span>
            <span class="n">card_id</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">rfid</span><span class="o">.</span><span class="n">scan_card</span><span class="p">()</span>
            <span class="k">if</span> <span class="n">card_id</span><span class="p">:</span>
                <span class="k">if</span> <span class="n">card_id</span> <span class="ow">not</span> <span class="ow">in</span> <span class="bp">self</span><span class="o">.</span><span class="n">students</span><span class="p">:</span>
                    <span class="c1"># Thẻ mới - thêm vào danh sách</span>
                    <span class="n">student_name</span> <span class="o">=</span> <span class="sa">f</span><span class="s2">&quot;Student_</span><span class="si">{</span><span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">students</span><span class="p">)</span><span class="o">+</span><span class="mi">1</span><span class="si">}</span><span class="s2">&quot;</span>
                    <span class="n">student_class</span> <span class="o">=</span> <span class="s2">&quot;10A1&quot;</span>

                    <span class="bp">self</span><span class="o">.</span><span class="n">students</span><span class="p">[</span><span class="n">card_id</span><span class="p">]</span> <span class="o">=</span> <span class="p">{</span>
                        <span class="s2">&quot;name&quot;</span><span class="p">:</span> <span class="n">student_name</span><span class="p">,</span>
                        <span class="s2">&quot;class&quot;</span><span class="p">:</span> <span class="n">student_class</span>
                    <span class="p">}</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">save_students</span><span class="p">()</span>

                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;STUDENT&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;ADDED&quot;</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="n">student_name</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4 C5&quot;</span><span class="p">)</span>

                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
                    <span class="k">return</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;STUDENT&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;EXISTS&quot;</span><span class="p">,</span> <span class="mi">30</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

        <span class="c1"># Timeout</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;TIMEOUT&quot;</span><span class="p">,</span> <span class="mi">25</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">show_statistics</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Hiển thị thống kê điểm danh&quot;&quot;&quot;</span>
        <span class="n">present_count</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">attendance_log</span><span class="p">)</span>
        <span class="n">total_count</span> <span class="o">=</span> <span class="nb">len</span><span class="p">(</span><span class="bp">self</span><span class="o">.</span><span class="n">students</span><span class="p">)</span>
        <span class="n">absent_count</span> <span class="o">=</span> <span class="n">total_count</span> <span class="o">-</span> <span class="n">present_count</span>

        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;STATISTICS&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Present: </span><span class="si">{</span><span class="n">present_count</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">10</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Absent: </span><span class="si">{</span><span class="n">absent_count</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>
        <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Total: </span><span class="si">{</span><span class="n">total_count</span><span class="si">}</span><span class="s2">&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">45</span><span class="p">)</span>

        <span class="c1"># Hiển thị trong 5 giây</span>
        <span class="k">for</span> <span class="n">i</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">50</span><span class="p">):</span>
            <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_A</span><span class="p">)</span> <span class="ow">or</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
                <span class="k">break</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Vòng lặp chính&quot;&quot;&quot;</span>
        <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">setup</span><span class="p">():</span>
            <span class="k">return</span>

        <span class="n">last_card_time</span> <span class="o">=</span> <span class="mi">0</span>
        <span class="n">card_cooldown</span> <span class="o">=</span> <span class="mi">1</span>

        <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
            <span class="k">try</span><span class="p">:</span>
                <span class="k">if</span> <span class="ow">not</span> <span class="bp">self</span><span class="o">.</span><span class="n">session_active</span><span class="p">:</span>
                    <span class="c1"># Chế độ chờ</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Press A: Start&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Press B: Add&quot;</span><span class="p">,</span> <span class="mi">15</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
                    <span class="n">bee</span><span class="o">.</span><span class="n">neopixel</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">LED1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>

                    <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_A</span><span class="p">):</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">start_session</span><span class="p">()</span>
                    <span class="k">elif</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">add_student</span><span class="p">()</span>

                <span class="k">else</span><span class="p">:</span>
                    <span class="c1"># Phiên điểm danh đang hoạt động</span>
                    <span class="n">current_time</span> <span class="o">=</span> <span class="n">time</span><span class="o">.</span><span class="n">time</span><span class="p">()</span>

                    <span class="c1"># Đọc thẻ</span>
                    <span class="k">if</span> <span class="p">(</span><span class="n">current_time</span> <span class="o">-</span> <span class="n">last_card_time</span><span class="p">)</span> <span class="o">&gt;</span> <span class="n">card_cooldown</span><span class="p">:</span>
                        <span class="n">card_id</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">rfid</span><span class="o">.</span><span class="n">scan_card</span><span class="p">()</span>
                        <span class="k">if</span> <span class="n">card_id</span><span class="p">:</span>
                            <span class="n">last_card_time</span> <span class="o">=</span> <span class="n">current_time</span>
                            <span class="bp">self</span><span class="o">.</span><span class="n">check_attendance</span><span class="p">(</span><span class="n">card_id</span><span class="p">)</span>
                            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>  <span class="c1"># Hiển thị kết quả 2 giây</span>

                    <span class="c1"># Kiểm tra nút điều khiển</span>
                    <span class="k">if</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_A</span><span class="p">):</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">show_statistics</span><span class="p">()</span>
                    <span class="k">elif</span> <span class="n">bee</span><span class="o">.</span><span class="n">is_button_pressed</span><span class="p">(</span><span class="n">bee</span><span class="o">.</span><span class="n">BUTTON_B</span><span class="p">):</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">end_session</span><span class="p">()</span>

                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>

            <span class="k">except</span> <span class="ne">KeyboardInterrupt</span><span class="p">:</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">session_active</span><span class="p">:</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">end_session</span><span class="p">()</span>
                <span class="k">break</span>

<span class="c1"># Chạy hệ thống điểm danh</span>
<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="n">system</span> <span class="o">=</span> <span class="n">AttendanceSystem</span><span class="p">()</span>
    <span class="n">system</span><span class="o">.</span><span class="n">run</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="giai-thich-ma">
<h2>Giải thích mã<a class="headerlink" href="#giai-thich-ma" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban">
<h3>Ví dụ cơ bản:<a class="headerlink" href="#vi-du-co-ban" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Access control</strong>: Hệ thống kiểm soát truy cập đơn giản</p></li>
<li><p><strong>Card management</strong>: Thêm/xóa thẻ từ danh sách được phép</p></li>
<li><p><strong>Visual feedback</strong>: Hiển thị trạng thái trên OLED và LED</p></li>
<li><p><strong>Cooldown mechanism</strong>: Tránh đọc thẻ liên tục</p></li>
</ol>
</section>
<section id="vi-du-nang-cao">
<h3>Ví dụ nâng cao:<a class="headerlink" href="#vi-du-nang-cao" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Student database</strong>: Quản lý thông tin học sinh với JSON</p></li>
<li><p><strong>Attendance logging</strong>: Ghi log điểm danh với timestamp</p></li>
<li><p><strong>Session management</strong>: Quản lý phiên điểm danh</p></li>
<li><p><strong>Statistics</strong>: Thống kê và báo cáo điểm danh</p></li>
</ol>
</section>
</section>
<section id="bai-tap-mo-rong">
<h2>Bài tập mở rộng<a class="headerlink" href="#bai-tap-mo-rong" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p><strong>Hệ thống thanh toán</strong>: Tạo hệ thống thanh toán với thẻ RFID và quản lý số dư</p></li>
<li><p><strong>Smart locker</strong>: Tủ khóa thông minh mở bằng thẻ RFID cá nhân</p></li>
<li><p><strong>Pet feeder</strong>: Máy cho ăn thú cưng tự động nhận diện bằng thẻ RFID</p></li>
</ol>
</section>
<section id="loi-thuong-gap">
<h2>Lỗi thường gặp<a class="headerlink" href="#loi-thuong-gap" title="Link to this heading"></a></h2>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Không đọc được thẻ</p>
<p><strong>Nguyên nhân</strong>: Khoảng cách quá xa hoặc thẻ không tương thích</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Đưa thẻ gần hơn (&lt; 5cm)</p></li>
<li><p>Kiểm tra thẻ có phải MIFARE Classic/NTAG</p></li>
<li><p>Đảm bảo thẻ không bị hỏng</p></li>
<li><p>Kiểm tra antenna không bị che khuất</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Địa chỉ I2C không đúng</p>
<p><strong>Nguyên nhân</strong>: Module có địa chỉ I2C khác mặc định</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Kiểm tra ASW switch trên module</p></li>
<li><p>Thử các địa chỉ 0x2C, 0x2D, 0x2E, 0x2F</p></li>
<li><p>Sử dụng I2C scanner để tìm địa chỉ</p></li>
<li><p>Đọc datasheet module để xác định địa chỉ</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: File JSON bị lỗi</p>
<p><strong>Nguyên nhân</strong>: File danh sách thẻ bị corrupt hoặc format sai</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Xóa file JSON và tạo lại</p></li>
<li><p>Kiểm tra format JSON hợp lệ</p></li>
<li><p>Backup danh sách thẻ thường xuyên</p></li>
<li><p>Sử dụng try-except khi đọc file</p></li>
</ul>
</div>
</section>
<section id="tai-nguyen-tham-khao">
<h2>Tài nguyên tham khảo<a class="headerlink" href="#tai-nguyen-tham-khao" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference external" href="https://www.nxp.com/docs/en/data-sheet/MFRC522.pdf">RC522 Datasheet</a></p></li>
<li><p><a class="reference external" href="https://www.nxp.com/products/rfid-nfc/mifare-hf/mifare-classic:MC_41863">MIFARE Classic Documentation</a></p></li>
<li><p><a class="reference external" href="https://www.nxp.com/docs/en/data-sheet/NTAG213_215_216.pdf">NTAG213/215/216 Datasheet</a></p></li>
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">BeE Block IDE Online</a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="line-detect.html" class="btn btn-neutral float-left" title="BeeLineDetect" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="ultrasonic.html" class="btn btn-neutral float-right" title="BeeUltrasonic" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>