

<!DOCTYPE html>
<html class="writer-html5" lang="vi" data-content_root="../">
<head>
  <meta charset="utf-8" /><meta name="viewport" content="width=device-width, initial-scale=1" />

  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>BeeUltrasonic &mdash; Tài Liệu BeE Board v1.0</title>
      <link rel="stylesheet" type="text/css" href="../_static/pygments.css?v=b86133f3" />
      <link rel="stylesheet" type="text/css" href="../_static/css/theme.css?v=e59714d7" />
      <link rel="stylesheet" type="text/css" href="../_static/copybutton.css?v=76b2166b" />
      <link rel="stylesheet" type="text/css" href="../_static/custom.css?v=ec7d574e" />

  
    <link rel="shortcut icon" href="../_static/logo.png"/>
      <script src="../_static/jquery.js?v=5d32c60e"></script>
      <script src="../_static/_sphinx_javascript_frameworks_compat.js?v=2cd50e6c"></script>
      <script src="../_static/documentation_options.js?v=78054f06"></script>
      <script src="../_static/doctools.js?v=9a2dae69"></script>
      <script src="../_static/sphinx_highlight.js?v=dc90522c"></script>
      <script src="../_static/clipboard.min.js?v=a7894cd8"></script>
      <script src="../_static/copybutton.js?v=30646c52"></script>
      <script src="../_static/translations.js?v=c4f2f737"></script>
    <script src="../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../genindex.html" />
    <link rel="search" title="Tìm Kiếm" href="../search.html" />
    <link rel="next" title="BeE STEM Solutions" href="../5.about/index.html" />
    <link rel="prev" title="BeeRC522" href="rc522.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search"  style="background: #2980B9" >

          
          
          <a href="../index.html" class="icon icon-home">
            Tài Liệu BeE Board
              <img src="../_static/logo.png" class="logo" alt="Logo"/>
          </a>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" aria-label="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption" role="heading"><span class="caption-text">BeE Board V2</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/2.getting-started.html">Bắt đầu</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/3.hardware-overview.html">Giới thiệu phần cứng</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/4.programming-guide.html">Hướng dẫn lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../1.bee-board-v2/5.troubleshooting.html">Xử lý lỗi</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">BeE IDE</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/1.index.html">BeE IDE</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/2.installation.html">Cài đặt</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/3.user-interface.html">Giao diện</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/4.flashing-guide.html">Nạp chương trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../2.bee-ide/5.flashing-image.html">Nạp Firmware</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Ví dụ lập trình</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/1.index.html">Ví dụ lập trình</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/2.led-example.html">LED RGB</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/3.button-example.html">Nút nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/4.buzzer-example.html">Buzzer</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/5.oled-example.html">Màn hình OLED</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/6.motor-example.html">Động cơ</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/7.servo-example.html">Servo</a></li>
<li class="toctree-l1"><a class="reference internal" href="../3.examples/8.imu-example.html">Cảm biến IMU</a></li>
</ul>
<p class="caption" role="heading"><span class="caption-text">Module mở rộng</span></p>
<ul class="current">
<li class="toctree-l1"><a class="reference internal" href="1.index.html">Giới thiệu</a></li>
<li class="toctree-l1"><a class="reference internal" href="button.html">Module Nút Nhấn</a></li>
<li class="toctree-l1"><a class="reference internal" href="color-detect.html">BeeColorDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="dht11.html">Module DHT11</a></li>
<li class="toctree-l1"><a class="reference internal" href="led-segment.html">BeeLedSegment</a></li>
<li class="toctree-l1"><a class="reference internal" href="line-detect.html">BeeLineDetect</a></li>
<li class="toctree-l1"><a class="reference internal" href="rc522.html">BeeRC522</a></li>
<li class="toctree-l1 current"><a class="current reference internal" href="#">BeeUltrasonic</a><ul>
<li class="toctree-l2"><a class="reference internal" href="#gioi-thieu">Giới thiệu</a></li>
<li class="toctree-l2"><a class="reference internal" href="#thong-so-ky-thuat">Thông số kỹ thuật</a></li>
<li class="toctree-l2"><a class="reference internal" href="#giao-dien-lap-trinh">Giao diện lập trình</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#khoi-tao">Khởi tạo</a></li>
<li class="toctree-l3"><a class="reference internal" href="#cac-phuong-thuc-chinh">Các phương thức chính</a><ul>
<li class="toctree-l4"><a class="reference internal" href="#distance-mm-do-khoang-cach-mm"><code class="docutils literal notranslate"><span class="pre">distance_mm()</span></code> - Đo khoảng cách (mm)</a></li>
<li class="toctree-l4"><a class="reference internal" href="#distance-cm-do-khoang-cach-cm"><code class="docutils literal notranslate"><span class="pre">distance_cm()</span></code> - Đo khoảng cách (cm)</a></li>
<li class="toctree-l4"><a class="reference internal" href="#is-object-present-threshold-phat-hien-vat-the"><code class="docutils literal notranslate"><span class="pre">is_object_present(threshold)</span></code> - Phát hiện vật thể</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-blockly">Ví dụ Blockly</a></li>
<li class="toctree-l2"><a class="reference internal" href="#vi-du-python">Ví dụ Python</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban-do-khoang-cach-don-gian">Ví dụ cơ bản - Đo khoảng cách đơn giản</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao-robot-tranh-vat-can">Ví dụ nâng cao - Robot tránh vật cản</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#giai-thich-ma">Giải thích mã</a><ul>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-co-ban">Ví dụ cơ bản:</a></li>
<li class="toctree-l3"><a class="reference internal" href="#vi-du-nang-cao">Ví dụ nâng cao:</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="#bai-tap-mo-rong">Bài tập mở rộng</a></li>
<li class="toctree-l2"><a class="reference internal" href="#loi-thuong-gap">Lỗi thường gặp</a></li>
<li class="toctree-l2"><a class="reference internal" href="#tai-nguyen-tham-khao">Tài nguyên tham khảo</a></li>
</ul>
</li>
</ul>
<p class="caption" role="heading"><span class="caption-text">About</span></p>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../5.about/index.html">BeE STEM Solutions</a></li>
</ul>

        </div>
      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu"  style="background: #2980B9" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../index.html">Tài Liệu BeE Board</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="Page navigation">
  <ul class="wy-breadcrumbs">
      <li><a href="../index.html" class="icon icon-home" aria-label="Home"></a></li>
          <li class="breadcrumb-item"><a href="1.index.html">Giới thiệu</a></li>
      <li class="breadcrumb-item active">BeeUltrasonic</li>
      <li class="wy-breadcrumbs-aside">
              <a href="https://github.com/bee-stem-solutions/bee-board-docs/blob/main/docs/bee-board/4.extensions/ultrasonic.md" class="fa fa-github"> Edit on GitHub</a>
      </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <section class="tex2jax_ignore mathjax_ignore" id="beeultrasonic">
<h1>BeeUltrasonic<a class="headerlink" href="#beeultrasonic" title="Link to this heading"></a></h1>
<section id="gioi-thieu">
<h2>Giới thiệu<a class="headerlink" href="#gioi-thieu" title="Link to this heading"></a></h2>
<p>Module BeeUltrasonic cung cấp giao diện điều khiển cảm biến siêu âm HC-SR04 cho BeE Board. Cảm biến sử dụng sóng siêu âm để đo khoảng cách chính xác từ 2cm đến 4m, hoạt động dựa trên nguyên lý phản xạ sóng âm.</p>
<p>Cảm biến siêu âm là một trong những cảm biến phổ biến nhất trong robotics và IoT, cho phép robot “nhìn thấy” môi trường xung quanh và tránh vật cản. Công nghệ này tương tự như hệ thống định vị của dơi và cá heo.</p>
<p><strong>Ứng dụng thực tế:</strong></p>
<ul class="simple">
<li><p>Robot tránh vật cản</p></li>
<li><p>Hệ thống đỗ xe tự động</p></li>
<li><p>Đo mức nước trong bể</p></li>
<li><p>Cảnh báo xâm nhập</p></li>
<li><p>Đo khoảng cách trong công nghiệp</p></li>
<li><p>Hệ thống điều khiển thông minh</p></li>
</ul>
</section>
<section id="thong-so-ky-thuat">
<h2>Thông số kỹ thuật<a class="headerlink" href="#thong-so-ky-thuat" title="Link to this heading"></a></h2>
<table class="docutils align-default">
<thead>
<tr class="row-odd"><th class="head"><p>Thông số</p></th>
<th class="head"><p>Giá trị</p></th>
</tr>
</thead>
<tbody>
<tr class="row-even"><td><p>Loại cảm biến</p></td>
<td><p>HC-SR04 Ultrasonic</p></td>
</tr>
<tr class="row-odd"><td><p>Dải đo</p></td>
<td><p>20mm - 4000mm</p></td>
</tr>
<tr class="row-even"><td><p>Độ chính xác</p></td>
<td><p>±3mm</p></td>
</tr>
<tr class="row-odd"><td><p>Góc đo</p></td>
<td><p>15° (cone)</p></td>
</tr>
<tr class="row-even"><td><p>Tần số siêu âm</p></td>
<td><p>40kHz</p></td>
</tr>
<tr class="row-odd"><td><p>Điện áp hoạt động</p></td>
<td><p>5V (3.3V tương thích)</p></td>
</tr>
<tr class="row-even"><td><p>Dòng tiêu thụ</p></td>
<td><p>15mA</p></td>
</tr>
<tr class="row-odd"><td><p>Thời gian đo</p></td>
<td><p>38ms (tối đa)</p></td>
</tr>
<tr class="row-even"><td><p>Nhiệt độ hoạt động</p></td>
<td><p>-15°C đến +70°C</p></td>
</tr>
</tbody>
</table>
</section>
<section id="giao-dien-lap-trinh">
<h2>Giao diện lập trình<a class="headerlink" href="#giao-dien-lap-trinh" title="Link to this heading"></a></h2>
<section id="khoi-tao">
<h3>Khởi tạo<a class="headerlink" href="#khoi-tao" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeUltrasonic</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeUltrasonic</span>

<span class="c1"># Khởi tạo cảm biến với PORT1</span>
<span class="c1"># Pin 1: Trigger, Pin 2: Echo</span>
<span class="n">sensor</span> <span class="o">=</span> <span class="n">BeeUltrasonic</span><span class="p">(</span><span class="n">PORT1</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="cac-phuong-thuc-chinh">
<h3>Các phương thức chính<a class="headerlink" href="#cac-phuong-thuc-chinh" title="Link to this heading"></a></h3>
<section id="distance-mm-do-khoang-cach-mm">
<h4><code class="docutils literal notranslate"><span class="pre">distance_mm()</span></code> - Đo khoảng cách (mm)<a class="headerlink" href="#distance-mm-do-khoang-cach-mm" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Đo khoảng cách tính bằng milimeter</span>
<span class="n">distance</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">distance_mm</span><span class="p">()</span>
<span class="k">if</span> <span class="n">distance</span> <span class="o">!=</span> <span class="o">-</span><span class="mi">1</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Khoảng cách: </span><span class="si">{</span><span class="n">distance</span><span class="si">}</span><span class="s2">mm&quot;</span><span class="p">)</span>
<span class="k">else</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Không phát hiện vật thể&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="distance-cm-do-khoang-cach-cm">
<h4><code class="docutils literal notranslate"><span class="pre">distance_cm()</span></code> - Đo khoảng cách (cm)<a class="headerlink" href="#distance-cm-do-khoang-cach-cm" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Đo khoảng cách tính bằng centimeter</span>
<span class="n">distance</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">distance_cm</span><span class="p">()</span>
<span class="k">if</span> <span class="n">distance</span> <span class="o">!=</span> <span class="o">-</span><span class="mf">1.0</span><span class="p">:</span>
    <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Khoảng cách: </span><span class="si">{</span><span class="n">distance</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">cm&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
<section id="is-object-present-threshold-phat-hien-vat-the">
<h4><code class="docutils literal notranslate"><span class="pre">is_object_present(threshold)</span></code> - Phát hiện vật thể<a class="headerlink" href="#is-object-present-threshold-phat-hien-vat-the" title="Link to this heading"></a></h4>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="c1"># Kiểm tra có vật thể trong phạm vi 30cm</span>
<span class="k">if</span> <span class="n">sensor</span><span class="o">.</span><span class="n">is_object_present</span><span class="p">(</span><span class="mi">300</span><span class="p">):</span>  <span class="c1"># 300mm = 30cm</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Phát hiện vật thể gần!&quot;</span><span class="p">)</span>
</pre></div>
</div>
</section>
</section>
</section>
<section id="vi-du-blockly">
<h2>Ví dụ Blockly<a class="headerlink" href="#vi-du-blockly" title="Link to this heading"></a></h2>
<div class="highlight-default notranslate"><div class="highlight"><pre><span></span><span class="n">repeat</span> <span class="n">forever</span><span class="p">:</span>
    <span class="nb">set</span> <span class="n">distance</span> <span class="n">to</span> <span class="n">ultrasonic</span> <span class="n">sensor</span> <span class="n">distance</span> <span class="ow">in</span> <span class="n">cm</span>
    <span class="k">if</span> <span class="n">distance</span> <span class="o">&lt;</span> <span class="mi">20</span><span class="p">:</span>
        <span class="nb">set</span> <span class="n">LED</span> <span class="n">to</span> <span class="n">RED</span>
        <span class="n">play</span> <span class="n">sound</span> <span class="s2">&quot;beep&quot;</span>
    <span class="k">else</span> <span class="k">if</span> <span class="n">distance</span> <span class="o">&lt;</span> <span class="mi">50</span><span class="p">:</span>
        <span class="nb">set</span> <span class="n">LED</span> <span class="n">to</span> <span class="n">YELLOW</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="nb">set</span> <span class="n">LED</span> <span class="n">to</span> <span class="n">GREEN</span>
    <span class="n">wait</span> <span class="mf">0.5</span> <span class="n">seconds</span>
</pre></div>
</div>
</section>
<section id="vi-du-python">
<h2>Ví dụ Python<a class="headerlink" href="#vi-du-python" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban-do-khoang-cach-don-gian">
<h3>Ví dụ cơ bản - Đo khoảng cách đơn giản<a class="headerlink" href="#vi-du-co-ban-do-khoang-cach-don-gian" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeUltrasonic</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeUltrasonic</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeOled</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeOled</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>

<span class="c1"># Khởi tạo</span>
<span class="n">sensor</span> <span class="o">=</span> <span class="n">BeeUltrasonic</span><span class="p">(</span><span class="n">PORT1</span><span class="p">)</span>
<span class="n">oled</span> <span class="o">=</span> <span class="n">BeeOled</span><span class="p">(</span><span class="n">PORT2</span><span class="p">)</span>

<span class="k">def</span><span class="w"> </span><span class="nf">display_distance</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Hiển thị khoảng cách trên OLED&quot;&quot;&quot;</span>
    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="n">distance_cm</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">distance_cm</span><span class="p">()</span>

        <span class="n">oled</span><span class="o">.</span><span class="n">clear</span><span class="p">()</span>
        <span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Ultrasonic Sensor&quot;</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>

        <span class="k">if</span> <span class="n">distance_cm</span> <span class="o">!=</span> <span class="o">-</span><span class="mf">1.0</span><span class="p">:</span>
            <span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Distance:&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
            <span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;</span><span class="si">{</span><span class="n">distance_cm</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2"> cm&quot;</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">35</span><span class="p">)</span>

            <span class="c1"># Vẽ thanh biểu diễn khoảng cách</span>
            <span class="n">bar_width</span> <span class="o">=</span> <span class="nb">int</span><span class="p">(</span><span class="nb">min</span><span class="p">(</span><span class="n">distance_cm</span> <span class="o">*</span> <span class="mi">2</span><span class="p">,</span> <span class="mi">120</span><span class="p">))</span>
            <span class="n">oled</span><span class="o">.</span><span class="n">draw_rect</span><span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">50</span><span class="p">,</span> <span class="n">bar_width</span><span class="p">,</span> <span class="mi">8</span><span class="p">,</span> <span class="mi">1</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="n">oled</span><span class="o">.</span><span class="n">write</span><span class="p">(</span><span class="s2">&quot;Out of range&quot;</span><span class="p">,</span> <span class="mi">20</span><span class="p">,</span> <span class="mi">30</span><span class="p">)</span>

        <span class="n">oled</span><span class="o">.</span><span class="n">show</span><span class="p">()</span>
        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.2</span><span class="p">)</span>

<span class="c1"># Chạy chương trình</span>
<span class="n">display_distance</span><span class="p">()</span>
</pre></div>
</div>
</section>
<section id="vi-du-nang-cao-robot-tranh-vat-can">
<h3>Ví dụ nâng cao - Robot tránh vật cản<a class="headerlink" href="#vi-du-nang-cao-robot-tranh-vat-can" title="Link to this heading"></a></h3>
<div class="highlight-python notranslate"><div class="highlight"><pre><span></span><span class="kn">from</span><span class="w"> </span><span class="nn">BeeUltrasonic</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeUltrasonic</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeMotor</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeDCMotors</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeNeopixel</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeNeopixel</span>
<span class="kn">from</span><span class="w"> </span><span class="nn">BeeBuzzer</span><span class="w"> </span><span class="kn">import</span> <span class="n">BeeMusic</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">time</span>
<span class="kn">import</span><span class="w"> </span><span class="nn">random</span>

<span class="c1"># Khởi tạo các module</span>
<span class="n">sensor</span> <span class="o">=</span> <span class="n">BeeUltrasonic</span><span class="p">(</span><span class="n">PORT1</span><span class="p">)</span>
<span class="n">motors</span> <span class="o">=</span> <span class="n">BeeDCMotors</span><span class="p">(</span><span class="n">PORT2</span><span class="p">)</span>
<span class="n">leds</span> <span class="o">=</span> <span class="n">BeeNeopixel</span><span class="p">(</span><span class="n">pin</span><span class="o">=</span><span class="mi">5</span><span class="p">,</span> <span class="n">number_of_led</span><span class="o">=</span><span class="mi">2</span><span class="p">)</span>
<span class="n">buzzer</span> <span class="o">=</span> <span class="n">BeeMusic</span><span class="p">(</span><span class="n">pin</span><span class="o">=</span><span class="mi">15</span><span class="p">,</span> <span class="n">volume</span><span class="o">=</span><span class="mi">50</span><span class="p">)</span>

<span class="c1"># Thông số điều khiển</span>
<span class="n">SAFE_DISTANCE</span> <span class="o">=</span> <span class="mi">30</span>  <span class="c1"># cm</span>
<span class="n">WARNING_DISTANCE</span> <span class="o">=</span> <span class="mi">15</span>  <span class="c1"># cm</span>
<span class="n">DANGER_DISTANCE</span> <span class="o">=</span> <span class="mi">10</span>  <span class="c1"># cm</span>

<span class="k">class</span><span class="w"> </span><span class="nc">ObstacleAvoidanceRobot</span><span class="p">:</span>
    <span class="k">def</span><span class="w"> </span><span class="fm">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">state</span> <span class="o">=</span> <span class="s2">&quot;FORWARD&quot;</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">turn_direction</span> <span class="o">=</span> <span class="s2">&quot;RIGHT&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">set_led_color</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">color</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Đặt màu LED theo trạng thái&quot;&quot;&quot;</span>
        <span class="n">colors</span> <span class="o">=</span> <span class="p">{</span>
            <span class="s2">&quot;GREEN&quot;</span><span class="p">:</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>
            <span class="s2">&quot;YELLOW&quot;</span><span class="p">:</span> <span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>
            <span class="s2">&quot;RED&quot;</span><span class="p">:</span> <span class="p">(</span><span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">),</span>
            <span class="s2">&quot;BLUE&quot;</span><span class="p">:</span> <span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>
        <span class="p">}</span>

        <span class="k">if</span> <span class="n">color</span> <span class="ow">in</span> <span class="n">colors</span><span class="p">:</span>
            <span class="n">r</span><span class="p">,</span> <span class="n">g</span><span class="p">,</span> <span class="n">b</span> <span class="o">=</span> <span class="n">colors</span><span class="p">[</span><span class="n">color</span><span class="p">]</span>
            <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">r</span><span class="p">,</span> <span class="n">g</span><span class="p">,</span> <span class="n">b</span><span class="p">)</span>
            <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">r</span><span class="p">,</span> <span class="n">g</span><span class="p">,</span> <span class="n">b</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">move_forward</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">speed</span><span class="o">=</span><span class="mi">30</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Di chuyển tiến&quot;&quot;&quot;</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">speed</span><span class="p">)</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">speed</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">set_led_color</span><span class="p">(</span><span class="s2">&quot;GREEN&quot;</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">move_backward</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">speed</span><span class="o">=</span><span class="mi">25</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Di chuyển lùi&quot;&quot;&quot;</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="n">speed</span><span class="p">)</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="n">speed</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">set_led_color</span><span class="p">(</span><span class="s2">&quot;BLUE&quot;</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">turn_right</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">speed</span><span class="o">=</span><span class="mi">25</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Rẽ phải&quot;&quot;&quot;</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="n">speed</span><span class="p">)</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="o">-</span><span class="n">speed</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">set_led_color</span><span class="p">(</span><span class="s2">&quot;YELLOW&quot;</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">turn_left</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">speed</span><span class="o">=</span><span class="mi">25</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Rẽ trái&quot;&quot;&quot;</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="o">-</span><span class="n">speed</span><span class="p">)</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">speed</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="n">speed</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">set_led_color</span><span class="p">(</span><span class="s2">&quot;YELLOW&quot;</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">stop</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Dừng robot&quot;&quot;&quot;</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">brake</span><span class="p">(</span><span class="mi">0</span><span class="p">)</span>
        <span class="n">motors</span><span class="o">.</span><span class="n">brake</span><span class="p">(</span><span class="mi">1</span><span class="p">)</span>
        <span class="bp">self</span><span class="o">.</span><span class="n">set_led_color</span><span class="p">(</span><span class="s2">&quot;RED&quot;</span><span class="p">)</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">scan_environment</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Quét môi trường và quyết định hành động&quot;&quot;&quot;</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">distance_cm</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">distance</span> <span class="o">==</span> <span class="o">-</span><span class="mf">1.0</span><span class="p">:</span>
            <span class="c1"># Không đo được - tiếp tục di chuyển</span>
            <span class="k">return</span> <span class="n">SAFE_DISTANCE</span> <span class="o">+</span> <span class="mi">1</span>

        <span class="k">return</span> <span class="n">distance</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">avoid_obstacle</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Logic tránh vật cản chính&quot;&quot;&quot;</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">scan_environment</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">distance</span> <span class="o">&gt;</span> <span class="n">SAFE_DISTANCE</span><span class="p">:</span>
            <span class="c1"># An toàn - di chuyển tiến</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">move_forward</span><span class="p">()</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">state</span> <span class="o">=</span> <span class="s2">&quot;FORWARD&quot;</span>

        <span class="k">elif</span> <span class="n">distance</span> <span class="o">&gt;</span> <span class="n">WARNING_DISTANCE</span><span class="p">:</span>
            <span class="c1"># Cảnh báo - giảm tốc độ</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">move_forward</span><span class="p">(</span><span class="n">speed</span><span class="o">=</span><span class="mi">20</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">set_led_color</span><span class="p">(</span><span class="s2">&quot;YELLOW&quot;</span><span class="p">)</span>
            <span class="n">buzzer</span><span class="o">.</span><span class="n">play_tone</span><span class="p">(</span><span class="mi">440</span><span class="p">)</span>  <span class="c1"># Beep cảnh báo</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>
            <span class="n">buzzer</span><span class="o">.</span><span class="n">be_quiet</span><span class="p">()</span>

        <span class="k">elif</span> <span class="n">distance</span> <span class="o">&gt;</span> <span class="n">DANGER_DISTANCE</span><span class="p">:</span>
            <span class="c1"># Nguy hiểm - chuẩn bị rẽ</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">stop</span><span class="p">()</span>
            <span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;A5:0.2 SIL:0.1 A5:0.2&quot;</span><span class="p">)</span>

            <span class="c1"># Lùi một chút</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">move_backward</span><span class="p">()</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>

            <span class="c1"># Chọn hướng rẽ ngẫu nhiên</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">turn_direction</span> <span class="o">=</span> <span class="n">random</span><span class="o">.</span><span class="n">choice</span><span class="p">([</span><span class="s2">&quot;LEFT&quot;</span><span class="p">,</span> <span class="s2">&quot;RIGHT&quot;</span><span class="p">])</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">state</span> <span class="o">=</span> <span class="s2">&quot;TURNING&quot;</span>

        <span class="k">else</span><span class="p">:</span>
            <span class="c1"># Rất nguy hiểm - rẽ gấp</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">stop</span><span class="p">()</span>
            <span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;A6:0.1 SIL:0.05 A6:0.1 SIL:0.05 A6:0.1&quot;</span><span class="p">)</span>

            <span class="c1"># Lùi xa hơn</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">move_backward</span><span class="p">()</span>
            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">1.0</span><span class="p">)</span>

            <span class="c1"># Rẽ mạnh</span>
            <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">turn_direction</span> <span class="o">==</span> <span class="s2">&quot;RIGHT&quot;</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">turn_right</span><span class="p">()</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="bp">self</span><span class="o">.</span><span class="n">turn_left</span><span class="p">()</span>

            <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">1.5</span><span class="p">)</span>  <span class="c1"># Rẽ lâu hơn</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">state</span> <span class="o">=</span> <span class="s2">&quot;FORWARD&quot;</span>

    <span class="k">def</span><span class="w"> </span><span class="nf">run</span><span class="p">(</span><span class="bp">self</span><span class="p">):</span>
<span class="w">        </span><span class="sd">&quot;&quot;&quot;Chạy robot tránh vật cản&quot;&quot;&quot;</span>
        <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Robot tránh vật cản đã khởi động!&quot;</span><span class="p">)</span>
        <span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C4 E4 G4 C5&quot;</span><span class="p">,</span> <span class="mf">0.3</span><span class="p">)</span>  <span class="c1"># Âm thanh khởi động</span>

        <span class="k">try</span><span class="p">:</span>
            <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
                <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">state</span> <span class="o">==</span> <span class="s2">&quot;TURNING&quot;</span><span class="p">:</span>
                    <span class="c1"># Đang trong quá trình rẽ</span>
                    <span class="k">if</span> <span class="bp">self</span><span class="o">.</span><span class="n">turn_direction</span> <span class="o">==</span> <span class="s2">&quot;RIGHT&quot;</span><span class="p">:</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">turn_right</span><span class="p">()</span>
                    <span class="k">else</span><span class="p">:</span>
                        <span class="bp">self</span><span class="o">.</span><span class="n">turn_left</span><span class="p">()</span>

                    <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.8</span><span class="p">)</span>  <span class="c1"># Rẽ trong 0.8 giây</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">state</span> <span class="o">=</span> <span class="s2">&quot;FORWARD&quot;</span>
                <span class="k">else</span><span class="p">:</span>
                    <span class="c1"># Quét và tránh vật cản</span>
                    <span class="bp">self</span><span class="o">.</span><span class="n">avoid_obstacle</span><span class="p">()</span>

                <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.1</span><span class="p">)</span>  <span class="c1"># Delay nhỏ giữa các lần đo</span>

        <span class="k">except</span> <span class="ne">KeyboardInterrupt</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Dừng robot...&quot;</span><span class="p">)</span>
            <span class="bp">self</span><span class="o">.</span><span class="n">stop</span><span class="p">()</span>
            <span class="n">buzzer</span><span class="o">.</span><span class="n">play_song</span><span class="p">(</span><span class="s2">&quot;C5 G4 E4 C4&quot;</span><span class="p">,</span> <span class="mf">0.2</span><span class="p">)</span>  <span class="c1"># Âm thanh tắt</span>

<span class="k">def</span><span class="w"> </span><span class="nf">distance_monitor</span><span class="p">():</span>
<span class="w">    </span><span class="sd">&quot;&quot;&quot;Monitor khoảng cách liên tục&quot;&quot;&quot;</span>
    <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;=== Distance Monitor ===&quot;</span><span class="p">)</span>

    <span class="k">while</span> <span class="kc">True</span><span class="p">:</span>
        <span class="n">distance</span> <span class="o">=</span> <span class="n">sensor</span><span class="o">.</span><span class="n">distance_cm</span><span class="p">()</span>

        <span class="k">if</span> <span class="n">distance</span> <span class="o">!=</span> <span class="o">-</span><span class="mf">1.0</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="sa">f</span><span class="s2">&quot;Distance: </span><span class="si">{</span><span class="n">distance</span><span class="si">:</span><span class="s2">.1f</span><span class="si">}</span><span class="s2">cm&quot;</span><span class="p">,</span> <span class="n">end</span><span class="o">=</span><span class="s2">&quot;&quot;</span><span class="p">)</span>

            <span class="k">if</span> <span class="n">distance</span> <span class="o">&lt;</span> <span class="mi">10</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot; - DANGER!&quot;</span><span class="p">)</span>
                <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Đỏ</span>
                <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="k">elif</span> <span class="n">distance</span> <span class="o">&lt;</span> <span class="mi">30</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot; - WARNING&quot;</span><span class="p">)</span>
                <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Vàng</span>
                <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
            <span class="k">else</span><span class="p">:</span>
                <span class="nb">print</span><span class="p">(</span><span class="s2">&quot; - SAFE&quot;</span><span class="p">)</span>
                <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>  <span class="c1"># Xanh</span>
                <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
        <span class="k">else</span><span class="p">:</span>
            <span class="nb">print</span><span class="p">(</span><span class="s2">&quot;Out of range&quot;</span><span class="p">)</span>
            <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>  <span class="c1"># Xanh dương</span>
            <span class="n">leds</span><span class="o">.</span><span class="n">set_rgb</span><span class="p">(</span><span class="mi">1</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">255</span><span class="p">)</span>

        <span class="n">time</span><span class="o">.</span><span class="n">sleep</span><span class="p">(</span><span class="mf">0.5</span><span class="p">)</span>

<span class="c1"># Chạy chương trình</span>
<span class="k">if</span> <span class="vm">__name__</span> <span class="o">==</span> <span class="s2">&quot;__main__&quot;</span><span class="p">:</span>
    <span class="c1"># Chọn chế độ chạy</span>
    <span class="n">mode</span> <span class="o">=</span> <span class="nb">input</span><span class="p">(</span><span class="s2">&quot;Chọn chế độ (1: Monitor, 2: Robot): &quot;</span><span class="p">)</span>

    <span class="k">if</span> <span class="n">mode</span> <span class="o">==</span> <span class="s2">&quot;1&quot;</span><span class="p">:</span>
        <span class="n">distance_monitor</span><span class="p">()</span>
    <span class="k">else</span><span class="p">:</span>
        <span class="n">robot</span> <span class="o">=</span> <span class="n">ObstacleAvoidanceRobot</span><span class="p">()</span>
        <span class="n">robot</span><span class="o">.</span><span class="n">run</span><span class="p">()</span>
</pre></div>
</div>
</section>
</section>
<section id="giai-thich-ma">
<h2>Giải thích mã<a class="headerlink" href="#giai-thich-ma" title="Link to this heading"></a></h2>
<section id="vi-du-co-ban">
<h3>Ví dụ cơ bản:<a class="headerlink" href="#vi-du-co-ban" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Đo khoảng cách</strong>: Sử dụng <code class="docutils literal notranslate"><span class="pre">distance_cm()</span></code> để lấy giá trị</p></li>
<li><p><strong>Kiểm tra hợp lệ</strong>: Giá trị -1 nghĩa là ngoài phạm vi đo</p></li>
<li><p><strong>Hiển thị</strong>: Cập nhật OLED với thông tin khoảng cách</p></li>
<li><p><strong>Visualization</strong>: Vẽ thanh biểu diễn trực quan</p></li>
</ol>
</section>
<section id="vi-du-nang-cao">
<h3>Ví dụ nâng cao:<a class="headerlink" href="#vi-du-nang-cao" title="Link to this heading"></a></h3>
<ol class="arabic simple">
<li><p><strong>Multi-sensor system</strong>: Kết hợp ultrasonic với motor, LED, buzzer</p></li>
<li><p><strong>State machine</strong>: Robot có các trạng thái khác nhau (tiến, rẽ, lùi)</p></li>
<li><p><strong>Decision making</strong>: Logic quyết định dựa trên khoảng cách</p></li>
<li><p><strong>Safety levels</strong>: Phân cấp mức độ nguy hiểm và phản ứng tương ứng</p></li>
</ol>
</section>
</section>
<section id="bai-tap-mo-rong">
<h2>Bài tập mở rộng<a class="headerlink" href="#bai-tap-mo-rong" title="Link to this heading"></a></h2>
<ol class="arabic simple">
<li><p><strong>Hệ thống đỗ xe</strong>: Tạo cảm biến hỗ trợ đỗ xe với âm thanh và LED cảnh báo</p></li>
<li><p><strong>Đo mức nước</strong>: Sử dụng cảm biến để đo và cảnh báo mức nước trong bể</p></li>
<li><p><strong>Robot mapping</strong>: Tạo robot có thể vẽ bản đồ môi trường xung quanh</p></li>
</ol>
</section>
<section id="loi-thuong-gap">
<h2>Lỗi thường gặp<a class="headerlink" href="#loi-thuong-gap" title="Link to this heading"></a></h2>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Đo khoảng cách không chính xác</p>
<p><strong>Nguyên nhân</strong>: Nhiệt độ, độ ẩm hoặc vật liệu phản xạ ảnh hưởng</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Hiệu chỉnh theo nhiệt độ môi trường</p></li>
<li><p>Tránh đo các bề mặt mềm, xốp (thảm, vải)</p></li>
<li><p>Đảm bảo góc đo vuông góc với bề mặt</p></li>
<li><p>Lọc nhiễu bằng cách lấy trung bình nhiều lần đo</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Cảm biến trả về -1 liên tục</p>
<p><strong>Nguyên nhân</strong>: Kết nối sai hoặc nguồn không ổn định</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Kiểm tra kết nối Trigger và Echo pin</p></li>
<li><p>Đảm bảo nguồn 5V ổn định</p></li>
<li><p>Kiểm tra mass chung</p></li>
<li><p>Thử với timeout lớn hơn</p></li>
</ul>
</div>
<div class="warning admonition">
<p class="admonition-title">Lỗi: Đo khoảng cách gần bị sai</p>
<p><strong>Nguyên nhân</strong>: Blind zone của cảm biến hoặc nhiễu</p>
<p><strong>Giải pháp</strong>:</p>
<ul class="simple">
<li><p>Không đo khoảng cách &lt; 2cm</p></li>
<li><p>Tránh vật cản quá gần cảm biến</p></li>
<li><p>Sử dụng delay giữa các lần đo</p></li>
<li><p>Kiểm tra nhiễu từ các thiết bị khác</p></li>
</ul>
</div>
</section>
<section id="tai-nguyen-tham-khao">
<h2>Tài nguyên tham khảo<a class="headerlink" href="#tai-nguyen-tham-khao" title="Link to this heading"></a></h2>
<ul class="simple">
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/docs/bee-board">BeE Board API Documentation</a></p></li>
<li><p><a class="reference external" href="https://cdn.sparkfun.com/datasheets/Sensors/Proximity/HCSR04.pdf">HC-SR04 Datasheet</a></p></li>
<li><p><a class="reference external" href="https://learn.adafruit.com/ultrasonic-sonar-distance-sensors">Ultrasonic Sensor Theory</a></p></li>
<li><p><a class="reference external" href="https://beestemsolutions.com.vn/studio/bee-ide">BeE Block IDE Online</a></p></li>
</ul>
</section>
</section>


           </div>
          </div>
          <footer><div class="rst-footer-buttons" role="navigation" aria-label="Footer">
        <a href="rc522.html" class="btn btn-neutral float-left" title="BeeRC522" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
        <a href="../5.about/index.html" class="btn btn-neutral float-right" title="BeE STEM Solutions" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>&#169; Copyright 2025, BeE STEM Solutions.</p>
  </div>

   

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html>