# Giới thiệu

<p align="center">
  <img 
    src="../_static/bee-board-v2/board.png" 
    alt="BeE Board V2 tổng quan" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

## BeE Board V2 là gì?

**BeE Board V2** là bo mạch học lập trình dành cho học sinh tiểu học và THCS, đư<PERSON><PERSON> thiết kế bởi **BeE STEM Solutions**.
Bo mạch giúp học sinh học lập trình **kéo thả (Blockly)** hoặc **Python (MicroPython)** thông qua **BeE IDE** và **BeE Python**.

Bo tương thích với LEGO Technic, hỗ trợ học sinh tạo robot, mô hình IoT và các dự án sáng tạo thực tế.

---

## Thông số kỹ thuật

| Thành phần         | M<PERSON> tả                                |
| ------------------ | ------------------------------------ |
| Vi điều khiển      | ESP32-S3                             |
| LED tích hợp       | 2 LED RGB điều khiển độc lập         |
| Nút nhấn           | 2 nút nhấn A và B                    |
| Màn hình OLED      | 0.96 inch (128x64 pixel)             |
| Cảm biến           | MPU6050 (con quay + gia tốc kế)      |
| Loa Buzzer         | Âm thanh đơn hoặc giai điệu          |
| Động cơ DC         | 2 cổng điều khiển                    |
| Servo RC           | 4 cổng (S1–S4)                       |
| Cổng Grove mở rộng | 6 cổng (PORT1–PORT6)                 |
| Giao tiếp          | USB-C, OTA, Bluetooth, Wifi, MQTT    |
| Nguồn cấp          | Pin Li-ion 7.4V ~ 8.4V hoặc USB-C 5V |

---

## Thành phần chính trên board

| Vị trí | Mô tả                       | Ảnh minh họa                                                                                                                  |
| ------ | --------------------------- | ----------------------------------------------------------------------------------------------------------------------------- |
| 1      | LED RGB (led1, led2)        | <p align="center"><img  src="../_static/bee-board-v2/led.jpg" alt="LED"  width="150" style="border-radius: 10px;"/></p>       |
| 2      | Nút nhấn (buttonA, buttonB) | <p align="center"><img  src="../_static/bee-board-v2/button.jpg" alt="Button"  width="150" style="border-radius: 10px;"/></p> |
| 3      | Màn hình OLED 0.96"         | <p align="center"><img  src="../_static/bee-board-v2/oled.jpg" alt="OLED"  width="150" style="border-radius: 10px;"/></p>     |
| 4      | Loa buzzer                  | <p align="center"><img  src="../_static/bee-board-v2/buzzer.jpg" alt="Buzzer"  width="150" style="border-radius: 10px;"/></p> |
| 5      | Cảm biến MPU6050            | <p align="center"><img  src="../_static/bee-board-v2/imu.jpg" alt="IMU"  width="150" style="border-radius: 10px;"/></p>       |
| 6      | Cổng Grove mở rộng          | <p align="center"><img  src="../_static/bee-board-v2/grove.jpg" alt="Grove"  width="150" style="border-radius: 10px;"/></p>   |
| 7      | Cổng motor (M1, M2)         | <p align="center"><img  src="../_static/bee-board-v2/motor.jpg" alt="Motor"  width="150" style="border-radius: 10px;"/></p>   |
| 8      | Cổng Servo (S1–S4)          | <p align="center"><img  src="../_static/bee-board-v2/servo.jpg" alt="Servo"  width="150" style="border-radius: 10px;"/></p>   |

---

## Đối tượng `bee` trong Python

Khi lập trình bằng Python trong BeE Python, học sinh sử dụng thư viện:

```python
from BeeBrain import bee
```

Một số lệnh cơ bản:

```python
bee.led1.set_rgb(255, 0, 0)     # Bật LED1 màu đỏ
bee.buttonA.is_pressed()        # Kiểm tra nút A
bee.oled.write("Hello", 0, 0, 1)
bee.motor1.speed(60)
bee.servo3.position(90)
bee.imu.update()
bee.buzzer.play_tone(bee.buzzer.TONES['C5'])
```

---

## Kết nối và lập trình

### Kết nối qua USB-C

1. Gắn BeE Board V2 với máy tính bằng cáp USB-C.
2. Mở **BeE IDE** → chọn cổng serial (ví dụ `/dev/cu.usbmodem` hoặc `COM3`).
3. Chọn ngôn ngữ lập trình: **Blockly** hoặc **Python**.
4. Nạp chương trình và chạy thử.

<p align="center">
  <img 
    src="../_static/bee-board-v2/connect-usb.jpg" 
    alt="Kết nối USB-C" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

### Cập nhật code qua OTA

1. Tạo chương trình `Setup OTA` trong Blockly hoặc Python.
2. Nhập tên Wi-Fi và mật khẩu.
3. BeE Board sẽ hiển thị IP trên màn hình OLED.
4. Từ **BeE IDE**, chọn “Upload OTA” và nhập IP hiển thị.

---

## Một số ví dụ đơn giản

### Ví dụ 1: Bật LED 1 màu xanh lá

```python
from BeeBrain import bee

bee.led1.set_rgb(0, 255, 0)
```

### Ví dụ 2: Hiển thị dòng chữ

```python
from BeeBrain import bee

bee.oled.clear()
bee.oled.write("Xin chao!", 0, 0, 1)
```

### Ví dụ 3: Robot di chuyển

```python
from BeeBrain import bee

bee.move_forward(50, 2)
bee.turn_left(60, 1)
bee.stop_robot()
```

---

## Các nhóm khối Blockly hỗ trợ

| Nhóm          | Mô tả                                           |
| ------------- | ----------------------------------------------- |
| LED           | Điều khiển LED1, LED2 tích hợp                  |
| Display       | Hiển thị chữ, hình, icon trên màn hình tích hợp |
| Button        | Tương tác nút nhấn A và B tích hợp              |
| Buzzer        | Phát âm thanh, nhạc tích hợp                    |
| Write Signal  | Ghi tín hiệu số, analog từ PORT1-6              |
| Read Signal   | Đọc tín hiệu số, analog từ PORT1-6              |
| Gyroscope     | Cảm biến góc nghiêng, rung lắc tích hợp         |
| Motor & Servo | Điều khiển motor và servo tích hợp              |
| Movement      | Điều khiển robot di chuyển                      |
| Advanced      | Wifi, Bluetooth, MQTT, OTA, Reset               |

---

## Xử lý sự cố thường gặp

| Vấn đề              | Nguyên nhân                          | Cách khắc phục                                     |
| ------------------- | ------------------------------------ | -------------------------------------------------- |
| Không nạp được code | Sai cổng serial hoặc chưa nhấn reset | Kiểm tra lại kết nối USB, chọn đúng cổng trong IDE |
| Không thấy IP OTA   | Wi-Fi yếu hoặc sai mật khẩu          | Kiểm tra mạng Wi-Fi, khởi động lại board           |
| OLED không hiển thị | Chưa khởi tạo hoặc code lỗi          | Dùng `bee.oled.clear()` trước khi ghi chữ          |
| LED không sáng      | Dây LED hỏng hoặc code sai           | Kiểm tra lệnh `bee.led1.set_rgb()`                 |

---

## Tài nguyên liên quan

-   [Hướng dẫn BeE IDE](../2.bee-ide/index.md)
-   [Ví dụ lập trình với BeE Board V2](../3.examples/2.led-example.md)
-   [Cài đặt thư viện mở rộng trên BeE IDE](../4.extensions/1.index.md)
-   [Giới thiệu BeE STEM Solutions](../5.about/about.md)

---

```{toctree}
:maxdepth: 1
:caption: Bắt đầu
2.getting-started
3.hardware-overview
4.programming-guide
5.troubleshooting
```
