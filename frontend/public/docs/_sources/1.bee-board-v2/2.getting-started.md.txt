# Bắt đầu

<p align="center">
  <img 
    src="../_static/logo.png" 
    alt="BeE-Cover" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

Chào mừng bạn đến với **BeE Board V2** – bo mạch học lập trình và robot sáng tạo do **BeE STEM Solutions** phát triển độc quyền 🇻🇳
BeE Board giúp học sinh học lập trình trực tuyến qua hai nền tảng web:

-   **BeE IDE** → lập trình **kéo thả Blockly**
-   **BeE Python** → lập trình **bằng code Python**

Không cần cài đặt phần mềm – chỉ cần trình duyệt web!

---

## 1. Chuẩn bị trước khi bắt đầu

### Bạn cần có:

-   1 bo mạch **BeE Board V2**
-   Cáp **USB-C** để kết nối với máy tính
-   **Trình duyệt web hiện đại** (Chrome, Edge)
-   **Driver CH340C** nếu là lần đầu kết nối BeE Board qua USB

> 💡 BeE Board V2 dùng chip **CH340C** để giao tiếp USB.
> Nếu máy tính chưa nhận cổng COM, hãy cài **driver CH340C** theo hướng dẫn:
> 👉 [https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board](https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board)

---

## 2. Truy cập nền tảng lập trình

### BeE IDE – Lập trình kéo thả (Blockly)

Truy cập trực tiếp bằng trình duyệt:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://ide.beestemsolutions.com.vn/studio/bee-ide)

<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

**Đặc điểm:**

-   Giao diện lập trình trực quan bằng khối kéo thả.
-   Dành cho học sinh tiểu học hoặc người mới bắt đầu.
-   Không hiển thị code Python — mọi thao tác đều bằng khối lệnh.
-   Hỗ trợ nạp code qua USB hoặc OTA.

---

### BeE Python – Lập trình bằng Python

Truy cập trực tiếp bằng trình duyệt:
👉 [https://beestemsolutions.com.vn/studio/python](https://python.beestemsolutions.com.vn/studio/python)

<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-python.png" 
    alt="BeE Python" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

**Đặc điểm:**

-   Viết code Python trên trình soạn thảo Monaco (giống VS Code).
-   Dành cho học sinh THCS hoặc đã quen tư duy lập trình.
-   Có gợi ý cú pháp, chạy trực tiếp trên board qua USB hoặc OTA.

---

## 3. Kết nối BeE Board V2

1. Kết nối BeE Board với máy tính bằng **cáp USB-C**
2. Mở BeE IDE hoặc BeE Python trong trình duyệt
3. Chọn cổng **Serial / USB** (ví dụ: `COM3` hoặc `/dev/cu.usbmodem`)
4. Nhấn **Kết nối** để bắt đầu lập trình

<p align="center">
  <img 
    src="../_static/bee-board-v2/connect-usb.jpg" 
    alt="Kết nối USB-C" 
    width="200" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

## 4. Viết chương trình đầu tiên

### Trong BeE IDE

1. Kéo khối **“bật LED1** vào vùng làm việc bên dưới khối **Khi chương trình bắt đầu**
2. Nhấn nút **▶️ Upload** để tải chương trình
3. Quan sát LED1 sáng lên trên BeE Board!

<p align="center">
  <img 
    src="../_static/bee-board-v2/blockly-example-led.png" 
    alt="Ví dụ BeE IDE" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

### Trong BeE Python

```python
from BeeBrain import bee

bee.led1.set_rgb(255, 0, 0)
bee.oled.write("Hello BeE!", 0, 0, 1)
```

Nhấn **Run ▶️** để chạy chương trình trực tiếp trên BeE Board.

<p align="center">
  <img 
    src="../_static/bee-board-v2/python-run.png" 
    alt="Ví dụ BeE Python" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

## 5. So sánh nhanh giữa BeE IDE và BeE Python

| Nền tảng       | Kiểu lập trình  | Đối tượng                            | Đặc điểm                                              |
| -------------- | --------------- | ------------------------------------ | ----------------------------------------------------- |
| **BeE IDE**    | Kéo thả Blockly | Học sinh tiểu học, người mới bắt đầu | Trực quan, sinh động, không hiển thị code             |
| **BeE Python** | Python thực tế  | Học sinh THCS, nâng cao              | Viết code thật, có gợi ý cú pháp và kết nối trực tiếp |

> ⚠️ Hai nền tảng hoạt động **độc lập**, không thể mở file chéo.
> Người học có thể bắt đầu bằng BeE IDE, sau đó chuyển dần sang BeE Python khi đã quen logic lập trình.

---

## 6. Cập nhật chương trình qua OTA (Wi-Fi)

BeE Board V2 hỗ trợ **Over-The-Air (OTA)** – nạp code không cần dây USB bằng BeE IDE:

1. Trong Blockly, kéo khối **“Setup OTA”**, nhập tên Wi-Fi và mật khẩu.

<p align="center">
  <img 
    src="../_static/bee-board-v2/setup-ota.png" 
    alt="Thiết lập OTA" 
    width="250" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

2. Khi board kết nối thành công, OLED hiển thị **địa chỉ IP**.
3. Trong BeE IDE, chọn **Upload OTA** → nhập IP đó.
4. Code sẽ được tải và chạy không dây.

<p align="center">
  <img 
    src="../_static/bee-board-v2/upload-ota.png" 
    alt="Nạp code OTA" 
    width="250" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

## 7. Mẹo cho người mới bắt đầu

-   Nhấn **Reset** trên board để khởi tạo lại
-   Kiểm tra LED bằng lệnh `bee.led1.set_rgb(255, 255, 255)`
-   Khi OLED không hiển thị, thử `bee.oled.show()`
-   Luôn chắc chắn chọn đúng cổng Serial trước khi nạp code
-   Hãy sáng tạo: lập trình đèn nhấp nháy, robot di chuyển, hay nhạc vui 🎵

---

## Tiếp theo

-   [Hướng dẫn BeE IDE](../2.bee-ide/index.md)
-   [Ví dụ lập trình với BeE Board V2](../3.examples/2.led-example.md)
-   [Cài đặt thư viện mở rộng trên BeE IDE](../4.extensions/1.index.md)
-   [Giới thiệu BeE STEM Solutions](../5.about/about.md)
