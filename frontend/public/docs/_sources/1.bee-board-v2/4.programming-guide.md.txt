# Hướng dẫn lập trình

---

## 1. <PERSON><PERSON><PERSON><PERSON> thiệu

BeE Board V2 có thể lập trình theo **hai cách** tùy vào cấp độ của học sinh:

| Nền tảng       | Ki<PERSON>u lập trình   | Đ<PERSON><PERSON> tượng                        | Đặc điểm                                  |
| -------------- | ---------------- | -------------------------------- | ----------------------------------------- |
| **BeE IDE**    | K<PERSON><PERSON> thả <PERSON>ly  | Học sinh tiểu học, người mới học | Trực quan, sinh động, không hiển thị code |
| **BeE Python** | Viết code Python | Học sinh THCS hoặc nâng cao      | Gần với lập trình thật, c<PERSON> gợi ý cú pháp  |

> ⚠️ Hai nền tảng này hoạt động **độc lập**, không thể mở file lẫn nhau.
> Học sinh có thể bắt đầu với **BeE IDE**, sau đó chuyển dần sang **BeE Python** khi đã quen tư duy lập trình.

---

## 2. Lập trình kéo thả với BeE IDE

### Truy cập

👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide)

BeE IDE là nền tảng lập trình trực quan cho trẻ em.
Mỗi chương trình được xây dựng từ các **khối lệnh (Blocks)** kéo thả vào vùng làm việc.

<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE UI" 
    width="500" 
    style="border-radius: 10px; max-width: 90%;"
  />
</p>

---

### Giao diện chính

| Khu vực          | Chức năng                                        | Mô tả                           |
| ---------------- | ------------------------------------------------ | ------------------------------- |
| ① Thanh công cụ  | Tạo mới, mở, lưu, nạp code                       | Điều khiển chung                |
| ② Danh mục khối  | Chứa các nhóm lệnh (LED, Button, Motor, OLED...) | Kéo thả lệnh từ đây             |
| ③ Vùng làm việc  | Nơi ghép nối các khối lệnh                       | Xây dựng chương trình           |
| ④ Khu điều khiển | Nút **Upload**                                   | Nạp và chạy code trên BeE Board |
| ⑤ Log            | Hiển thị thông báo                               | Kết quả và thông báo lỗi        |
| ⑤ BeE Assistant  | Trợ lý AI giúp lập trình                         | Hỗ trợ giải thích các khối lệnh |

---

### Nhóm khối lệnh cơ bản

| Nhóm              | Mô tả                              | Ví dụ khối                       |
| ----------------- | ---------------------------------- | -------------------------------- |
| **LED**           | Bật/tắt hoặc đổi màu LED RGB       | “Bật LED1 màu đỏ”                |
| **Button**        | Kiểm tra nút nhấn A hoặc B         | “Nếu nút A được nhấn”            |
| **Display**       | Hiển thị chữ, hình, biểu tượng     | “Hiển thị ‘Hello’ tại (0,0)”     |
| **Write Signal**  | Ghi tín hiệu số, analog từ PORT1-6 | “Ghi 50% PWM vào PORT1”          |
| **Read Signal**   | Đọc tín hiệu số, analog từ PORT1-6 | “Đọc tín hiệu nút nhấn từ PORT1” |
| **Buzzer**        | Phát âm thanh hoặc giai điệu       | “Phát nốt C5 trong 1/4 giây”     |
| **Gyroscope**     | Phát hiện nghiêng hoặc rung        | “Nếu board bị lắc thì bật đèn”   |
| **Motor & Servo** | Điều khiển động cơ hoặc servo      | “Động cơ M1 quay với tốc độ 50%” |
| **Movement**      | Điều khiển robot di chuyển         | “Robot tiến 2 giây”              |
| **Advanced**      | OTA, Reset, Kết nối                | “Cấu hình OTA Wi-Fi”             |

<p align="center">
  <img 
    src="../_static/bee-board-v2/blockly-example-led.png" 
    alt="Blockly LED Example" 
    width="500" 
    style="border-radius: 10px; max-width: 90%;"
  />
</p>

---

### Nạp chương trình từ BeE IDE

BeE IDE có hai cách nạp chương trình:

| Phương thức      | Mô tả                   | Khi sử dụng                     |
| ---------------- | ----------------------- | ------------------------------- |
| **USB (Serial)** | Kết nối qua cáp USB-C   | Khi học trực tiếp trong lớp     |
| **OTA (Wi-Fi)**  | Cập nhật code không dây | Khi board đã kết nối mạng Wi-Fi |

#### Nạp qua USB

1. Cắm BeE Board vào máy tính bằng **cáp USB-C**
2. Nhấn **Kết nối → Chọn cổng Serial (COMx / usbmodem)**
3. Nhấn **Upload ▶️** để tải code xuống board

#### Nạp qua OTA

1. Kéo khối “Setup OTA” và nhập Wi-Fi + mật khẩu
2. Sau khi board hiển thị **IP** trên màn hình OLED
3. Chọn **Upload OTA** → nhập IP đó để tải code không dây

---

## 3. Lập trình Python với BeE Python

### Truy cập

👉 [https://beestemsolutions.com.vn/studio/python](https://beestemsolutions.com.vn/studio/python)

BeE Python là nền tảng web cho học sinh lập trình trực tiếp bằng **ngôn ngữ Python (MicroPython)**.

<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-python.png" 
    alt="BeE Python" 
    width="500" 
    style="border-radius: 10px; max-width: 90%;"
  />
</p>

---

### Giao diện chính

| Khu vực                | Chức năng                         | Mô tả                             |
| ---------------------- | --------------------------------- | --------------------------------- |
| ① Thanh công cụ        | Kết nối, tải code, reset board    | Giao tiếp trực tiếp với BeE Board |
| ② Quản lý dự án        | Tạo mới, mở, lưu                  | Quản lý dự án                     |
| ③ Trình soạn thảo code | Monaco Editor (giống VS Code)     | Viết code Python                  |
| ④ Console              | Hiển thị kết quả & thông báo      | Có thể nhập lệnh trực tiếp        |
| ⑤ Thanh trạng thái     | Hiển thị cổng kết nối, trạng thái | Theo dõi trạng thái board         |
| ⑥ BeE Assistant        | Trợ lý AI giúp lập trình          | Hỗ trợ giải thích các khối lệnh   |

---

### Ví dụ chương trình cơ bản

#### Bật LED 1 màu đỏ

```python
from BeeBrain import bee

bee.led1.set_rgb(255, 0, 0)
```

#### Hiển thị chữ trên OLED

```python
from BeeBrain import bee

bee.oled.write("Hello BeE!", 0, 0, 1)
```

#### Robot tiến và quay

```python
from BeeBrain import bee

bee.move_forward(50, 2)
bee.turn_left(60, 1)
bee.stop_robot()
```

---

### Một số hàm cơ bản trong Python

| Nhóm   | Hàm / Lệnh                         | Mô tả                 |
| ------ | ---------------------------------- | --------------------- |
| LED    | `bee.led1.set_rgb(r,g,b)`          | Bật LED RGB           |
| OLED   | `bee.oled.write(text, x, y, size)` | Ghi chữ lên màn hình  |
| Button | `bee.buttonA.is_pressed()`         | Kiểm tra nút nhấn     |
| IMU    | `bee.imu.is_shaking()`             | Phát hiện lắc         |
| Motor  | `bee.motor1.speed(power)`          | Chạy động cơ          |
| Servo  | `bee.servo1.position(angle)`       | Quay servo            |
| Buzzer | `bee.buzzer.play_tone(note)`       | Phát âm thanh         |
| Reset  | `bee.init_bee()`                   | Khởi tạo lại hệ thống |

---

## 4. Cập nhật chương trình OTA từ BeE Python

1. Chạy chương trình “Setup OTA” từ BeE IDE hoặc Python
2. OLED hiển thị **địa chỉ IP**
3. Trong BeE Python, chọn **Upload OTA → nhập IP**
4. Nhấn **Run ▶️** để tải và chạy code qua Wi-Fi

<p align="center">
  <img 
    src="../_static/bee-board-v2/upload-ota.png" 
    alt="BeE OTA Example" 
    width="200" 
    style="border-radius: 10px; max-width: 90%;"
  />
</p>

---

## 5. Gợi ý dự án thực hành

| Dự án                  | Mô tả                               | Kiến thức áp dụng      |
| ---------------------- | ----------------------------------- | ---------------------- |
| Đèn giao thông mini    | Dùng LED RGB đổi màu theo thời gian | Điều khiển LED + Timer |
| Chuông cửa thông minh  | Phát nhạc khi nhấn nút              | Button + Buzzer        |
| Robot tránh vật cản    | Xe tự động quay khi phát hiện rung  | IMU + Motor            |
| Nhiệt kế hiển thị OLED | Đọc cảm biến DHT11 qua cổng Grove   | PORT + OLED            |

---

## 6. Tài liệu liên quan

-   [Hướng dẫn BeE IDE](../2.bee-ide/index.md)
-   [Ví dụ lập trình với BeE Board V2](../3.examples/2.led-example.md)
-   [Cài đặt thư viện mở rộng trên BeE IDE](../4.extensions/1.index.md)
-   [Giới thiệu BeE STEM Solutions](../5.about/about.md)
