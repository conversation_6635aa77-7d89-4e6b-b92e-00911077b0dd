# BeE IDE

<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

## BeE IDE là gì?

**BeE IDE** là nền tảng **lập trình kéo thả trực quan (Blockly)** được phát triển bởi **BeE STEM Solutions** 🇻🇳.
Nó giúp học sinh, đặc biệt là cấp Tiểu học và THCS, **học lập trình thông qua thao tác kéo thả khối lệnh**, không cần gõ code.

> 🌐 Truy cập trực tiếp:
> 👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide)

BeE IDE hoạt động hoàn toàn **trên trình duyệt web**,
không cần cài đặt phần mềm — chỉ cần cắm **BeE Board V2** là có thể lập trình ngay!

---

## Các tính năng nổi bật

| Tính năng                              | Mô tả                                                     |
| -------------------------------------- | --------------------------------------------------------- |
| **Lập trình kéo thả Blockly**          | Dễ học, sinh động, phù hợp trẻ em                         |
| **Kết nối trực tiếp với BeE Board V2** | Qua USB, Bluetooth hoặc OTA Wi-Fi                         |
| **Nhóm khối phong phú**                | LED, Buzzer, Button, OLED, Motor, Servo, IMU...           |
| **Xuất và lưu dự án**                  | Lưu trên trình duyệt hoặc tải file `.json`                |
| **Giao diện web**                      | Chạy trên mọi thiết bị: Windows, macOS, Linux, Chromebook |
| **Tải chương trình OTA**               | Nạp code không dây qua mạng Wi-Fi                         |
| **Cài đặt thư viện mở rộng**           | Tích hợp các thư viện các module mở rộng bên ngoài        |

---

## Giao diện BeE IDE

<p align="center">
  <img 
    src="../_static/bee-ide/bee-ide.png" 
    alt="BeE IDE UI" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

| Khu vực          | Chức năng                                        | Mô tả                           |
| ---------------- | ------------------------------------------------ | ------------------------------- |
| ① Thanh công cụ  | Tạo mới, mở, lưu, nạp code                       | Điều khiển chung                |
| ② Danh mục khối  | Chứa các nhóm lệnh (LED, Button, Motor, OLED...) | Kéo thả lệnh từ đây             |
| ③ Vùng làm việc  | Nơi ghép nối các khối lệnh                       | Xây dựng chương trình           |
| ④ Khu điều khiển | Nút **Upload**                                   | Nạp và chạy code trên BeE Board |
| ⑤ Log            | Hiển thị thông báo                               | Kết quả và thông báo lỗi        |
| ⑥ BeE Assistant  | Trợ lý AI giúp lập trình                         | Hỗ trợ giải thích các khối lệnh |

---

## Nhóm khối lệnh chính

| Nhóm              | Mô tả                              | Ví dụ                            |
| ----------------- | ---------------------------------- | -------------------------------- |
| **LED**           | Bật/tắt hoặc đổi màu LED RGB       | “Bật LED1 màu đỏ”                |
| **Button**        | Kiểm tra nút nhấn A hoặc B         | “Nếu nút A được nhấn”            |
| **Display**       | Hiển thị chữ, hình, biểu tượng     | “Hiển thị ‘Hello’ tại (0,0)”     |
| **Write Signal**  | Ghi tín hiệu số, analog từ PORT1-6 | “Ghi 50% PWM vào PORT1”          |
| **Read Signal**   | Đọc tín hiệu số, analog từ PORT1-6 | “Đọc tín hiệu nút nhấn từ PORT1” |
| **Buzzer**        | Phát âm thanh hoặc giai điệu       | “Phát nốt C5 trong 1/4 giây”     |
| **Gyroscope**     | Phát hiện nghiêng hoặc rung        | “Nếu board bị lắc thì bật đèn”   |
| **Motor & Servo** | Điều khiển động cơ hoặc servo      | “Động cơ M1 quay với tốc độ 50%” |
| **Movement**      | Điều khiển robot di chuyển         | “Robot tiến 2 giây”              |
| **Advanced**      | OTA, Reset, Kết nối                | “Cấu hình OTA Wi-Fi”             |

---

## Cách kết nối BeE Board V2

### Qua USB

1. Cắm cáp USB-C vào BeE Board V2
2. Chọn **Kết nối → Chọn cổng Serial (COMx / /dev/cu.usbmodem)**
3. Nhấn **Run ▶️** để nạp chương trình

<p align="center">
  <img 
    src="../_static/bee-board-v2/connect-usb.jpg" 
    alt="USB Connect" 
    width="250" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

### Qua Wi-Fi (OTA)

1. Kéo khối “Setup OTA” và nhập Wi-Fi + mật khẩu

<p align="center">
  <img 
    src="../_static/bee-board-v2/setup-ota.png" 
    alt="Setup OTA" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

2. Sau khi OLED hiển thị **địa chỉ IP**, chọn **Upload OTA**
3. Nhập IP đó để tải chương trình không dây

<p align="center">
  <img 
    src="../_static/bee-board-v2/upload-ota.png" 
    alt="Upload OTA" 
    width="250" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

## Quản lý dự án

| Hành động     | Mô tả                                        |
| ------------- | -------------------------------------------- |
| **Lưu dự án** | Nhấn “Save Project” để lưu trong trình duyệt |
| **Mở dự án**  | Nhấn “Open Project” để chọn file `.json`     |
| **Xuất file** | Tải file `.json` để lưu trữ hoặc chia sẻ     |

> Các file `.json` có thể mở lại trực tiếp trên BeE IDE ở bất kỳ máy tính nào.

---

## Thực hành với BeE IDE

```{toctree}
:maxdepth: 1
:caption: Ví dụ Blockly
../3.examples/2.led-example
../3.examples/3.button-example
../3.examples/4.buzzer-example
../3.examples/5.oled-example
../3.examples/6.motor-example
../3.examples/7.servo-example
../3.examples/8.imu-example
```

---

## Mẹo học hiệu quả

-   Bắt đầu với LED và Button để hiểu khối lệnh cơ bản
-   Thử tạo **âm thanh + đèn LED** để làm mini piano 🎹
-   Kết hợp **IMU + Motor** để làm robot tự quay khi rung
-   Lưu dự án mỗi buổi học để theo dõi tiến độ

---

## Tài liệu liên quan

-   [Bắt đầu với BeE Board V2](../1.bee-board-v2/2.getting-started.md)
-   [Cài đặt mở rộng thư viện trên BeE IDE](../4.extensions/1.index.md)
-   [Giới thiệu phần cứng BeE Board V2](../1.bee-board-v2/3.hardware-overview.md)
