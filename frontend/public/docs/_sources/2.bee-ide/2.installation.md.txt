# Cài đặt

---

## Mục tiêu

Hướng dẫn bạn **chuẩn bị máy tính và kết nối BeE Board V2** với nền tảng lập trình trực tuyến **BeE IDE** và **BeE Python**.
Sau khi hoàn tất, bạn có thể lập trình và nạp code cho BeE Board trực tiếp qua trình duyệt.

---

## 1. Yêu cầu hệ thống

| Thành phần           | Yêu cầu                                      |
| -------------------- | -------------------------------------------- |
| **Hệ điều hành**     | Windows 10/11, macOS, Linux, Chromebook      |
| **Trình duyệt**      | Chrome / Edge (phiên bản mới nhất)           |
| **<PERSON><PERSON><PERSON> nối Internet** | B<PERSON>t buộc (để truy cập Be<PERSON> IDE và BeE Python) |
| **Cáp USB-C**        | Hỗ trợ truyền dữ liệu (4 dây), không chỉ sạc |

> 💡 Bạn **không cần cài đặt phần mềm**, vì **BeE IDE** và **BeE Python** hoạt động trực tiếp trên web.

---

## 2. Cài đặt driver CH340C

BeE Board V2 sử dụng **chip CH340C** để giao tiếp USB với máy tính.
Lần đầu kết nối, bạn cần cài **driver CH340C** để máy tính nhận cổng COM.

### Windows

1. Tải driver và cài đặt theo hướng dẫn tại [đây](https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board)
2. Mở file `.exe` và nhấn **INSTALL**
3. Sau khi cài đặt, rút và cắm lại BeE Board
4. Mở **Device Manager → Ports (COM & LPT)**, bạn sẽ thấy cổng như `CH340 (COM3)`

---

### MacOS & Linux

Trên hầu hết các bản phân phối của MacOS và Linux, CH340C đã có sẵn driver.
Nếu không nhận cổng, kiểm tra bằng:

```bash
dmesg | grep ttyUSB
```

---

## 3. Truy cập nền tảng lập trình

| Nền tảng       | Mục đích                       | Liên kết                                                                                         |
| -------------- | ------------------------------ | ------------------------------------------------------------------------------------------------ |
| **BeE IDE**    | Lập trình kéo thả Blockly      | [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide) |
| **BeE Python** | Lập trình bằng ngôn ngữ Python | [https://beestemsolutions.com.vn/studio/python](https://beestemsolutions.com.vn/studio/python)   |

> Cả hai nền tảng hoạt động hoàn toàn **trực tuyến**, không cần tải về.

---

## 4. Kết nối BeE Board V2 qua USB

1. Cắm cáp USB-C vào BeE Board và máy tính
2. Mở BeE IDE hoặc BeE Python
3. Nhấn nút **Kết nối → Chọn thiết bị USB**
4. Chọn dòng có chữ `cu.usbmodemxxxx` (MacOS) hoặc `COMx` (Windows) hoặc `/dev/ttyUSBx` (Linux)
5. Khi kết nối thành công, biểu tượng ▶️ sẽ sáng lên

---

## 5. Cấu hình kết nối qua Wi-Fi (OTA)

1. Trong Blockly, kéo khối **“Setup OTA Wi-Fi”**
2. Nhập **tên mạng (SSID)** và **mật khẩu**
3. Khi board kết nối thành công, màn hình OLED hiển thị **địa chỉ IP**
4. Trong BeE IDE hoặc BeE Python → chọn **Upload OTA** → nhập IP để nạp code không dây

---

## 6. Kiểm tra hoạt động của board

Dán đoạn code sau trong **BeE Python** để kiểm tra nhanh:

```python
from BeeBrain import bee
import time

bee.led1.set_rgb(0, 255, 0)
bee.oled.write("BeE Ready!", 0, 0, 1)
bee.buzzer.play_tone(bee.buzzer.TONES['C5'])
time.sleep(0.3)
bee.buzzer.be_quiet()
```

> Nếu LED sáng xanh + OLED hiển thị chữ “BeE Ready!” + nghe tiếng “bíp” → board hoạt động bình thường.

---

## 7. Sự cố thường gặp

| Vấn đề                     | Nguyên nhân                       | Cách khắc phục                      |
| -------------------------- | --------------------------------- | ----------------------------------- |
| Không thấy cổng USB        | Chưa cài driver CH340C            | Cài lại driver từ WCH               |
| Board không sáng đèn nguồn | Cáp chỉ sạc hoặc nguồn yếu        | Dùng cáp dữ liệu USB-C chất lượng   |
| Không kết nối được IDE     | Trình duyệt chưa cấp quyền USB    | Chọn “Cho phép” khi trình duyệt hỏi |
| Không nạp được code OTA    | Board và máy tính khác mạng Wi-Fi | Đảm bảo cùng mạng LAN               |

---

## 8. Bước tiếp theo

-   [Bắt đầu với BeE Board V2](1.bee-board-v2/2.getting-started.md)
-   [Lập trình với BeE IDE](2.bee-ide/1.index.md)
-   [Cài đặt module mở rộng](4.extensions/1.index.md)
-   [Giới thiệu phần cứng BeE Board V2](1.bee-board-v2/3.hardware-overview.md)
