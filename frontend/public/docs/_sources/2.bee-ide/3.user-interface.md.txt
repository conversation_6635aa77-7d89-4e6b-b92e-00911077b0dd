# Giao diện

<p align="center">
  <img 
    src="../_static/bee-board-v2/bee-ide.png" 
    alt="BeE IDE UI Overview" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

---

## Mục tiêu

Hiểu rõ **bố cục giao diện** của BeE IDE để học sinh và giáo viên có thể **sử dụng hiệu quả các công cụ lập trình kéo thả**.

---

## Tổng quan giao diện

<p align="center">
    <img 
    src="../_static/bee-ide/bee-ide.png" 
    alt="BeE IDE Layout" 
    width="100%" 
    style="border-radius: 10px; max-width: 100%;"/>
</p>

BeE IDE được chia thành **6 khu vực chính**:

| <PERSON>hu vực          | <PERSON>ê<PERSON>                                              | <PERSON><PERSON><PERSON> năng                       |
| ---------------- | ------------------------------------------------ | ------------------------------- |
| ① Thanh công cụ  | Tạo mới, mở, lưu, nạp code                       | Điều khiển chung                |
| ② Danh mục khối  | Chứa các nhóm lệnh (LED, Button, Motor, OLED...) | Kéo thả lệnh từ đây             |
| ③ Vùng làm việc  | Nơi ghép nối các khối lệnh                       | Xây dựng chương trình           |
| ④ Khu điều khiển | Nút **Upload**                                   | Nạp và chạy code trên BeE Board |
| ⑤ Log            | Hiển thị thông báo                               | Kết quả và thông báo lỗi        |
| ⑥ BeE Assistant  | Trợ lý AI giúp lập trình                         | Hỗ trợ giải thích các khối lệnh |

---

## 1. Thanh công cụ (Toolbar)

<p align="center">
  <img 
    src="../_static/bee-ide/toolbar.png" 
    alt="Toolbar" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

| Nút                   | Biểu tượng                                                                              | Chức năng                  |
| --------------------- | --------------------------------------------------------------------------------------- | -------------------------- |
| **Project Name**      | <p align="center"><img src="../_static/bee-ide/project-name.png" width="100" /></p>     | Chọn tên cho dự án của bạn |
| **Connect Serial**    | <p align="center"><img src="../_static/bee-ide/connect-serial.png" width="20" /></p>    | Chọn cổng USB              |
| **Connect Bluetooth** | <p align="center"><img src="../_static/bee-ide/connect-bluetooth.png" width="20" /></p> | Chọn thiết bị Bluetooth    |
| **Save**              | <p align="center"><img src="../_static/bee-ide/save.png" width="20" /></p>              | Lưu trong trình duyệt      |
| **Load**              | <p align="center"><img src="../_static/bee-ide/load.png" width="20" /></p>              | Tải lại dự án đã lưu       |

---

## 2. Danh mục khối lệnh (Toolbox)

<p align="center">
  <img 
    src="../_static/bee-ide/toolbox.png" 
    alt="Toolbox" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

Các khối lệnh trong BeE IDE được chia theo **nhóm chức năng**:

| Nhóm               | Biểu tượng                                                                          | Mô tả                          |
| ------------------ | ----------------------------------------------------------------------------------- | ------------------------------ |
| **LED**            | <p align="center"><img src="../_static/bee-ide/led.png" width="20" /></p>           | Bật/tắt, đổi màu LED           |
| **Button**         | <p align="center"><img src="../_static/bee-ide/button.png" width="20" /></p>        | Kiểm tra nút A/B được nhấn     |
| **Buzzer**         | <p align="center"><img src="../_static/bee-ide/buzzer.png" width="20" /></p>        | Phát nốt nhạc hoặc âm thanh    |
| **OLED**           | <p align="center"><img src="../_static/bee-ide/display.png" width="20" /></p>       | Hiển thị văn bản, cảm biến     |
| **Motor**          | <p align="center"><img src="../_static/bee-ide/motor.png" width="20" /></p>         | Điều khiển servo, motor        |
| **Servo**          | <p align="center"><img src="../_static/bee-ide/servo.png" width="20" /></p>         | Điều khiển servo, motor        |
| **IMU**            | <p align="center"><img src="../_static/bee-ide/imu.png" width="20" /></p>           | Đọc góc nghiêng, phát hiện lắc |
| **Digital Read**   | <p align="center"><img src="../_static/bee-ide/digital-read.png" width="20" /></p>  | Đọc tín hiệu digital           |
| **Analog Read**    | <p align="center"><img src="../_static/bee-ide/analog-read.png" width="20" /></p>   | Đọc tín hiệu analog            |
| **Digital Write**  | <p align="center"><img src="../_static/bee-ide/digital-write.png" width="20" /></p> | Ghi tín hiệu digital           |
| **Advanced / OTA** | <p align="center"><img src="../_static/bee-ide/ota.png" width="20" /></p>           | Setup Wi-Fi, OTA, Reset        |

---

## 3. Khu vực làm việc (Workspace)

<p align="center">
  <img 
    src="../_static/bee-ide/workspace.png" 
    alt="Workspace" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

-   Kéo thả các khối từ Toolbox vào Workspace.
-   Các khối tự động **khớp (snap)** với nhau theo logic.
-   Sử dụng chuột cuộn để phóng to/thu nhỏ.
-   Nhấn **Ctrl + A** để chọn tất cả khối, **Delete** để xóa.

> Mỗi chương trình sẽ có **một khối bắt đầu chính** (ví dụ: “Khi bắt đầu chạy chương trình”).

---

## 4. Bảng Log

<p align="center">
  <img 
    src="../_static/bee-ide/log.png" 
    alt="Log" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

Bảng này hiển thị:

-   Thông báo khi nạp code thành công
-   Cảnh báo hoặc lỗi khi kết nối không thành công

> Gợi ý: Khi gặp lỗi “Connection failed”, hãy kiểm tra lại **cáp USB hoặc IP Wi-Fi**.

---

## 5. Giao diện tùy chỉnh

BeE IDE hỗ trợ **ngôn ngữ tiếng Việt / tiếng Anh** tự động nhận theo trình duyệt.

<p align="center">
  <img 
    src="../_static/bee-ide/language.png" 
    alt="Theme Switch" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

| Tùy chọn             | Mô tả                                    |
| -------------------- | ---------------------------------------- |
| 🇻🇳 / 🇬🇧 **Ngôn ngữ** | Tự động chuyển theo ngôn ngữ trình duyệt |

---

## 6. BeE Assistant

<p align="center">
  <img 
    src="../_static/bee-ide/assistant.png" 
    alt="Assistant" 
    width="500" 
    style="border-radius: 10px; max-width: 100%;"
  />
</p>

BeE Assistant là trợ lý AI tích hợp sẵn trong BeE IDE, giúp học sinh và giáo viên **học hỏi và giải đáp thắc mắc**.

## 7. Mẹo sử dụng hiệu quả

-   Nhấn **Ctrl + Z / Ctrl + Y** để hoàn tác hoặc khôi phục thao tác
-   Sử dụng **khối “ghi chú” (comment)** để giải thích từng phần chương trình
-   Thường xuyên **lưu dự án (.bee)** sau mỗi bài học
-   Dùng **khối “chờ 0.5 giây”** để làm mượt hiệu ứng LED, motor, buzzer

---

## 8. Tài liệu liên quan

-   [Bắt đầu với BeE Board V2](../1.bee-board-v2/2.getting-started.md)
-   [Cài đặt và kết nối BeE Board](2.installation.md)
-   [Cài đặt module mở rộng](../4.extensions/1.index.md)
