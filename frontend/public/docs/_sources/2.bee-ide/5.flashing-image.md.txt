# Nạp Firmware

---

## 1. Giới thiệu

BeE IDE hỗ trợ **2 cách** để flashing firmware lên BeE Board:

| Phương thức | <PERSON><PERSON> tả                                         | Khi nào dùng?         |
| ----------- | --------------------------------------------- | --------------------- |
| **USB**     | Kết nối BeE Board với máy tính bằng cáp USB-C | Lập trình và kiểm tra |

> 💡 **Lưu ý:** Hãy chắc chắn rằng bạn đã cài đặt **USB Serial Driver** cho BeE Board V2. Xem [tại đây](https://beestemsolutions.com.vn/blog/cai-at-driver-ch340-e-nap-code-cho-mach-bee-board) để biết thêm thông tin.

---

## 2. Flashing firmware qua USB

1. Cắm cáp USB-C vào BeE Board và máy tính
2. Mở BeE IDE
3. Nhấn nút **Flash Firmware** để mở dialog flashing firmware
4. Chọn đúng cổng USB cho BeE Board
5. Nhấn nút **Flash** để bắt đầu flashing firmware

Lưu ý:

-   Trong quá trình flashing, bạn có thể thấy thông báo lỗi “Error while flashing firmware! Try to press reset button while flashing.”. Bạn cần nhấn giữ nút **BOOT** trên BeE Board và nhấn nhả nút **RESET** và nhả nút **BOOT** để vào trạng thái flashing.
-   Hãy chắc chắn rằng bạn đã cài đặt **USB Serial Driver** cho BeE Board V2. Xem [tại đây](https://github.com/BeE-STEM-Solutions/bee-board-v2#usb-serial-driver) để biết thêm thông tin.
-   Flashing firmware sẽ mất khoảng 1-2 phút, vui lòng không tắt nguồn hoặc ngắt kết nối trong quá trình flashing.
