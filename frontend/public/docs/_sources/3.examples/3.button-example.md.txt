# Nút nhấn

![Button Example Cover](_static/bee-board-v2/examples/button-example-cover.jpg)

---

## M<PERSON><PERSON> tiêu

Trong bài học này, bạn sẽ học cách:

-   Phát hiện khi nút nhấn **A hoặc B** được bấm
-   Kết hợp nút nhấn để **điều khiển LED và Buzzer**
-   Lập trình hành vi phản ứng khi người dùng tương tác với board

---

## Phần cứng cần có

| Thành phần      | Số lượng | Ghi chú                         |
| --------------- | -------- | ------------------------------- |
| BeE Board V2    | 1        | Bo mạch chính có sẵn nút A và B |
| Cáp USB-C       | 1        | Kết nối với máy tính            |
| Trình duyệt web | 1        | Chrome, Edge hoặc Safari        |

> 💡 Nút **A** và **B** được kết nối với GPIO nội bộ,
> bạn có thể truy cập qua `bee.buttonA` và `bee.buttonB`.

---

## 1. Lập trình với BeE IDE

Truy cập BeE IDE:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide)

### Bật LED khi nhấn nút A

Kéo các khối:

1. “Nếu nút A được nhấn”
2. “Bật LED 1 màu xanh lá cây”
3. “Nếu không thì tắt LED 1”

![Blockly Button LED](_static/bee-ide/examples/button-led-blockly.jpg)

Khi bạn nhấn nút **A**, LED 1 sáng lên
Khi thả tay, LED tắt.

---

### Phát nhạc khi nhấn nút B

1. “Nếu nút B được nhấn”
2. “Phát nốt C5 trong 1/2 giây”
3. “Nếu không thì tắt loa buzzer”

![Blockly Button Buzzer](_static/bee-ide/examples/button-buzzer-blockly.jpg)

Khi nhấn nút **B**, bạn sẽ nghe tiếng “bíp”.

---

### Kết hợp A & B

Bạn có thể tạo chương trình như:

-   A bật LED
-   B phát nhạc
-   A + B cùng nhấn thì đổi màu LED

![Blockly Button Combo](_static/bee-ide/examples/button-combo-blockly.jpg)

---

## 2. Lập trình với BeE Python

Truy cập BeE Python:
👉 [https://beestemsolutions.com.vn/studio/python](https://beestemsolutions.com.vn/studio/python)

### Bật LED khi nhấn nút A

```python
from BeeBrain import bee
import time

while True:
    if bee.buttonA.is_pressed():
        bee.led1.set_rgb(0, 255, 0)
    else:
        bee.led1.off()
    time.sleep(0.05)
```

Nhấn nút **A** → LED1 sáng màu xanh lá.
Thả ra → LED tắt.

---

### Phát nhạc khi nhấn nút B

```python
from BeeBrain import bee
import time

while True:
    if bee.buttonB.is_pressed():
        bee.buzzer.play_tone(bee.buzzer.TONES['C5'])
    else:
        bee.buzzer.be_quiet()
    time.sleep(0.05)
```

Khi bạn nhấn **B**, buzzer phát nốt C5.

---

### Kết hợp cả hai nút

```python
from BeeBrain import bee
import time

while True:
    if bee.buttonA.is_pressed() and bee.buttonB.is_pressed():
        bee.led1.set_rgb(255, 0, 255)
    elif bee.buttonA.is_pressed():
        bee.led1.set_rgb(0, 255, 0)
    elif bee.buttonB.is_pressed():
        bee.led1.set_rgb(0, 0, 255)
    else:
        bee.led1.off()
    time.sleep(0.05)
```

| Nút nhấn | Hành động      |
| -------- | -------------- |
| A        | LED xanh lá    |
| B        | LED xanh dương |
| A + B    | LED tím        |

---

## 3. Thảo luận và mở rộng

| Câu hỏi                                             | Gợi ý                                                  |
| --------------------------------------------------- | ------------------------------------------------------ |
| Làm sao để LED sáng khi giữ nút A, và tắt khi thả?  | Dùng `if bee.buttonA.is_pressed()` trong vòng lặp      |
| Làm sao để phát nhạc theo giai điệu khi nhấn nút B? | Dùng danh sách nốt và lặp qua `bee.buzzer.play_tone()` |
| Có thể tạo trò chơi bằng 2 nút này không?           | Có! Bạn có thể làm game “phản xạ nhanh” với A và B     |

---

## 4. Kết luận

Trong bài học này, bạn đã học cách:

-   Sử dụng nút nhấn A và B
-   Tạo phản hồi bằng LED và Buzzer
-   Xây dựng chương trình tương tác người dùng

> 💡 Hãy thử thêm OLED để hiển thị thông điệp “Xin chào!” khi nhấn nút nhé
