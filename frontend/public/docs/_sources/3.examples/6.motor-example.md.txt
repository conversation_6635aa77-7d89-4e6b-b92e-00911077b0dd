# Động cơ

![Motor Example Cover](_static/bee-board-v2/examples/motor-example-cover.jpg)

---

## <PERSON><PERSON><PERSON> tiêu

Trong bài học nà<PERSON>, bạn sẽ học cách:

-   Đi<PERSON>u khiển **động cơ DC** chạy tiến, lùi, dừng
-   <PERSON>i<PERSON>u khiển **servo** xoay góc chính xác
-   Tạo chương trình điều khiển robot di chuyển

---

## Phần cứng cần có

| Thành phần               | Số lượng | Ghi chú                          |
| ------------------------ | -------- | -------------------------------- |
| BeE Board V2             | 1        | Bo mạch chính                    |
| Bộ khung LEGO / xe robot | 1        | <PERSON><PERSON> thể gắn động cơ M1 & M2       |
| Cáp USB-C                | 1        | Kết nối với máy tính             |
| Nguồn pin 5V–8.4V        | 1        | Cấp cho motor hoạt động mạnh hơn |

> 💡 BeE Board V2 có sẵn **2 cổng điều khiển động cơ DC (M1, M2)**
> và **4 cổng servo (S1–S4)** được điều khiển qua I2C.

---

## 1. Lập trình với BeE IDE

Truy cập BeE IDE:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://ide.beestemsolutions.com.vn/studio/bee-ide)

### Chạy robot tiến 2 giây

Kéo các khối lệnh sau:

1. “Robot tiến với tốc độ 60% trong 2 giây”
2. “Dừng robot”

![Blockly Move Forward](_static/bee-ide/examples/motor-forward-blockly.jpg)

Khi nhấn **Run ▶️**, hai động cơ M1 và M2 sẽ quay cùng chiều, giúp xe di chuyển thẳng.

---

### Quay trái và quay phải

-   **Quay trái:** dùng “Robot quay trái 60% trong 1 giây”
-   **Quay phải:** dùng “Robot quay phải 60% trong 1 giây”

![Blockly Turn Left](_static/bee-ide/examples/motor-turn-blockly.jpg)

Kết hợp với vòng lặp `mãi mãi`, bạn có thể tạo robot tuần tra theo hình vuông.

---

### Điều khiển servo xoay góc

1. Chọn khối “Servo số ... xoay đến góc ... độ”
2. Đặt góc lần lượt: 0°, 90°, 180°
3. Thêm khối “chờ 0.5 giây” giữa mỗi lần

![Blockly Servo Example](_static/bee-ide/examples/servo-blockly.jpg)

Khi chạy, servo sẽ xoay qua lại như robot đang vẫy tay

---

## 2. Lập trình với BeE Python

Truy cập BeE Python:
👉 [https://beestemsolutions.com.vn/studio/python](https://python.beestemsolutions.com.vn/studio/python)

### 🔹 Chạy robot tiến & dừng

```python
from BeeBrain import bee
import time

bee.init_bee()

bee.move_forward(60, 2)
bee.stop_robot()
```

Xe robot sẽ chạy tiến với tốc độ 60% trong 2 giây, sau đó dừng.

---

### Quay trái và quay phải

```python
from BeeBrain import bee
import time

bee.turn_left(70, 1)
time.sleep(0.5)
bee.turn_right(70, 1)
bee.stop_robot()
```

> 💡 `turn_left()` và `turn_right()` điều khiển M1 & M2 theo hướng ngược nhau.

---

### Điều khiển từng motor riêng

```python
from BeeBrain import bee
import time

bee.motor1.speed(80)   # Motor trái
bee.motor2.speed(80)   # Motor phải
time.sleep(2)
bee.stop_robot()
```

---

### Điều khiển servo

```python
from BeeBrain import bee
import time

bee.init_bee()

bee.servo1.position(0)
time.sleep(0.5)
bee.servo1.position(90)
time.sleep(0.5)
bee.servo1.position(180)
```

Servo sẽ xoay tuần tự từ 0 → 90 → 180 độ.

---

## 3. Thảo luận và mở rộng

| Câu hỏi                               | Gợi ý                                                    |
| ------------------------------------- | -------------------------------------------------------- |
| Làm sao để robot tiến khi nhấn nút A? | Dùng `bee.buttonA.is_pressed()`                          |
| Làm sao để robot lùi khi gặp vật cản? | Dùng `bee.imu.is_shaking()` hoặc cảm biến khoảng cách    |
| Làm sao để servo xoay theo LED?       | Kết hợp `bee.led1.set_rgb()` với `bee.servo1.position()` |

---

## 4. Kết luận

Trong bài này, bạn đã học cách:

-   Điều khiển motor DC (tiến, lùi, dừng, quay)
-   Điều khiển servo xoay góc
-   Lập trình robot di chuyển bằng Blockly hoặc Python

> Bước tiếp theo: Hãy kết hợp **button + LED + motor** để làm robot “biết phản ứng” khi được nhấn!
