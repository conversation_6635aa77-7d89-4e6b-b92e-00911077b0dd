# Servo

![Servo Example Cover](_static/bee-board-v2/examples/servo-example-cover.jpg)

---

## <PERSON><PERSON><PERSON> tiêu

Trong bài học này, bạn sẽ học cách:

-   Điều khiển servo xoay đến góc mong muốn
-   Tạo chuyển động tuần tự và hiệu ứng servo mượt mà
-   Kết hợp servo với Button và LED để tạo robot có hành vi thông minh

---

## Phần cứng cần có

| Thành phần                       | Số lượng | Ghi chú              |
| -------------------------------- | -------- | -------------------- |
| BeE Board V2                     | 1        | Bo mạch chính        |
| Servo RC (SG90 hoặc tương đương) | 1–2      | Cắm vào cổng S1–S4   |
| Cáp USB-C                        | 1        | Kết nối với máy t<PERSON> |
| Nguồn pin 5–6V                   | 1        | Nếu dùng nhiều servo |

> 💡 BeE Board V2 có 4 cổng điều khiển servo: **S1 – S4**,
> được điều khiển thông qua chip **PCA9685 (I2C)**.

---

## 1. Lập trình với BeE IDE

Truy cập BeE IDE:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide)

### Xoay servo đến góc 90°

Kéo các khối:

1. “Servo số 1 xoay đến góc 90 độ”
2. “Chờ 1 giây”
3. “Servo số 1 xoay đến góc 0 độ”

![Blockly Servo Basic](_static/bee-ide/examples/servo-basic-blockly.jpg)

Khi nhấn **Run ▶️**, servo xoay từ 0° → 90° rồi trở lại 0°.

---

### Xoay tuần tự nhiều góc

1. Dùng khối “lặp lại 3 lần”
2. Trong vòng lặp, tăng góc mỗi lần 30°
3. Thêm “chờ 0.2 giây” giữa mỗi lần

![Blockly Servo Loop](_static/bee-ide/examples/servo-loop-blockly.jpg)

Kết quả: servo xoay dần từng bước, tạo hiệu ứng mượt mà

---

### Điều khiển servo bằng nút A/B

1. Nếu **A được nhấn** → servo xoay sang 0°
2. Nếu **B được nhấn** → servo xoay sang 180°

![Blockly Servo Button](_static/bee-ide/examples/servo-button-blockly.jpg)

---

## 2. Lập trình với BeE Python

Truy cập BeE Python:
👉 [https://beestemsolutions.com.vn/studio/python](https://beestemsolutions.com.vn/studio/python)

### Xoay servo đến góc cố định

```python
from BeeBrain import bee
import time

bee.servo1.position(90)
time.sleep(1)
bee.servo1.position(0)
```

Servo xoay từ 0° → 90° rồi trở lại.

---

### Xoay tuần tự nhiều góc

```python
from BeeBrain import bee
import time

for angle in range(0, 181, 30):
    bee.servo1.position(angle)
    time.sleep(0.2)
```

Servo sẽ xoay qua 0°, 30°, 60°, 90°, 120°, 150°, 180° tuần tự.

---

### Điều khiển bằng nút A/B

```python
from BeeBrain import bee
import time

while True:
    if bee.buttonA.is_pressed():
        bee.servo1.position(0)
    elif bee.buttonB.is_pressed():
        bee.servo1.position(180)
    time.sleep(0.05)
```

Khi nhấn nút **A**, servo về vị trí 0°.
Khi nhấn nút **B**, servo xoay sang 180°.

---

### Kết hợp LED và Servo

```python
from BeeBrain import bee
import time

for angle in [0, 90, 180, 90]:
    bee.led1.set_rgb(255, 255, 0)
    bee.servo1.position(angle)
    time.sleep(0.5)
    bee.led1.off()
```

Servo xoay tuần tự, LED nhấp nháy theo chuyển động

---

## 3. Thảo luận và mở rộng

| Câu hỏi                                            | Gợi ý                                             |
| -------------------------------------------------- | ------------------------------------------------- |
| Làm sao để servo xoay theo góc ngẫu nhiên?         | Dùng `random.randint(0,180)`                      |
| Làm sao để servo dừng ở vị trí giữa khi khởi động? | Gọi `bee.servo1.position(90)` ở đầu chương trình  |
| Có thể dùng nhiều servo cùng lúc không?            | Có! Dùng `bee.servo2`, `bee.servo3`, `bee.servo4` |
| Có thể điều khiển servo bằng cảm biến không?       | Có thể, ví dụ: xoay theo góc nghiêng IMU          |

---

## 4. Kết luận

Trong bài học này, bạn đã học cách:

-   Điều khiển servo xoay đến góc mong muốn
-   Tạo chuyển động tuần tự hoặc phản ứng theo nút nhấn
-   Kết hợp servo với LED và cảm biến

> 💡 Hãy thử làm **cánh tay robot mini** hoặc **robot gật đầu** bằng LEGO + BeE Board nhé
