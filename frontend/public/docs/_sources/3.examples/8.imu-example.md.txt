# Cảm biến IMU

![IMU Example Cover](_static/bee-board-v2/examples/imu-example-cover.jpg)

---

## Mục tiêu

Trong bài học này, bạn sẽ học cách:

-   Sử dụng cảm biến **IMU MPU6050** để phát hiện nghiêng và rung
-   Hiển thị dữ liệu góc nghiêng trên OLED
-   Kết hợp IMU với LED hoặc Servo để tạo robot phản ứng thông minh

---

## Phần cứng cần có

| Thành phần      | Số lượng | Ghi chú                                   |
| --------------- | -------- | ----------------------------------------- |
| BeE Board V2    | 1        | Bo mạch chính có sẵn cảm biến IMU MPU6050 |
| Cáp USB-C       | 1        | Kết nối với máy tính                      |
| Trình duyệt web | 1        | Chrome, Edge hoặc Safari                  |

> 💡 Cảm biến **MPU6050** đo đ<PERSON> **gia tốc (Accelerometer)** và **con quay hồi chuyển (Gyroscope)**,
> dùng để xác định **góc nghiêng, rung, hoặc chuyển động của board**.

---

## 1. Lập trình với BeE IDE

Truy cập BeE IDE:
👉 [https://beestemsolutions.com.vn/studio/bee-ide](https://beestemsolutions.com.vn/studio/bee-ide)

### Hiển thị góc nghiêng

1. Kéo khối “Lấy giá trị Roll của IMU”
2. Hiển thị giá trị đó trên màn hình OLED
3. Lặp lại liên tục

![Blockly IMU OLED](_static/bee-ide/examples/imu-oled-blockly.jpg)

> Mỗi khi bạn nghiêng board, giá trị trên OLED sẽ thay đổi theo góc nghiêng

---

### Phát hiện rung lắc

1. Dùng khối “Nếu board bị lắc thì...”
2. Bật LED1 màu đỏ hoặc phát tiếng Buzzer

![Blockly IMU Shake](_static/bee-ide/examples/imu-shake-blockly.jpg)

Khi bạn rung nhẹ board, LED sẽ sáng hoặc buzzer kêu cảnh báo

---

### Điều khiển Servo theo góc nghiêng

1. Đọc giá trị **Pitch**
2. Chuyển đổi sang góc servo (0–180)
3. Dùng khối “Servo số 1 xoay đến góc ... độ”

![Blockly IMU Servo](_static/bee-ide/examples/imu-servo-blockly.jpg)

---

## 2. Lập trình với BeE Python

Truy cập BeE Python:
👉 [https://beestemsolutions.com.vn/studio/python](https://beestemsolutions.com.vn/studio/python)

### Hiển thị góc nghiêng

```python
from BeeBrain import bee
import time

while True:
    bee.imu.update()
    roll = bee.imu.rollDeg
    pitch = bee.imu.pitchDeg
    bee.oled.clear()
    bee.oled.write(f"Roll: {roll:.1f}", 0, 0, 1)
    bee.oled.write(f"Pitch: {pitch:.1f}", 0, 1, 1)
    time.sleep(0.2)
```

Khi nghiêng board, giá trị **Roll** và **Pitch** trên OLED thay đổi theo.

---

### Phát hiện rung (Shake Detection)

```python
from BeeBrain import bee
import time

while True:
    bee.imu.update()
    if bee.imu.is_shaking():
        bee.led1.set_rgb(255, 0, 0)
        bee.buzzer.play_tone(bee.buzzer.TONES['C6'])
        time.sleep(0.2)
        bee.buzzer.be_quiet()
    else:
        bee.led1.off()
    time.sleep(0.05)
```

Khi rung nhẹ board → LED sáng đỏ + kêu “bíp” cảnh báo

---

### Điều khiển Servo theo góc nghiêng

```python
from BeeBrain import bee
import time

while True:
    bee.imu.update()
    angle = max(0, min(180, int(bee.imu.rollDeg + 90)))
    bee.servo1.position(angle)
    time.sleep(0.05)
```

Khi nghiêng board sang trái/phải, servo xoay theo cùng hướng

---

## 3. Thảo luận và mở rộng

| Câu hỏi                                           | Gợi ý                                                              |
| ------------------------------------------------- | ------------------------------------------------------------------ |
| Làm sao để đo độ rung mạnh?                       | Dùng `bee.imu.shake_strength`                                      |
| Có thể phát hiện hướng xoay không?                | Dùng `bee.imu.gyroZ` để tính vận tốc quay                          |
| Làm sao để hiển thị đồ thị góc nghiêng trên OLED? | Dùng hàm `bee.oled.write_bar(value)` (phiên bản sắp phát hành)     |
| Có thể kết hợp IMU với robot di chuyển không?     | Có! Dùng IMU để giúp robot giữ thăng bằng hoặc tự quay khi nghiêng |

---

## 4. Kết luận

Trong bài học này, bạn đã học cách:

-   Đọc dữ liệu cảm biến IMU (Roll, Pitch, Shake)
-   Hiển thị dữ liệu trên OLED
-   Kết hợp IMU với LED, Buzzer, và Servo để tạo robot phản ứng thông minh

> 💡 Hãy thử làm **robot giữ thăng bằng mini** hoặc **cảm biến phát hiện động đất mini** nhé!
