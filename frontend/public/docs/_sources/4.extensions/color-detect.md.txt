# BeeColorDetect

## Giới thiệu

BeeColorDetect là module cảm biến màu sắc sử dụng chip TCS34725 với độ chính xác cao. <PERSON><PERSON><PERSON> có thể nhận diện màu sắc RGB, chuy<PERSON><PERSON> đổ<PERSON> sang HSV và xác định các màu cơ bản. Chip TCS34725 tích hợp LED trắng để chiếu sáng mẫu, giúp đo màu chính xác trong nhiều điều kiện ánh sáng khác nhau.

Module được thiết kế để dễ sử dụng với giao diện I2C, phù hợp cho các dự án robotics, automation và giáo dục STEM. Đặc biệt hữu ích cho robot phân loại màu, game tương tác và các ứng dụng IoT.

**Ứng dụng thực tế:**

-   Robot phân loại vật phẩm theo màu
-   <PERSON>ệ thống kiểm tra chất lượng sản phẩm
-   Game tương tác với màu sắc
-   M<PERSON>y trộn màu tự động
-   Hệ thống nhận diện màu cho người khiếm thị
-   Dự án nghệ thuật tương tác
-   Cảm biến môi trường (đo độ đục nước)

## Thông số kỹ thuật

| Thông số           | Giá trị                              |
| ------------------ | ------------------------------------ |
| Chip cảm biến      | TCS34725                             |
| Giao tiếp          | I2C                                  |
| Địa chỉ I2C        | 0x29 (mặc định)                      |
| Điện áp hoạt động  | 3.3V                                 |
| Dòng tiêu thụ      | 65μA (hoạt động), 2.5μA (sleep)      |
| Độ phân giải ADC   | 16-bit                               |
| Kênh màu           | Red, Green, Blue, Clear              |
| LED tích hợp       | LED trắng 3000K                      |
| Gain               | 1x, 4x, 16x, 60x (có thể điều chỉnh) |
| Thời gian tích hợp | 2.4ms - 614ms                        |
| Phạm vi nhiệt độ   | -30°C đến +70°C                      |
| Kích thước         | 20mm x 16mm                          |

## Giao diện lập trình

### Khởi tạo

```python
from BeeColorDetect import BeeColorDetect
from BeeBrain import bee

# Khởi tạo với cổng I2C mặc định
color_sensor = BeeColorDetect()

# Khởi tạo với BeeBrain
color_sensor = BeeColorDetect(bee.i2c)
```

### Cấu hình cảm biến

#### `gain` - Thiết lập độ khuếch đại

```python
# Thiết lập gain (1, 4, 16, 60)
color_sensor.gain = 16  # Gain 16x cho độ nhạy cao

# Đọc gain hiện tại
current_gain = color_sensor.gain
print(f"Current gain: {current_gain}x")
```

#### `integration_time` - Thiết lập thời gian tích hợp

```python
# Thiết lập thời gian tích hợp (ms)
color_sensor.integration_time = 50  # 50ms

# Đọc thời gian tích hợp hiện tại
current_time = color_sensor.integration_time
print(f"Integration time: {current_time}ms")
```

### Đọc dữ liệu màu

#### `get_all_colors_in_rgb()` - Đọc tất cả kênh RGB

```python
# Đọc giá trị RGB và Clear
red, green, blue, clear = color_sensor.get_all_colors_in_rgb()

print(f"Red: {red}")
print(f"Green: {green}")
print(f"Blue: {blue}")
print(f"Clear: {clear}")
```

#### `get_color()` - Nhận diện màu cơ bản

```python
# Nhận diện màu cơ bản
color_name = color_sensor.get_color()
print(f"Detected color: {color_name}")

# Các màu có thể nhận diện:
# "RED", "GREEN", "BLUE", "YELLOW", "CYAN", "MAGENTA", "WHITE", "BLACK"
```

#### `is_color(color_name)` - Kiểm tra màu cụ thể

```python
# Kiểm tra có phải màu đỏ không
if color_sensor.is_color("RED"):
    print("This is RED!")

# Kiểm tra màu xanh lá
if color_sensor.is_color("GREEN"):
    print("This is GREEN!")
```

#### `html_hex()` - Chuyển đổi sang mã hex HTML

```python
# Lấy mã màu hex (ví dụ: "#FF0000" cho đỏ)
hex_color = color_sensor.html_hex()
print(f"HTML color: {hex_color}")
```

## Ví dụ Blockly

```
when program starts:
    set color_sensor to BeeColorDetect
    set color_sensor gain to 16
    set color_sensor integration_time to 50

forever:
    set detected_color to color_sensor get_color

    if detected_color equals "RED":
        set LED to RED color
        play tone C4
    else if detected_color equals "GREEN":
        set LED to GREEN color
        play tone E4
    else if detected_color equals "BLUE":
        set LED to BLUE color
        play tone G4

    wait 0.5 seconds
```

## Ví dụ Python

### Ví dụ cơ bản - Nhận diện màu đơn giản

```python
from BeeColorDetect import BeeColorDetect
from BeeBrain import bee
import time

def setup():
    """Khởi tạo cảm biến màu"""
    global color_sensor

    try:
        # Khởi tạo cảm biến
        color_sensor = BeeColorDetect()

        # Cấu hình tối ưu
        color_sensor.gain = 16
        color_sensor.integration_time = 50

        # Test cảm biến
        red, green, blue, clear = color_sensor.get_all_colors_in_rgb()

        bee.oled.clear()
        bee.oled.write("Color Sensor", 15, 0)
        bee.oled.write("Ready!", 25, 15)
        bee.oled.write(f"R:{red} G:{green}", 5, 30)
        bee.oled.write(f"B:{blue} C:{clear}", 5, 45)

        return True

    except Exception as e:
        bee.oled.clear()
        bee.oled.write("Sensor Error!", 15, 10)
        bee.oled.write(str(e)[:20], 5, 25)
        return False

def simple_color_detection():
    """Nhận diện màu cơ bản"""
    color_count = {"RED": 0, "GREEN": 0, "BLUE": 0, "OTHER": 0}

    while True:
        try:
            # Đọc màu
            detected_color = color_sensor.get_color()
            hex_color = color_sensor.html_hex()

            # Đếm màu
            if detected_color in color_count:
                color_count[detected_color] += 1
            else:
                color_count["OTHER"] += 1

            # Hiển thị kết quả
            bee.oled.clear()
            bee.oled.write("Color Detection", 10, 0)
            bee.oled.write(f"Color: {detected_color}", 5, 15)
            bee.oled.write(f"Hex: {hex_color}", 5, 30)
            bee.oled.write(f"Count: {color_count[detected_color] if detected_color in color_count else color_count['OTHER']}", 5, 45)

            # LED phản hồi
            if detected_color == "RED":
                bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
                bee.buzzer.play_tone("C4", 0.1)
            elif detected_color == "GREEN":
                bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
                bee.buzzer.play_tone("E4", 0.1)
            elif detected_color == "BLUE":
                bee.neopixel.set_rgb(bee.LED1, 0, 0, 255)
                bee.buzzer.play_tone("G4", 0.1)
            else:
                bee.neopixel.set_rgb(bee.LED1, 50, 50, 50)

            # Kiểm tra nút thoát
            if bee.is_button_pressed(bee.BUTTON_B):
                break

            time.sleep(0.5)

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Read Error!", 15, 10)
            time.sleep(1)

def interactive_color_test():
    """Test màu tương tác"""
    test_colors = ["RED", "GREEN", "BLUE", "YELLOW", "WHITE", "BLACK"]
    current_test = 0

    while current_test < len(test_colors):
        target_color = test_colors[current_test]

        bee.oled.clear()
        bee.oled.write("Color Test", 20, 0)
        bee.oled.write(f"Show: {target_color}", 10, 15)
        bee.oled.write("A: Check", 15, 30)
        bee.oled.write("B: Skip", 20, 45)

        # Chờ người dùng
        while True:
            if bee.is_button_pressed(bee.BUTTON_A):
                # Kiểm tra màu
                detected = color_sensor.get_color()

                if detected == target_color:
                    bee.oled.clear()
                    bee.oled.write("CORRECT!", 20, 15)
                    bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
                    bee.buzzer.play_song("C4 E4 G4")
                    current_test += 1
                else:
                    bee.oled.clear()
                    bee.oled.write("WRONG!", 25, 10)
                    bee.oled.write(f"Got: {detected}", 10, 25)
                    bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
                    bee.buzzer.play_tone("C3", 0.5)

                time.sleep(2)
                break

            elif bee.is_button_pressed(bee.BUTTON_B):
                current_test += 1
                break

            time.sleep(0.1)

    # Hoàn thành test
    bee.oled.clear()
    bee.oled.write("Test Complete!", 15, 20)
    bee.neopixel.set_rgb(bee.LED1, 0, 255, 255)

# Chạy chương trình
if setup():
    print("1. Simple Color Detection")
    print("2. Interactive Color Test")

    # Demo tự động
    simple_color_detection()
    time.sleep(1)
    interactive_color_test()
```

### Ví dụ nâng cao - Robot phân loại màu

```python
from BeeColorDetect import BeeColorDetect
from BeeBrain import bee
import time
import json

class ColorSortingRobot:
    def __init__(self):
        # Khởi tạo cảm biến màu
        self.color_sensor = BeeColorDetect()
        self.color_sensor.gain = 16
        self.color_sensor.integration_time = 50

        # Thống kê phân loại
        self.color_stats = {
            "RED": 0, "GREEN": 0, "BLUE": 0,
            "YELLOW": 0, "WHITE": 0, "BLACK": 0, "OTHER": 0
        }

        # Cấu hình servo cho phân loại
        self.servo_positions = {
            "RED": 0,      # Góc 0° - hộp đỏ
            "GREEN": 45,   # Góc 45° - hộp xanh lá
            "BLUE": 90,    # Góc 90° - hộp xanh dương
            "YELLOW": 135, # Góc 135° - hộp vàng
            "OTHER": 180   # Góc 180° - hộp khác
        }

        # Trạng thái robot
        self.is_running = False
        self.total_sorted = 0
        self.current_item = None

    def setup(self):
        """Khởi tạo hệ thống"""
        try:
            # Test cảm biến màu
            red, green, blue, clear = self.color_sensor.get_all_colors_in_rgb()

            # Test servo phân loại
            bee.servo.position(0, 90)  # Servo về vị trí giữa
            time.sleep(0.5)

            # Hiển thị thông tin khởi tạo
            bee.oled.clear()
            bee.oled.write("Color Sorting", 15, 0)
            bee.oled.write("Robot Ready!", 15, 15)
            bee.oled.write(f"RGB: {red},{green},{blue}", 5, 30)
            bee.oled.write("A:Start B:Stop", 10, 45)

            # LED sẵn sàng
            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
            bee.buzzer.play_song("C4 E4 G4")

            return True

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Setup Error!", 15, 10)
            bee.oled.write(str(e)[:20], 5, 25)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
            return False

    def detect_and_classify_color(self):
        """Phát hiện và phân loại màu"""
        try:
            # Đọc màu nhiều lần để chính xác
            color_readings = []
            for _ in range(5):
                color = self.color_sensor.get_color()
                color_readings.append(color)
                time.sleep(0.1)

            # Lấy màu xuất hiện nhiều nhất
            most_common = max(set(color_readings), key=color_readings.count)

            # Lấy giá trị RGB để hiển thị
            red, green, blue, clear = self.color_sensor.get_all_colors_in_rgb()
            hex_color = self.color_sensor.html_hex()

            return {
                "color": most_common,
                "rgb": (red, green, blue),
                "hex": hex_color,
                "confidence": color_readings.count(most_common) / len(color_readings)
            }

        except Exception as e:
            return {"color": "ERROR", "rgb": (0, 0, 0), "hex": "#000000", "confidence": 0}

    def sort_item(self, color_info):
        """Phân loại vật phẩm"""
        color = color_info["color"]

        # Xác định vị trí servo
        if color in self.servo_positions:
            target_position = self.servo_positions[color]
        else:
            target_position = self.servo_positions["OTHER"]
            color = "OTHER"

        # Di chuyển servo để phân loại
        bee.servo.position(0, target_position)

        # Cập nhật thống kê
        self.color_stats[color] += 1
        self.total_sorted += 1

        # Hiệu ứng LED theo màu
        rgb = color_info["rgb"]
        bee.neopixel.set_rgb(bee.LED1, rgb[0]//10, rgb[1]//10, rgb[2]//10)

        # Âm thanh xác nhận
        if color == "RED":
            bee.buzzer.play_tone("C4", 0.2)
        elif color == "GREEN":
            bee.buzzer.play_tone("E4", 0.2)
        elif color == "BLUE":
            bee.buzzer.play_tone("G4", 0.2)
        elif color == "YELLOW":
            bee.buzzer.play_tone("A4", 0.2)
        else:
            bee.buzzer.play_tone("F4", 0.2)

        # Chờ vật phẩm rơi vào hộp
        time.sleep(1)

        # Trở về vị trí chờ
        bee.servo.position(0, 90)
        time.sleep(0.5)

        return color

    def display_status(self, color_info=None):
        """Hiển thị trạng thái hệ thống"""
        bee.oled.clear()

        if self.is_running:
            bee.oled.write("SORTING...", 20, 0)

            if color_info:
                color = color_info["color"]
                confidence = int(color_info["confidence"] * 100)

                bee.oled.write(f"Color: {color}", 5, 15)
                bee.oled.write(f"Conf: {confidence}%", 5, 30)
                bee.oled.write(f"Total: {self.total_sorted}", 5, 45)
            else:
                bee.oled.write("Waiting item...", 10, 20)
                bee.oled.write(f"Sorted: {self.total_sorted}", 10, 35)
        else:
            bee.oled.write("STOPPED", 25, 10)
            bee.oled.write(f"Total: {self.total_sorted}", 15, 25)
            bee.oled.write("A:Start B:Stats", 5, 40)

    def show_statistics(self):
        """Hiển thị thống kê chi tiết"""
        stats_pages = []

        # Trang 1: Tổng quan
        page1 = [
            "=== STATISTICS ===",
            f"Total sorted: {self.total_sorted}",
            f"Red: {self.color_stats['RED']}",
            f"Green: {self.color_stats['GREEN']}"
        ]
        stats_pages.append(page1)

        # Trang 2: Chi tiết
        page2 = [
            "=== DETAILS ===",
            f"Blue: {self.color_stats['BLUE']}",
            f"Yellow: {self.color_stats['YELLOW']}",
            f"Other: {self.color_stats['OTHER']}"
        ]
        stats_pages.append(page2)

        # Hiển thị từng trang
        for page in stats_pages:
            bee.oled.clear()
            for i, line in enumerate(page):
                bee.oled.write(line, 5, i * 12)

            time.sleep(3)

        # Lưu thống kê vào file
        try:
            with open("/stats.json", "w") as f:
                json.dump({
                    "total_sorted": self.total_sorted,
                    "color_stats": self.color_stats,
                    "timestamp": time.time()
                }, f)

            bee.oled.clear()
            bee.oled.write("Stats saved!", 15, 20)
            time.sleep(2)

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("Save failed!", 15, 20)
            time.sleep(2)

    def detect_item_presence(self):
        """Phát hiện có vật phẩm cần phân loại"""
        # Sử dụng giá trị Clear để phát hiện vật phẩm
        _, _, _, clear = self.color_sensor.get_all_colors_in_rgb()

        # Ngưỡng phát hiện vật phẩm (có thể điều chỉnh)
        return clear > 100  # Có vật phẩm khi clear > 100

    def run_sorting_cycle(self):
        """Chu trình phân loại tự động"""
        while self.is_running:
            try:
                # Kiểm tra có vật phẩm không
                if self.detect_item_presence():
                    # Phát hiện màu
                    color_info = self.detect_and_classify_color()

                    # Hiển thị thông tin
                    self.display_status(color_info)

                    # Phân loại nếu độ tin cậy đủ cao
                    if color_info["confidence"] >= 0.6:  # 60% confidence
                        sorted_color = self.sort_item(color_info)

                        # Hiển thị kết quả
                        bee.oled.clear()
                        bee.oled.write("SORTED!", 25, 10)
                        bee.oled.write(f"Color: {sorted_color}", 10, 25)
                        time.sleep(1)
                    else:
                        # Độ tin cậy thấp
                        bee.oled.clear()
                        bee.oled.write("LOW CONFIDENCE", 5, 15)
                        bee.oled.write("Try again...", 15, 30)
                        bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)  # Vàng cảnh báo
                        time.sleep(1)
                else:
                    # Không có vật phẩm
                    self.display_status()
                    bee.neopixel.set_rgb(bee.LED1, 0, 0, 255)  # Xanh chờ

                # Kiểm tra nút dừng
                if bee.is_button_pressed(bee.BUTTON_B):
                    self.is_running = False
                    break

                time.sleep(0.2)

            except Exception as e:
                bee.oled.clear()
                bee.oled.write("Cycle Error!", 15, 10)
                bee.oled.write(str(e)[:20], 5, 25)
                time.sleep(2)

    def run(self):
        """Vòng lặp chính"""
        if not self.setup():
            return

        while True:
            try:
                self.display_status()

                # Kiểm tra nút bấm
                if bee.is_button_pressed(bee.BUTTON_A):
                    if not self.is_running:
                        # Bắt đầu phân loại
                        self.is_running = True
                        bee.oled.clear()
                        bee.oled.write("Starting...", 20, 20)
                        time.sleep(1)

                        # Chạy chu trình phân loại
                        self.run_sorting_cycle()

                    time.sleep(0.3)  # Debounce

                elif bee.is_button_pressed(bee.BUTTON_B):
                    if not self.is_running:
                        # Hiển thị thống kê
                        self.show_statistics()

                    time.sleep(0.3)  # Debounce

                time.sleep(0.1)

            except KeyboardInterrupt:
                break

        # Cleanup
        self.is_running = False
        bee.servo.position(0, 90)  # Servo về giữa
        bee.neopixel.set_rgb(bee.LED1, 0, 0, 0)  # Tắt LED

# Chạy robot phân loại màu
if __name__ == "__main__":
    robot = ColorSortingRobot()
    robot.run()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Color detection**: Nhận diện màu cơ bản với feedback LED và âm thanh
2. **RGB reading**: Đọc giá trị RGB thô và chuyển đổi hex
3. **Interactive testing**: Test màu tương tác với người dùng
4. **Error handling**: Xử lý lỗi cảm biến và hiển thị

### Ví dụ nâng cao:

1. **Multi-sample detection**: Đọc màu nhiều lần để tăng độ chính xác
2. **Confidence scoring**: Tính độ tin cậy của kết quả nhận diện
3. **Servo control**: Điều khiển servo để phân loại vật phẩm
4. **Statistics tracking**: Theo dõi và lưu thống kê phân loại
5. **Item presence detection**: Phát hiện vật phẩm bằng cảm biến Clear

## Bài tập mở rộng

1. **Color mixing system**: Hệ thống trộn màu tự động dựa trên cảm biến
2. **Quality control robot**: Robot kiểm tra chất lượng sản phẩm theo màu
3. **Interactive art installation**: Tác phẩm nghệ thuật tương tác với màu sắc

## Lỗi thường gặp

```{admonition} Lỗi: Cảm biến không phát hiện màu chính xác
:class: warning

**Nguyên nhân**: Ánh sáng môi trường không ổn định hoặc cấu hình sai

**Giải pháp**:
- Điều chỉnh gain phù hợp (1x cho ánh sáng mạnh, 60x cho ánh sáng yếu)
- Tăng integration_time cho độ chính xác cao hơn (50-200ms)
- Sử dụng LED tích hợp của cảm biến
- Che chắn ánh sáng ngoài khi đo
```

```{admonition} Lỗi: I2C communication failed
:class: warning

**Nguyên nhân**: Kết nối I2C không ổn định hoặc địa chỉ sai

**Giải pháp**:
- Kiểm tra kết nối SDA, SCL và nguồn điện
- Xác nhận địa chỉ I2C là 0x29
- Thêm pull-up resistor 4.7kΩ cho SDA/SCL
- Kiểm tra không có xung đột địa chỉ I2C
```

```{admonition} Lỗi: Màu nhận diện không ổn định
:class: warning

**Nguyên nhân**: Nhiễu ánh sáng hoặc vật liệu phản xạ kém

**Giải pháp**:
- Đọc màu nhiều lần và lấy giá trị trung bình
- Sử dụng ngưỡng confidence để lọc kết quả
- Đảm bảo khoảng cách ổn định giữa cảm biến và vật thể
- Sử dụng vật liệu có độ phản xạ tốt
```

## Tài nguyên tham khảo

-   [TCS34725 Datasheet](https://cdn-shop.adafruit.com/datasheets/TCS34725.pdf)
-   [Color Theory Guide](https://en.wikipedia.org/wiki/Color_theory)
-   [RGB to HSV Conversion](https://en.wikipedia.org/wiki/HSL_and_HSV)
-   [BeE Block IDE Online](https://beestemsolutions.com.vn/studio/bee-ide)
