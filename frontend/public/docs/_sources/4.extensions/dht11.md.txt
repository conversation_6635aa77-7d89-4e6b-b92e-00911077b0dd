# Module DHT11

![DHT11 Module Cover](_static/bee-board-v2/modules/dht11-cover.jpg)

---

## Giới thiệu

**Module DHT11** là cảm biến đo **nhiệt độ và độ ẩm** kỹ thuật số,
phổ biến trong các dự án IoT và STEM.
Trên **BeE Board V2**, module này có thể kết nối dễ dàng qua các cổng **PORT1–PORT6**,
và được hỗ trợ sẵn trong thư viện **BeeBrain**.

> 💡 DHT11 phù hợp cho các dự án:
>
> -   Hiển thị nhiệt độ phòng trên OLED
> -   Cảnh báo nóng/lạnh bằng LED
> -   G<PERSON>i dữ liệu lên dashboard IoT

---

## Thông số kỹ thuật

| Thông số          | Gi<PERSON> trị                    |
| ----------------- | -------------------------- |
| Điện áp hoạt động | 3.3V – 5V                  |
| Dải đo nhiệt độ   | 0°C → 50°C (±2°C)          |
| Dải đo độ ẩm      | 20% → 90% RH (±5%)         |
| Tín hiệu ra       | Digital                    |
| Chu kỳ đọc        | ≥ 1 giây                   |
| Giao tiếp         | 1-wire Digital             |
| Tương thích       | PORTx (GPIO) của BeE Board |

---

## Sơ đồ kết nối

| Dây | Mô tả                            |
| --- | -------------------------------- |
| VCC | 3.3V                             |
| GND | Nối đất                          |
| SIG | Tín hiệu Digital (cắm vào PORTx) |

> Cắm module DHT11 vào **PORT1 – PORT6**
> (tuỳ cổng được cấu hình trong phần mềm).

---

## Giao diện lập trình Python

### Khởi tạo module

```python
from BeeBrain import bee
from DHT11 import DHT11

sensor = DHT11(bee.PORT1)
```

### Đọc nhiệt độ và độ ẩm

```python
temperature = sensor.temperature()
humidity = sensor.humidity()

print("Nhiệt độ:", temperature, "°C")
print("Độ ẩm:", humidity, "%")
```

### Đọc cùng lúc cả hai giá trị

```python
t, h = sensor.read()
print(f"Nhiệt độ: {t}°C | Độ ẩm: {h}%")
```

> Mỗi lần đọc nên cách nhau ít nhất **1 giây**, tránh lỗi `Checksum Error`.

---

## Lập trình với BeE IDE

![DHT11 Blocks Example](_static/bee-ide/blocks/dht11-blocks.jpg)

### Các khối lệnh phổ biến:

```
lấy nhiệt độ từ cảm biến DHT11 ở PORT1
lấy độ ẩm từ cảm biến DHT11 ở PORT1
hiển thị nhiệt độ và độ ẩm lên màn hình OLED
```

> 💡 Nhóm khối **Sensor → DHT11** cho phép hiển thị trực tiếp dữ liệu cảm biến.

---

## Ví dụ Python

### Hiển thị nhiệt độ & độ ẩm lên OLED

```python
from BeeBrain import bee
from DHT11 import DHT11
import time

sensor = DHT11(bee.PORT1)

while True:
    t, h = sensor.read()
    bee.oled.clear()
    bee.oled.write(f"Nhiệt độ: {t:.1f}°C", 0, 0)
    bee.oled.write(f"Độ ẩm: {h:.1f}%", 0, 1)
    time.sleep(1)
```

---

### Bật LED khi quá nóng

```python
from BeeBrain import bee
from DHT11 import DHT11
import time

sensor = DHT11(bee.PORT2)

while True:
    t, h = sensor.read()
    if t > 30:
        bee.led1.set_rgb(255, 0, 0)  # Đỏ – nóng
    else:
        bee.led1.set_rgb(0, 0, 255)  # Xanh – mát
    time.sleep(1)
```

---

## Giải thích mã

| Thành phần                     | Mô tả                                   |
| ------------------------------ | --------------------------------------- |
| `DHT11(PORTx)`                 | Tạo cảm biến ở cổng PORTx               |
| `read()`                       | Trả về bộ đôi `(temperature, humidity)` |
| `temperature()` / `humidity()` | Trả về từng giá trị riêng               |
| `bee.oled.write()`             | Hiển thị thông tin lên màn hình OLED    |
| `bee.led1.set_rgb()`           | Báo trạng thái bằng màu LED             |

---

## Bài tập mở rộng

1. **Cảnh báo môi trường**
   Hiển thị thông điệp “Nóng quá!” khi nhiệt độ > 32°C.
2. **Biểu đồ nhiệt độ OLED**
   Hiển thị đồ thị cột theo thời gian bằng `bee.oled.plot_bar()`.
3. **IoT Dashboard**
   Gửi dữ liệu nhiệt độ/độ ẩm lên MQTT broker và hiển thị trên web.

---

## Lỗi thường gặp

```{admonition} Dữ liệu trả về None hoặc 0
:class: warning
**Nguyên nhân:** Đọc cảm biến quá nhanh hoặc kết nối sai chân.
**Giải pháp:**
- Đảm bảo mỗi lần đọc cách nhau ≥ 1 giây
- Kiểm tra lại dây tín hiệu và cổng PORT
```

```{admonition} Giá trị nhiệt độ sai lệch
:class: warning
**Nguyên nhân:** Cảm biến đặt gần nguồn nhiệt (board, LED, motor)
**Giải pháp:**
- Đặt cảm biến xa nguồn nhiệt > 5cm
- Đợi 10–20 giây để DHT11 ổn định
```

---

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beestemsolutions.com.vn/docs/bee-board)
-   [MicroPython DHT Library](https://docs.micropython.org/en/latest/library/dht.html)
-   [ESP32 GPIO Reference](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/api-reference/peripherals/gpio.html)
-   [BeE IDE Online](https://beestemsolutions.com.vn/studio/bee-ide)
