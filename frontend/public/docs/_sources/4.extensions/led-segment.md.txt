# BeeLedSegment

## Giới thiệu

BeeLedSegment là module hiển thị LED 7 đoạn 4 chữ số sử dụng chip TM1637, hỗ trợ hiển thị số, chữ cái, ký tự đặc biệt và có thể điều chỉnh độ sáng. <PERSON><PERSON><PERSON> này lý tưởng cho việc hiển thị thời gian, nhiệt độ, điểm số và các thông tin số học.

<PERSON><PERSON><PERSON> tích hợp dấu hai chấm (:) ở giữa để hiển thị thời gian, có thể hiển thị text cuộn và hỗ trợ nhiều chế độ hiển thị khác nhau. Giao tiếp đơn giản chỉ cần 2 dây (CLK và DIO).

**Ứng dụng thực tế:**

-   <PERSON>ồng hồ số hiển thị giờ:phút
-   Hiển thị nhiệt độ môi trường
-   Bảng điểm số trò chơi
-   <PERSON>ồng hồ đếm ngược
-   <PERSON>ể<PERSON> thị thông số cảm biến
-   <PERSON><PERSON><PERSON> tính đơn giản
-   <PERSON><PERSON> thống thông báo số thứ tự

## Thông số kỹ thuật

| Thông số            | Giá trị                     |
| ------------------- | --------------------------- |
| Chip điều khiển     | TM1637                      |
| Số chữ số           | 4                           |
| Loại LED            | 7-segment + dấu hai chấm    |
| Giao tiếp           | 2-wire (CLK, DIO)           |
| Điện áp hoạt động   | 3.3V - 5V                   |
| Dòng tiêu thụ       | 80mA (max brightness)       |
| Độ sáng             | 8 mức (0-7)                 |
| Tần số quét         | 1kHz                        |
| Màu LED             | Đỏ                          |
| Kích thước hiển thị | 0.56 inch                   |
| Ký tự hỗ trợ        | 0-9, A-Z, space, dash, star |

## Giao diện lập trình

### Khởi tạo

```python
from BeeLedSegment import BeeLedSegment

# Khởi tạo với độ sáng mặc định (7)
segment = BeeLedSegment(PORT1)

# Khởi tạo với độ sáng tùy chỉnh
segment = BeeLedSegment(PORT2, brightness=3)
```

### Điều khiển độ sáng

#### `brightness(value)` - Thiết lập/đọc độ sáng

```python
# Thiết lập độ sáng
segment.brightness(5)  # Độ sáng trung bình

# Đọc độ sáng hiện tại
current_brightness = segment.brightness()
print(f"Brightness: {current_brightness}")
```

### Hiển thị số

#### `show_number(number)` - Hiển thị số nguyên

```python
# Hiển thị số dương
segment.show_number(1234)

# Hiển thị số âm
segment.show_number(-123)

# Số ngoài phạm vi sẽ được giới hạn
segment.show_number(99999)  # Hiển thị 9999
```

#### `show_numbers(num1, num2, colon)` - Hiển thị hai số với dấu hai chấm

```python
# Hiển thị giờ:phút
segment.show_numbers(14, 30, True)  # "14:30"

# Hiển thị hai số không có dấu hai chấm
segment.show_numbers(12, 34, False)  # "1234"
```

#### `show_hex(value)` - Hiển thị số hex

```python
# Hiển thị giá trị hex
segment.show_hex(0xABCD)  # "ABCD"
segment.show_hex(255)     # "00FF"
```

### Hiển thị text

#### `show(text, colon)` - Hiển thị text

```python
# Hiển thị text đơn giản
segment.show("Hi")

# Hiển thị với dấu hai chấm
segment.show("12:34", True)

# Hiển thị ký tự đặc biệt
segment.show("A-b*")  # A, dash, b, star
```

#### `scroll(text, delay)` - Cuộn text

```python
# Cuộn text dài
segment.scroll("Hello World")

# Cuộn với tốc độ tùy chỉnh
segment.scroll("BeE Board", 150)  # Cuộn nhanh hơn
```

#### `display(text)` - Hiển thị thông minh

```python
# Tự động chọn hiển thị tĩnh hoặc cuộn
segment.display("Hi")          # Hiển thị tĩnh
segment.display("Long text")   # Tự động cuộn
```

### Hiển thị nhiệt độ

#### `show_temperature(temp)` - Hiển thị nhiệt độ

```python
# Hiển thị nhiệt độ bình thường
segment.show_temperature(25)    # "25°C"

# Nhiệt độ quá thấp/cao
segment.show_temperature(-15)   # "LO°C"
segment.show_temperature(150)   # "HI°C"
```

### Điều khiển cơ bản

#### `clear()` - Xóa màn hình

```python
segment.clear()
```

## Ví dụ Blockly

```
when program starts:
    set display to BeeLedSegment at PORT1
    set brightness to 5

forever:
    show current time on display with colon
    wait 1 second

    if button A pressed:
        show temperature on display
        wait 2 seconds

    if button B pressed:
        scroll "BeE Board STEM Kit" on display
```

## Ví dụ Python

### Ví dụ cơ bản - Đồng hồ số

```python
from BeeLedSegment import BeeLedSegment
from BeeBrain import bee
import time

def setup():
    """Khởi tạo màn hình LED 7 đoạn"""
    global display

    try:
        # Khởi tạo display
        display = BeeLedSegment(bee.PORT1, brightness=5)

        # Test hiển thị
        display.show("8888")  # Test tất cả segment
        time.sleep(1)
        display.clear()

        # Hiển thị thông báo khởi động
        display.scroll("Clock Ready")

        bee.oled.clear()
        bee.oled.write("Digital Clock", 10, 10)
        bee.oled.write("7-Segment", 15, 25)
        bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)

        return True

    except Exception as e:
        bee.oled.clear()
        bee.oled.write("Display Error!", 5, 10)
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
        return False

def digital_clock():
    """Đồng hồ số với LED 7 đoạn"""
    colon_state = True
    last_second = -1

    while True:
        # Lấy thời gian hiện tại
        current_time = time.localtime()
        hour = current_time[3]
        minute = current_time[4]
        second = current_time[5]

        # Cập nhật mỗi giây
        if second != last_second:
            last_second = second

            # Hiển thị giờ:phút
            display.show_numbers(hour, minute, colon_state)

            # Nhấp nháy dấu hai chấm mỗi giây
            colon_state = not colon_state

            # Hiển thị thông tin trên OLED
            bee.oled.clear()
            bee.oled.write("Digital Clock", 10, 0)
            bee.oled.write(f"{hour:02d}:{minute:02d}:{second:02d}", 15, 20)
            bee.oled.write("A: Brightness", 5, 35)
            bee.oled.write("B: Temperature", 5, 50)

        # Kiểm tra nút điều chỉnh độ sáng
        if bee.is_button_pressed(bee.BUTTON_A):
            adjust_brightness()

        # Kiểm tra nút hiển thị nhiệt độ
        if bee.is_button_pressed(bee.BUTTON_B):
            show_temperature_demo()

        time.sleep(0.1)

def adjust_brightness():
    """Điều chỉnh độ sáng"""
    current_brightness = display.brightness()
    new_brightness = (current_brightness + 1) % 8

    display.brightness(new_brightness)

    # Hiển thị mức độ sáng
    display.show_number(new_brightness)

    bee.oled.clear()
    bee.oled.write("Brightness", 15, 10)
    bee.oled.write(f"Level: {new_brightness}", 20, 25)

    # Hiệu ứng LED
    intensity = int(new_brightness * 255 / 7)
    bee.neopixel.set_rgb(bee.LED1, intensity, intensity, 0)

    time.sleep(1)
    bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)

def show_temperature_demo():
    """Demo hiển thị nhiệt độ"""
    # Giả lập dữ liệu nhiệt độ
    temperatures = [25, 30, -5, 100, 15, 22]

    for temp in temperatures:
        display.show_temperature(temp)

        bee.oled.clear()
        bee.oled.write("Temperature", 15, 10)
        bee.oled.write(f"{temp}°C", 30, 25)

        if temp < 0:
            bee.neopixel.set_rgb(bee.LED1, 0, 0, 255)  # Xanh dương = lạnh
        elif temp > 35:
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)  # Đỏ = nóng
        else:
            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)  # Xanh = bình thường

        time.sleep(1.5)

    bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)

# Chạy chương trình
if setup():
    digital_clock()
```

### Ví dụ nâng cao - Hệ thống hiển thị đa chức năng

```python
from BeeLedSegment import BeeLedSegment
from BeeBrain import bee
import time
import random

class MultiDisplaySystem:
    def __init__(self):
        self.display = BeeLedSegment(bee.PORT1, brightness=6)
        self.current_mode = 0
        self.modes = [
            "clock", "counter", "timer", "game", "sensor", "calculator"
        ]
        self.mode_names = [
            "Clock", "Counter", "Timer", "Game", "Sensor", "Calc"
        ]

        # Biến trạng thái cho các chế độ
        self.counter_value = 0
        self.timer_seconds = 0
        self.timer_running = False
        self.game_score = 0
        self.calculator_value = 0
        self.calculator_operation = None

    def setup(self):
        """Khởi tạo hệ thống"""
        try:
            # Test display
            self.display.scroll("Multi Display System")

            bee.oled.clear()
            bee.oled.write("Multi Display", 10, 0)
            bee.oled.write("System Ready", 15, 15)
            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)

            time.sleep(2)
            return True

        except Exception as e:
            bee.oled.clear()
            bee.oled.write("System Error!", 10, 10)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
            return False

    def switch_mode(self):
        """Chuyển đổi chế độ hiển thị"""
        self.current_mode = (self.current_mode + 1) % len(self.modes)
        mode_name = self.mode_names[self.current_mode]

        # Hiển thị tên chế độ
        self.display.scroll(mode_name)

        bee.oled.clear()
        bee.oled.write("Mode:", 0, 0)
        bee.oled.write(mode_name, 0, 15)

        # LED màu khác nhau cho mỗi chế độ
        colors = [
            (0, 255, 0),    # Clock - Xanh
            (255, 255, 0),  # Counter - Vàng
            (255, 0, 0),    # Timer - Đỏ
            (255, 0, 255),  # Game - Tím
            (0, 255, 255),  # Sensor - Cyan
            (255, 165, 0)   # Calculator - Cam
        ]
        bee.neopixel.set_rgb(bee.LED1, *colors[self.current_mode])

        time.sleep(1)

    def mode_clock(self):
        """Chế độ đồng hồ"""
        current_time = time.localtime()
        hour = current_time[3]
        minute = current_time[4]
        second = current_time[5]

        # Nhấp nháy dấu hai chấm
        colon = (second % 2) == 0
        self.display.show_numbers(hour, minute, colon)

        bee.oled.clear()
        bee.oled.write("CLOCK MODE", 15, 0)
        bee.oled.write(f"{hour:02d}:{minute:02d}:{second:02d}", 15, 20)
        bee.oled.write("A: Mode  B: Bright", 0, 40)

    def mode_counter(self):
        """Chế độ đếm số"""
        self.display.show_number(self.counter_value)

        bee.oled.clear()
        bee.oled.write("COUNTER MODE", 10, 0)
        bee.oled.write(f"Value: {self.counter_value}", 15, 20)
        bee.oled.write("A: Mode  B: +1", 5, 40)

        # Nút B để tăng counter
        if bee.is_button_pressed(bee.BUTTON_B):
            self.counter_value = (self.counter_value + 1) % 10000
            time.sleep(0.2)  # Debounce

    def mode_timer(self):
        """Chế độ đếm ngược"""
        if self.timer_running and self.timer_seconds > 0:
            self.timer_seconds -= 1
            if self.timer_seconds == 0:
                # Hết giờ
                self.timer_running = False
                bee.buzzer.play_song("A4:0.2 SIL:0.1 A4:0.2 SIL:0.1 A4:0.2")
                for _ in range(5):
                    bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
                    time.sleep(0.2)
                    bee.neopixel.set_rgb(bee.LED1, 0, 0, 0)
                    time.sleep(0.2)

        # Hiển thị phút:giây
        minutes = self.timer_seconds // 60
        seconds = self.timer_seconds % 60
        self.display.show_numbers(minutes, seconds, True)

        bee.oled.clear()
        bee.oled.write("TIMER MODE", 15, 0)
        bee.oled.write(f"{minutes:02d}:{seconds:02d}", 25, 20)
        status = "Running" if self.timer_running else "Stopped"
        bee.oled.write(f"Status: {status}", 5, 35)
        bee.oled.write("A: Mode  B: Start/Stop", 0, 50)

        # Nút B để start/stop timer
        if bee.is_button_pressed(bee.BUTTON_B):
            if not self.timer_running:
                if self.timer_seconds == 0:
                    self.timer_seconds = 60  # Mặc định 1 phút
                self.timer_running = True
            else:
                self.timer_running = False
            time.sleep(0.3)

    def mode_game(self):
        """Chế độ trò chơi đơn giản"""
        self.display.show_number(self.game_score)

        bee.oled.clear()
        bee.oled.write("GAME MODE", 20, 0)
        bee.oled.write(f"Score: {self.game_score}", 15, 20)
        bee.oled.write("A: Mode  B: Play", 5, 40)

        # Nút B để chơi (random điểm)
        if bee.is_button_pressed(bee.BUTTON_B):
            points = random.randint(1, 100)
            self.game_score += points

            # Hiệu ứng
            self.display.scroll(f"+{points}")
            bee.buzzer.play_song("C4 E4 G4")

            time.sleep(1)

    def mode_sensor(self):
        """Chế độ hiển thị cảm biến"""
        # Giả lập đọc cảm biến (có thể thay bằng cảm biến thật)
        try:
            # Đọc analog từ PORT2
            sensor_value = bee.analog_read(bee.PORT2, bee.PERCENT)
            self.display.show_number(int(sensor_value))

            bee.oled.clear()
            bee.oled.write("SENSOR MODE", 15, 0)
            bee.oled.write(f"Value: {sensor_value:.1f}%", 10, 20)
            bee.oled.write("A: Mode  B: Reset", 5, 40)

        except:
            # Không có cảm biến - hiển thị giá trị giả
            fake_value = random.randint(0, 100)
            self.display.show_number(fake_value)

            bee.oled.clear()
            bee.oled.write("SENSOR MODE", 15, 0)
            bee.oled.write(f"Demo: {fake_value}", 15, 20)
            bee.oled.write("A: Mode  B: Refresh", 0, 40)

        # Nút B để refresh
        if bee.is_button_pressed(bee.BUTTON_B):
            time.sleep(0.2)

    def mode_calculator(self):
        """Chế độ máy tính đơn giản"""
        self.display.show_number(self.calculator_value)

        bee.oled.clear()
        bee.oled.write("CALC MODE", 20, 0)
        bee.oled.write(f"Value: {self.calculator_value}", 10, 20)
        bee.oled.write("A: Mode  B: +10", 5, 40)

        # Nút B để cộng 10
        if bee.is_button_pressed(bee.BUTTON_B):
            self.calculator_value = (self.calculator_value + 10) % 10000
            time.sleep(0.2)

    def adjust_brightness(self):
        """Điều chỉnh độ sáng"""
        current = self.display.brightness()
        new_brightness = (current + 1) % 8
        self.display.brightness(new_brightness)

        # Hiển thị mức độ sáng
        self.display.show(f"br{new_brightness}")
        time.sleep(0.5)

    def run(self):
        """Vòng lặp chính"""
        if not self.setup():
            return

        last_update = 0

        while True:
            try:
                current_time = time.time()

                # Cập nhật hiển thị mỗi giây (trừ timer)
                if (current_time - last_update) >= 1 or self.modes[self.current_mode] == "timer":
                    last_update = current_time

                    # Gọi hàm tương ứng với chế độ hiện tại
                    mode_function = getattr(self, f"mode_{self.modes[self.current_mode]}")
                    mode_function()

                # Kiểm tra nút chuyển chế độ
                if bee.is_button_pressed(bee.BUTTON_A):
                    self.switch_mode()
                    time.sleep(0.3)  # Debounce

                time.sleep(0.1)

            except KeyboardInterrupt:
                self.display.clear()
                bee.oled.clear()
                bee.oled.write("System", 25, 10)
                bee.oled.write("Stopped", 20, 25)
                break

# Chạy hệ thống đa chức năng
if __name__ == "__main__":
    system = MultiDisplaySystem()
    system.run()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Digital clock**: Đồng hồ số với dấu hai chấm nhấp nháy
2. **Brightness control**: Điều chỉnh độ sáng bằng nút bấm
3. **Temperature display**: Demo hiển thị nhiệt độ với ký tự đặc biệt
4. **Visual feedback**: Kết hợp OLED và LED RGB

### Ví dụ nâng cao:

1. **Multi-mode system**: Hệ thống đa chức năng với 6 chế độ
2. **State management**: Quản lý trạng thái cho từng chế độ
3. **Interactive features**: Tương tác với nút bấm cho mỗi chế độ
4. **Dynamic display**: Hiển thị thông minh tùy theo nội dung

## Bài tập mở rộng

1. **Stopwatch**: Tạo đồng hồ bấm giờ với chức năng lap time
2. **Score board**: Bảng điểm cho trò chơi với nhiều người chơi
3. **Environmental monitor**: Hiển thị nhiệt độ, độ ẩm từ cảm biến thật

## Lỗi thường gặp

```{admonition} Lỗi: Một số segment không sáng
:class: warning

**Nguyên nhân**: Kết nối lỏng hoặc chip TM1637 bị lỗi

**Giải pháp**:
- Kiểm tra kết nối CLK và DIO
- Đảm bảo nguồn điện ổn định
- Thử giảm độ sáng
- Kiểm tra module có bị hỏng không
```

```{admonition} Lỗi: Hiển thị nhấp nháy hoặc không ổn định
:class: warning

**Nguyên nhân**: Nhiễu điện hoặc tần số cập nhật quá cao

**Giải pháp**:
- Thêm delay giữa các lần cập nhật
- Kiểm tra nguồn điện có ổn định
- Tránh cập nhật quá nhanh (<10Hz)
- Sử dụng dây kết nối ngắn và chất lượng
```

```{admonition} Lỗi: Ký tự hiển thị sai
:class: warning

**Nguyên nhân**: Ký tự không được hỗ trợ hoặc encoding sai

**Giải pháp**:
- Chỉ sử dụng ký tự 0-9, A-Z, space, dash, star
- Kiểm tra bảng mã ký tự trong datasheet
- Sử dụng hàm encode_char() để test
- Tránh ký tự Unicode hoặc đặc biệt
```

## Tài nguyên tham khảo

-   [TM1637 Datasheet](https://www.titanmec.com/index.php/en/project/download/id/302.html)
-   [7-Segment Display Guide](https://beestemsolutions.com.vn/docs/bee-board/tutorials/7segment-display)
-   [ASCII to 7-Segment Converter](https://www.electronics-tutorials.ws/blog/7-segment-display-tutorial.html)
-   [BeE Block IDE Online](https://beestemsolutions.com.vn/studio/bee-ide)
