# BeeLineDetect

## Giới thiệu

BeeLineDetect là module cảm biến hồng ngoại chuyên dụng cho việc dò đường và theo dõi vạch kẻ. Mo<PERSON><PERSON> sử dụng chip PCF8574 để giao tiếp I2C và tích hợp 4 cảm biến hồng ngoại (IR1-IR4) được sắp xếp theo hàng ngang.

<PERSON><PERSON><PERSON> n<PERSON><PERSON> đư<PERSON>c thiết kế đặc biệt cho các ứng dụng robot theo đường, có thể phát hiện vạch đen trên nền trắng hoặc ngược lại. Với 4 cảm biến được bố tr<PERSON> hợ<PERSON> lý, robot có thể xác định chính xác vị trí đường và điều chỉnh hướng di chuyển.

**Ứng dụng thực tế:**

-   Robot theo đường tự động
-   Xe AGV (Automated Guided Vehicle)
-   Robot dọn dẹp theo lộ trình
-   Hệ thống vận chuyển tự động
-   Cuộc thi robot sumo/line following
-   Dự án giáo dục STEM về automation

## Thông số kỹ thuật

| Thông số              | Giá trị                |
| --------------------- | ---------------------- |
| Chip điều khiển       | PCF8574                |
| Giao tiếp             | I2C                    |
| Địa chỉ I2C mặc định  | 0x23                   |
| Số lượng cảm biến     | 4 (IR1, IR2, IR3, IR4) |
| Khoảng cách phát hiện | 2-10mm                 |
| Điện áp hoạt động     | 3.3V - 5V              |
| Dòng tiêu thụ         | <20mA                  |
| Tần số hoạt động      | 38kHz (IR)             |
| Góc phát hiện         | ±15°                   |
| Thời gian phản hồi    | <1ms                   |

## Giao diện lập trình

### Khởi tạo

```python
from BeeLineDetect import BeeLineDetect

# Khởi tạo với địa chỉ mặc định
sensor = BeeLineDetect(PORT1)

# Khởi tạo với địa chỉ tùy chỉnh
sensor = BeeLineDetect(PORT2, address=0x24)
```

### Đọc cảm biến đơn lẻ

#### `pin(pin_number)` - Đọc trạng thái một cảm biến

```python
# Đọc cảm biến IR1 (trái nhất)
left_sensor = sensor.pin(sensor.IR1)

# Đọc cảm biến IR4 (phải nhất)
right_sensor = sensor.pin(sensor.IR4)

# Kiểm tra phát hiện đường
if sensor.pin(sensor.IR2):
    print("Phát hiện đường ở cảm biến trái-giữa")
```

### Đọc tất cả cảm biến

#### `read_all()` - Đọc trạng thái tất cả cảm biến

```python
# Đọc tất cả 4 cảm biến
sensors = sensor.read_all()
print(f"IR1: {sensors[0]}, IR2: {sensors[1]}, IR3: {sensors[2]}, IR4: {sensors[3]}")

# Sử dụng unpacking
ir1, ir2, ir3, ir4 = sensor.read_all()
```

### Kiểm tra kết nối

#### `check()` - Kiểm tra module có hoạt động không

```python
try:
    if sensor.check():
        print("Module line detect sẵn sàng")
except OSError:
    print("Không tìm thấy module line detect")
```

### Thuộc tính port

#### `port` - Đọc trạng thái port (4 bit cao)

```python
# Đọc trạng thái port
port_state = sensor.port
print(f"Port state: {port_state:04b}")
```

## Ví dụ Blockly

```
when program starts:
    set line_sensor to BeeLineDetect at PORT1

forever:
    set sensors to read all line sensors

    if IR1 and IR2 detect line:
        turn left
    else if IR3 and IR4 detect line:
        turn right
    else if IR2 and IR3 detect line:
        move forward
    else:
        stop and search for line
```

## Ví dụ Python

### Ví dụ cơ bản - Theo đường đơn giản

```python
from BeeLineDetect import BeeLineDetect
from BeeBrain import bee
import time

def setup():
    """Khởi tạo cảm biến và robot"""
    global line_sensor

    # Khởi tạo cảm biến dò đường
    line_sensor = BeeLineDetect(bee.PORT1)

    # Kiểm tra kết nối
    try:
        line_sensor.check()
        bee.oled.clear()
        bee.oled.write("Line Sensor OK", 5, 10)
        bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)  # LED xanh
        bee.buzzer.play_song("C4 E4 G4")
    except OSError:
        bee.oled.clear()
        bee.oled.write("Sensor Error!", 10, 10)
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)  # LED đỏ
        return False

    time.sleep(1)
    return True

def follow_line():
    """Thuật toán theo đường cơ bản"""
    base_speed = 30
    turn_speed = 25

    while True:
        # Đọc tất cả cảm biến
        ir1, ir2, ir3, ir4 = line_sensor.read_all()

        # Hiển thị trạng thái cảm biến trên OLED
        bee.oled.clear()
        bee.oled.write(f"IR: {ir1}{ir2}{ir3}{ir4}", 20, 0)

        # Logic theo đường
        if ir2 and ir3:
            # Đường thẳng - di chuyển tiến
            bee.oled.write("FORWARD", 25, 15)
            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)  # Xanh
            bee.move_forward(base_speed)

        elif ir1 or ir2:
            # Đường lệch trái - rẽ trái
            bee.oled.write("TURN LEFT", 15, 15)
            bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)  # Vàng
            bee.turn_left(turn_speed)

        elif ir3 or ir4:
            # Đường lệch phải - rẽ phải
            bee.oled.write("TURN RIGHT", 10, 15)
            bee.neopixel.set_rgb(bee.LED1, 0, 255, 255)  # Cyan
            bee.turn_right(turn_speed)

        elif ir1 and ir2 and ir3 and ir4:
            # Giao lộ hoặc kết thúc - dừng
            bee.oled.write("INTERSECTION", 5, 15)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 255)  # Tím
            bee.stop_robot()
            bee.buzzer.play_song("G4 E4 C4")
            time.sleep(1)

        else:
            # Mất đường - tìm kiếm
            bee.oled.write("SEARCHING", 15, 15)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)  # Đỏ
            bee.stop_robot()
            search_line()

        # Hiển thị tốc độ
        bee.oled.write(f"Speed: {base_speed}", 15, 30)

        # Kiểm tra nút dừng
        if bee.is_button_pressed(bee.BUTTON_B):
            bee.stop_robot()
            bee.oled.clear()
            bee.oled.write("STOPPED", 25, 20)
            break

        time.sleep(0.05)  # 20Hz update rate

def search_line():
    """Tìm kiếm đường khi bị mất"""
    search_time = 0
    max_search_time = 2  # 2 giây

    while search_time < max_search_time:
        # Quay trái tìm đường
        bee.turn_left(20)
        time.sleep(0.1)

        # Kiểm tra có tìm thấy đường không
        sensors = line_sensor.read_all()
        if any(sensors):
            return  # Tìm thấy đường, thoát

        search_time += 0.1

    # Không tìm thấy đường - dừng hẳn
    bee.stop_robot()
    bee.buzzer.play_song("A4:0.2 SIL:0.1 A4:0.2 SIL:0.1 A4:0.2")

# Chạy chương trình
if setup():
    follow_line()
```

### Ví dụ nâng cao - Robot theo đường thông minh với PID

```python
from BeeLineDetect import BeeLineDetect
from BeeBrain import bee
import time

class SmartLineFollower:
    def __init__(self):
        self.line_sensor = BeeLineDetect(bee.PORT1)
        self.base_speed = 35
        self.max_speed = 50
        self.min_speed = 15

        # PID parameters
        self.kp = 1.5  # Proportional gain
        self.ki = 0.1  # Integral gain
        self.kd = 0.8  # Derivative gain

        self.last_error = 0
        self.integral = 0
        self.last_position = 0

        # Statistics
        self.total_distance = 0
        self.start_time = time.time()
        self.intersections = 0

    def setup(self):
        """Khởi tạo và kiểm tra hệ thống"""
        try:
            self.line_sensor.check()
            bee.oled.clear()
            bee.oled.write("Smart Line", 15, 0)
            bee.oled.write("Follower Ready", 5, 15)
            bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)
            bee.buzzer.play_song("C4 E4 G4 C5")
            time.sleep(2)
            return True
        except OSError:
            bee.oled.clear()
            bee.oled.write("Sensor Error!", 10, 20)
            bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)
            return False

    def read_line_position(self):
        """Tính toán vị trí đường (weighted average)"""
        sensors = self.line_sensor.read_all()

        # Trọng số cho từng cảm biến (trái -> phải: -3, -1, 1, 3)
        weights = [-3, -1, 1, 3]
        weighted_sum = 0
        total_sensors = 0

        for i, sensor_value in enumerate(sensors):
            if sensor_value:
                weighted_sum += weights[i]
                total_sensors += 1

        if total_sensors == 0:
            return None  # Không phát hiện đường

        # Vị trí từ -3 (trái) đến 3 (phải), 0 là giữa
        position = weighted_sum / total_sensors
        return position

    def calculate_pid(self, position):
        """Tính toán điều khiển PID"""
        if position is None:
            return 0

        # Error = vị trí mong muốn (0) - vị trí hiện tại
        error = 0 - position

        # Proportional term
        proportional = self.kp * error

        # Integral term
        self.integral += error
        integral = self.ki * self.integral

        # Derivative term
        derivative = self.kd * (error - self.last_error)

        # PID output
        pid_output = proportional + integral + derivative

        self.last_error = error
        return pid_output

    def motor_control(self, pid_output):
        """Điều khiển motor dựa trên PID output"""
        # Tính tốc độ cho từng motor
        left_speed = self.base_speed + pid_output
        right_speed = self.base_speed - pid_output

        # Giới hạn tốc độ
        left_speed = max(self.min_speed, min(self.max_speed, left_speed))
        right_speed = max(self.min_speed, min(self.max_speed, right_speed))

        # Điều khiển motor (giả sử có hàm điều khiển từng motor)
        if hasattr(bee, 'dcmotor'):
            bee.dcmotor.speed(0, int(left_speed))   # Motor trái
            bee.dcmotor.speed(1, int(right_speed))  # Motor phải
        else:
            # Fallback: sử dụng hàm di chuyển cơ bản
            if abs(pid_output) < 0.5:
                bee.move_forward(self.base_speed)
            elif pid_output > 0:
                bee.turn_left(int(abs(pid_output) * 10))
            else:
                bee.turn_right(int(abs(pid_output) * 10))

    def detect_intersection(self):
        """Phát hiện giao lộ"""
        sensors = self.line_sensor.read_all()
        active_sensors = sum(sensors)

        # Giao lộ khi có 3 hoặc 4 cảm biến phát hiện đường
        if active_sensors >= 3:
            return True
        return False

    def handle_intersection(self):
        """Xử lý giao lộ"""
        self.intersections += 1

        bee.oled.clear()
        bee.oled.write(f"Intersection #{self.intersections}", 0, 0)
        bee.neopixel.set_rgb(bee.LED1, 255, 0, 255)  # Tím
        bee.buzzer.play_song("C5:0.1 E5:0.1 G5:0.1")

        # Dừng một chút
        bee.stop_robot()
        time.sleep(0.5)

        # Tiếp tục đi thẳng qua giao lộ
        bee.move_forward(self.base_speed, 0.3)
        time.sleep(0.3)

    def update_display(self, position, pid_output):
        """Cập nhật thông tin hiển thị"""
        runtime = time.time() - self.start_time

        bee.oled.clear()
        bee.oled.write(f"Pos: {position:.1f}" if position else "Lost", 0, 0)
        bee.oled.write(f"PID: {pid_output:.1f}", 0, 12)
        bee.oled.write(f"Speed: {self.base_speed}", 0, 24)
        bee.oled.write(f"Time: {runtime:.0f}s", 0, 36)
        bee.oled.write(f"Cross: {self.intersections}", 0, 48)

    def run(self):
        """Vòng lặp chính"""
        if not self.setup():
            return

        lost_count = 0
        max_lost_count = 10  # Số lần đọc liên tiếp không thấy đường

        while True:
            try:
                # Đọc vị trí đường
                position = self.read_line_position()

                if position is not None:
                    # Tìm thấy đường
                    lost_count = 0
                    self.last_position = position

                    # Kiểm tra giao lộ
                    if self.detect_intersection():
                        self.handle_intersection()
                        continue

                    # Tính PID và điều khiển
                    pid_output = self.calculate_pid(position)
                    self.motor_control(pid_output)

                    # LED xanh = bình thường
                    bee.neopixel.set_rgb(bee.LED1, 0, 255, 0)

                    # Cập nhật hiển thị
                    self.update_display(position, pid_output)

                else:
                    # Mất đường
                    lost_count += 1

                    if lost_count < max_lost_count:
                        # Tiếp tục với PID dựa trên vị trí cuối
                        pid_output = self.calculate_pid(self.last_position)
                        self.motor_control(pid_output)
                        bee.neopixel.set_rgb(bee.LED1, 255, 255, 0)  # Vàng
                    else:
                        # Mất đường hoàn toàn - dừng
                        bee.stop_robot()
                        bee.neopixel.set_rgb(bee.LED1, 255, 0, 0)  # Đỏ
                        bee.buzzer.play_song("A4:0.2 SIL:0.1 A4:0.2")

                        bee.oled.clear()
                        bee.oled.write("LINE LOST!", 15, 10)
                        bee.oled.write("Press A to retry", 0, 25)

                        # Chờ nút A để thử lại
                        while not bee.is_button_pressed(bee.BUTTON_A):
                            time.sleep(0.1)

                        lost_count = 0
                        self.integral = 0  # Reset integral

                # Kiểm tra nút dừng
                if bee.is_button_pressed(bee.BUTTON_B):
                    break

                time.sleep(0.02)  # 50Hz control loop

            except KeyboardInterrupt:
                break

        # Dừng robot và hiển thị thống kê
        bee.stop_robot()
        runtime = time.time() - self.start_time

        bee.oled.clear()
        bee.oled.write("FINISHED!", 20, 0)
        bee.oled.write(f"Time: {runtime:.1f}s", 15, 15)
        bee.oled.write(f"Intersections: {self.intersections}", 0, 30)
        bee.neopixel.set_rgb(bee.LED1, 0, 0, 255)  # Xanh dương
        bee.buzzer.play_song("C4 E4 G4 C5 G4 E4 C4")

# Chạy robot thông minh
if __name__ == "__main__":
    robot = SmartLineFollower()
    robot.run()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Sensor reading**: Đọc 4 cảm biến và phân tích pattern
2. **Simple logic**: Logic if-else đơn giản cho các trường hợp
3. **Visual feedback**: Hiển thị trạng thái trên OLED và LED
4. **Error handling**: Xử lý trường hợp mất đường

### Ví dụ nâng cao:

1. **PID control**: Điều khiển PID cho chuyển động mượt mà
2. **Position calculation**: Tính toán vị trí đường bằng weighted average
3. **Intersection detection**: Phát hiện và xử lý giao lộ
4. **Statistics tracking**: Theo dõi thống kê hiệu suất

## Bài tập mở rộng

1. **Robot maze solver**: Kết hợp line following với thuật toán giải mê cung
2. **Multi-path navigator**: Robot có thể chọn đường đi tối ưu tại giao lộ
3. **Speed optimization**: Tự động điều chỉnh tốc độ dựa trên độ cong của đường

## Lỗi thường gặp

```{admonition} Lỗi: Cảm biến không phát hiện đường
:class: warning

**Nguyên nhân**: Khoảng cách không phù hợp hoặc độ tương phản thấp

**Giải pháp**:
- Điều chỉnh khoảng cách 2-8mm từ mặt đất
- Đảm bảo đường có độ tương phản cao (đen/trắng)
- Kiểm tra ánh sáng môi trường (tránh ánh sáng mạnh)
- Làm sạch cảm biến IR
```

```{admonition} Lỗi: Robot dao động khi theo đường
:class: warning

**Nguyên nhân**: Tham số PID không phù hợp hoặc tốc độ quá cao

**Giải pháp**:
- Giảm tốc độ cơ bản
- Điều chỉnh tham số Kp (giảm nếu dao động)
- Tăng tham số Kd để giảm overshoot
- Kiểm tra tần số đọc cảm biến (20-50Hz)
```

```{admonition} Lỗi: Không giao tiếp được I2C
:class: warning

**Nguyên nhân**: Địa chỉ I2C sai hoặc kết nối lỏng

**Giải pháp**:
- Kiểm tra địa chỉ I2C (mặc định 0x23)
- Đảm bảo kết nối SDA, SCL chắc chắn
- Thử scan I2C để tìm địa chỉ đúng
- Kiểm tra pull-up resistor (4.7kΩ)
```

## Tài nguyên tham khảo

-   [PCF8574 Datasheet](https://www.ti.com/lit/ds/symlink/pcf8574.pdf)
-   [Line Following Robot Tutorial](https://beestemsolutions.com.vn/docs/bee-board/tutorials/line-following)
-   [PID Control Theory](https://en.wikipedia.org/wiki/PID_controller)
-   [BeE Block IDE Online](https://beestemsolutions.com.vn/studio/bee-ide)
