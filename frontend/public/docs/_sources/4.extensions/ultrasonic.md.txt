# BeeUltrasonic

## Giới thiệu

Module BeeUltrasonic cung cấp giao diện điều khiển cảm biến siêu âm HC-SR04 cho BeE Board. Cảm biến sử dụng sóng siêu âm để đo khoảng cách chính xác từ 2cm đến 4m, hoạt động dựa trên nguyên lý phản xạ sóng âm.

Cảm biến siêu âm là một trong những cảm biến phổ biến nhất trong robotics và IoT, cho phép robot "nhìn thấy" môi trường xung quanh và tránh vật cản. Công nghệ này tương tự như hệ thống định vị của dơi và cá heo.

**Ứng dụng thực tế:**

-   Robot tránh vật cản
-   <PERSON><PERSON> thống đỗ xe tự động
-   <PERSON><PERSON> mứ<PERSON> nước trong bể
-   <PERSON><PERSON><PERSON> báo xâm nhập
-   <PERSON><PERSON> cách trong công nghiệp
-   <PERSON><PERSON> thống điều khiển thông minh

## Thông số kỹ thuật

| Thông số           | Giá trị               |
| ------------------ | --------------------- |
| Loại cảm biến      | HC-SR04 Ultrasonic    |
| Dải đo             | 20mm - 4000mm         |
| Độ chính xác       | ±3mm                  |
| Góc đo             | 15° (cone)            |
| Tần số siêu âm     | 40kHz                 |
| Điện áp hoạt động  | 5V (3.3V tương thích) |
| Dòng tiêu thụ      | 15mA                  |
| Thời gian đo       | 38ms (tối đa)         |
| Nhiệt độ hoạt động | -15°C đến +70°C       |

## Giao diện lập trình

### Khởi tạo

```python
from BeeUltrasonic import BeeUltrasonic

# Khởi tạo cảm biến với PORT1
# Pin 1: Trigger, Pin 2: Echo
sensor = BeeUltrasonic(PORT1)
```

### Các phương thức chính

#### `distance_mm()` - Đo khoảng cách (mm)

```python
# Đo khoảng cách tính bằng milimeter
distance = sensor.distance_mm()
if distance != -1:
    print(f"Khoảng cách: {distance}mm")
else:
    print("Không phát hiện vật thể")
```

#### `distance_cm()` - Đo khoảng cách (cm)

```python
# Đo khoảng cách tính bằng centimeter
distance = sensor.distance_cm()
if distance != -1.0:
    print(f"Khoảng cách: {distance:.1f}cm")
```

#### `is_object_present(threshold)` - Phát hiện vật thể

```python
# Kiểm tra có vật thể trong phạm vi 30cm
if sensor.is_object_present(300):  # 300mm = 30cm
    print("Phát hiện vật thể gần!")
```

## Ví dụ Blockly

```
repeat forever:
    set distance to ultrasonic sensor distance in cm
    if distance < 20:
        set LED to RED
        play sound "beep"
    else if distance < 50:
        set LED to YELLOW
    else:
        set LED to GREEN
    wait 0.5 seconds
```

## Ví dụ Python

### Ví dụ cơ bản - Đo khoảng cách đơn giản

```python
from BeeUltrasonic import BeeUltrasonic
from BeeOled import BeeOled
import time

# Khởi tạo
sensor = BeeUltrasonic(PORT1)
oled = BeeOled(PORT2)

def display_distance():
    """Hiển thị khoảng cách trên OLED"""
    while True:
        distance_cm = sensor.distance_cm()

        oled.clear()
        oled.write("Ultrasonic Sensor", 5, 0)

        if distance_cm != -1.0:
            oled.write(f"Distance:", 0, 20)
            oled.write(f"{distance_cm:.1f} cm", 0, 35)

            # Vẽ thanh biểu diễn khoảng cách
            bar_width = int(min(distance_cm * 2, 120))
            oled.draw_rect(4, 50, bar_width, 8, 1)
        else:
            oled.write("Out of range", 20, 30)

        oled.show()
        time.sleep(0.2)

# Chạy chương trình
display_distance()
```

### Ví dụ nâng cao - Robot tránh vật cản

```python
from BeeUltrasonic import BeeUltrasonic
from BeeMotor import BeeDCMotors
from BeeNeopixel import BeeNeopixel
from BeeBuzzer import BeeMusic
import time
import random

# Khởi tạo các module
sensor = BeeUltrasonic(PORT1)
motors = BeeDCMotors(PORT2)
leds = BeeNeopixel(pin=5, number_of_led=2)
buzzer = BeeMusic(pin=15, volume=50)

# Thông số điều khiển
SAFE_DISTANCE = 30  # cm
WARNING_DISTANCE = 15  # cm
DANGER_DISTANCE = 10  # cm

class ObstacleAvoidanceRobot:
    def __init__(self):
        self.state = "FORWARD"
        self.turn_direction = "RIGHT"

    def set_led_color(self, color):
        """Đặt màu LED theo trạng thái"""
        colors = {
            "GREEN": (0, 255, 0),
            "YELLOW": (255, 255, 0),
            "RED": (255, 0, 0),
            "BLUE": (0, 0, 255)
        }

        if color in colors:
            r, g, b = colors[color]
            leds.set_rgb(0, r, g, b)
            leds.set_rgb(1, r, g, b)

    def move_forward(self, speed=30):
        """Di chuyển tiến"""
        motors.speed(0, speed)
        motors.speed(1, speed)
        self.set_led_color("GREEN")

    def move_backward(self, speed=25):
        """Di chuyển lùi"""
        motors.speed(0, -speed)
        motors.speed(1, -speed)
        self.set_led_color("BLUE")

    def turn_right(self, speed=25):
        """Rẽ phải"""
        motors.speed(0, speed)
        motors.speed(1, -speed)
        self.set_led_color("YELLOW")

    def turn_left(self, speed=25):
        """Rẽ trái"""
        motors.speed(0, -speed)
        motors.speed(1, speed)
        self.set_led_color("YELLOW")

    def stop(self):
        """Dừng robot"""
        motors.brake(0)
        motors.brake(1)
        self.set_led_color("RED")

    def scan_environment(self):
        """Quét môi trường và quyết định hành động"""
        distance = sensor.distance_cm()

        if distance == -1.0:
            # Không đo được - tiếp tục di chuyển
            return SAFE_DISTANCE + 1

        return distance

    def avoid_obstacle(self):
        """Logic tránh vật cản chính"""
        distance = self.scan_environment()

        if distance > SAFE_DISTANCE:
            # An toàn - di chuyển tiến
            self.move_forward()
            self.state = "FORWARD"

        elif distance > WARNING_DISTANCE:
            # Cảnh báo - giảm tốc độ
            self.move_forward(speed=20)
            self.set_led_color("YELLOW")
            buzzer.play_tone(440)  # Beep cảnh báo
            time.sleep(0.1)
            buzzer.be_quiet()

        elif distance > DANGER_DISTANCE:
            # Nguy hiểm - chuẩn bị rẽ
            self.stop()
            buzzer.play_song("A5:0.2 SIL:0.1 A5:0.2")

            # Lùi một chút
            self.move_backward()
            time.sleep(0.5)

            # Chọn hướng rẽ ngẫu nhiên
            self.turn_direction = random.choice(["LEFT", "RIGHT"])
            self.state = "TURNING"

        else:
            # Rất nguy hiểm - rẽ gấp
            self.stop()
            buzzer.play_song("A6:0.1 SIL:0.05 A6:0.1 SIL:0.05 A6:0.1")

            # Lùi xa hơn
            self.move_backward()
            time.sleep(1.0)

            # Rẽ mạnh
            if self.turn_direction == "RIGHT":
                self.turn_right()
            else:
                self.turn_left()

            time.sleep(1.5)  # Rẽ lâu hơn
            self.state = "FORWARD"

    def run(self):
        """Chạy robot tránh vật cản"""
        print("Robot tránh vật cản đã khởi động!")
        buzzer.play_song("C4 E4 G4 C5", 0.3)  # Âm thanh khởi động

        try:
            while True:
                if self.state == "TURNING":
                    # Đang trong quá trình rẽ
                    if self.turn_direction == "RIGHT":
                        self.turn_right()
                    else:
                        self.turn_left()

                    time.sleep(0.8)  # Rẽ trong 0.8 giây
                    self.state = "FORWARD"
                else:
                    # Quét và tránh vật cản
                    self.avoid_obstacle()

                time.sleep(0.1)  # Delay nhỏ giữa các lần đo

        except KeyboardInterrupt:
            print("Dừng robot...")
            self.stop()
            buzzer.play_song("C5 G4 E4 C4", 0.2)  # Âm thanh tắt

def distance_monitor():
    """Monitor khoảng cách liên tục"""
    print("=== Distance Monitor ===")

    while True:
        distance = sensor.distance_cm()

        if distance != -1.0:
            print(f"Distance: {distance:.1f}cm", end="")

            if distance < 10:
                print(" - DANGER!")
                leds.set_rgb(0, 255, 0, 0)  # Đỏ
                leds.set_rgb(1, 255, 0, 0)
            elif distance < 30:
                print(" - WARNING")
                leds.set_rgb(0, 255, 255, 0)  # Vàng
                leds.set_rgb(1, 255, 255, 0)
            else:
                print(" - SAFE")
                leds.set_rgb(0, 0, 255, 0)  # Xanh
                leds.set_rgb(1, 0, 255, 0)
        else:
            print("Out of range")
            leds.set_rgb(0, 0, 0, 255)  # Xanh dương
            leds.set_rgb(1, 0, 0, 255)

        time.sleep(0.5)

# Chạy chương trình
if __name__ == "__main__":
    # Chọn chế độ chạy
    mode = input("Chọn chế độ (1: Monitor, 2: Robot): ")

    if mode == "1":
        distance_monitor()
    else:
        robot = ObstacleAvoidanceRobot()
        robot.run()
```

## Giải thích mã

### Ví dụ cơ bản:

1. **Đo khoảng cách**: Sử dụng `distance_cm()` để lấy giá trị
2. **Kiểm tra hợp lệ**: Giá trị -1 nghĩa là ngoài phạm vi đo
3. **Hiển thị**: Cập nhật OLED với thông tin khoảng cách
4. **Visualization**: Vẽ thanh biểu diễn trực quan

### Ví dụ nâng cao:

1. **Multi-sensor system**: Kết hợp ultrasonic với motor, LED, buzzer
2. **State machine**: Robot có các trạng thái khác nhau (tiến, rẽ, lùi)
3. **Decision making**: Logic quyết định dựa trên khoảng cách
4. **Safety levels**: Phân cấp mức độ nguy hiểm và phản ứng tương ứng

## Bài tập mở rộng

1. **Hệ thống đỗ xe**: Tạo cảm biến hỗ trợ đỗ xe với âm thanh và LED cảnh báo
2. **Đo mức nước**: Sử dụng cảm biến để đo và cảnh báo mức nước trong bể
3. **Robot mapping**: Tạo robot có thể vẽ bản đồ môi trường xung quanh

## Lỗi thường gặp

```{admonition} Lỗi: Đo khoảng cách không chính xác
:class: warning

**Nguyên nhân**: Nhiệt độ, độ ẩm hoặc vật liệu phản xạ ảnh hưởng

**Giải pháp**:
- Hiệu chỉnh theo nhiệt độ môi trường
- Tránh đo các bề mặt mềm, xốp (thảm, vải)
- Đảm bảo góc đo vuông góc với bề mặt
- Lọc nhiễu bằng cách lấy trung bình nhiều lần đo
```

```{admonition} Lỗi: Cảm biến trả về -1 liên tục
:class: warning

**Nguyên nhân**: Kết nối sai hoặc nguồn không ổn định

**Giải pháp**:
- Kiểm tra kết nối Trigger và Echo pin
- Đảm bảo nguồn 5V ổn định
- Kiểm tra mass chung
- Thử với timeout lớn hơn
```

```{admonition} Lỗi: Đo khoảng cách gần bị sai
:class: warning

**Nguyên nhân**: Blind zone của cảm biến hoặc nhiễu

**Giải pháp**:
- Không đo khoảng cách < 2cm
- Tránh vật cản quá gần cảm biến
- Sử dụng delay giữa các lần đo
- Kiểm tra nhiễu từ các thiết bị khác
```

## Tài nguyên tham khảo

-   [BeE Board API Documentation](https://beestemsolutions.com.vn/docs/bee-board)
-   [HC-SR04 Datasheet](https://cdn.sparkfun.com/datasheets/Sensors/Proximity/HCSR04.pdf)
-   [Ultrasonic Sensor Theory](https://learn.adafruit.com/ultrasonic-sonar-distance-sensors)
-   [BeE Block IDE Online](https://beestemsolutions.com.vn/studio/bee-ide)
