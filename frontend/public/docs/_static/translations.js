Documentation.addTranslations({
    "locale": "vi",
    "messages": {
        "%(filename)s &#8212; %(docstitle)s": "",
        "&#169; %(copyright_prefix)s %(copyright)s.": "",
        ", in ": "",
        "About these documents": "V\u1ec1 c\u00e1c t\u00e0i li\u1ec7u n\u00e0y",
        "Automatically generated list of changes in version %(version)s": "",
        "C API changes": "",
        "Changes in Version %(version)s &#8212; %(docstitle)s": "",
        "Collapse sidebar": "",
        "Complete Table of Contents": "M\u1ee5c L\u1ee5c \u0110\u1ea7y \u0110\u1ee7",
        "Contents": "N\u1ed9i dung",
        "Copyright": "B\u1ea3n quy\u1ec1n",
        "Created using <a href=\"https://www.sphinx-doc.org/\">Sphinx</a> %(sphinx_version)s.": "",
        "Expand sidebar": "",
        "Full index on one page": "To\u00e0n b\u1ed9 ch\u1ec9 m\u1ee5c tr\u00ean m\u1ed9t trang",
        "General Index": "Ch\u1ec9 m\u1ee5c chung",
        "Global Module Index": "Ch\u1ec9 M\u1ee5c M\u00f4-\u0111un To\u00e0n C\u1ee5c",
        "Go": "Th\u1ef1c hi\u1ec7n",
        "Hide Search Matches": "",
        "Index": "",
        "Index &#x2013; %(key)s": "",
        "Index pages by letter": "C\u00e1c trang ch\u1ec9 m\u1ee5c theo ch\u1eef c\u00e1i",
        "Indices and tables:": "C\u00e1c ch\u1ec9 m\u1ee5c v\u00e0 b\u1ea3ng bi\u1ec3u:",
        "Last updated on %(last_updated)s.": "C\u1eadp nh\u1eadt m\u1edbi nh\u1ea5t v\u00e0o %(last_updated)s.",
        "Library changes": "",
        "Navigation": "\u0110i\u1ec1u h\u01b0\u1edbng",
        "Next topic": "Ch\u1ee7 \u0111\u1ec1 ti\u1ebfp",
        "Other changes": "",
        "Overview": "T\u1ed5ng quan",
        "Please activate JavaScript to enable the search\n    functionality.": "H\u00e3y b\u1eadt JavaScript \u0111\u1ec3 d\u00f9ng t\u00ednh n\u0103ng\nt\u00ecm ki\u1ebfm.",
        "Preparing search...": "",
        "Previous topic": "Ch\u1ee7 \u0111\u1ec1 tr\u01b0\u1edbc",
        "Quick search": "",
        "Search": "T\u00ecm Ki\u1ebfm",
        "Search Page": "",
        "Search Results": "",
        "Search finished, found ${resultCount} page(s) matching the search query.": "",
        "Search within %(docstitle)s": "T\u00ecm ki\u1ebfm trong %(docstitle)s",
        "Searching": "",
        "Searching for multiple words only shows matches that contain\n    all words.": "",
        "Show Source": "Hi\u1ec3n th\u1ecb m\u00e3 ngu\u1ed3n",
        "Table of Contents": "",
        "This Page": "",
        "Welcome! This is": "Ch\u00e0o m\u1eebng! \u0110\u00e2y l\u00e0",
        "Your search did not match any documents. Please make sure that all words are spelled correctly and that you've selected enough categories.": "",
        "all functions, classes, terms": "t\u1ea5t c\u1ea3 c\u00e1c h\u00e0m, l\u1edbp, thu\u1eadt ng\u1eef",
        "can be huge": "c\u00f3 th\u1ec3 r\u1ea5t nhi\u1ec1u",
        "last updated": "c\u1eadp nh\u1eadt m\u1edbi nh\u1ea5t",
        "lists all sections and subsections": "li\u1ec7t k\u00ea t\u1ea5t c\u1ea3 c\u00e1c m\u1ee5c v\u00e0 m\u1ee5c con",
        "next chapter": "ch\u01b0\u01a1ng ti\u1ebfp",
        "previous chapter": "ch\u01b0\u01a1ng tr\u01b0\u1edbc ",
        "quick access to all modules": "truy c\u1eadp nhanh t\u1ea5t c\u1ea3 c\u00e1c m\u00f4-\u0111un",
        "search": "",
        "search this documentation": "t\u00ecm ki\u1ebfm trong t\u00e0i li\u1ec7u n\u00e0y",
        "the documentation for": "t\u00e0i li\u1ec7u cho"
    },
    "plural_expr": "0"
});