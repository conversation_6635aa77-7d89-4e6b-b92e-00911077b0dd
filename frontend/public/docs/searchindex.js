Search.setIndex({"alltitles": {"1. Chu\u1ea9n b\u1ecb tr\u01b0\u1edbc khi b\u1eaft \u0111\u1ea7u": [[1, "chuan-bi-truoc-khi-bat-dau"]], "1. Gi\u1edbi thi\u1ec7u": [[3, "gioi-thieu"], [4, "gioi-thieu"], [8, "gioi-thieu"], [9, "gioi-thieu"]], "1. L\u1eadp tr\u00ecnh v\u1edbi BeE IDE": [[11, "lap-trinh-voi-bee-ide"], [12, "lap-trinh-voi-bee-ide"], [13, "lap-trinh-voi-bee-ide"], [14, "lap-trinh-voi-bee-ide"], [15, "lap-trinh-voi-bee-ide"], [16, "lap-trinh-voi-bee-ide"], [17, "lap-trinh-voi-bee-ide"]], "1. <PERSON>h c\u00f4ng c\u1ee5 (<PERSON><PERSON><PERSON>)": [[7, "thanh-cong-cu-toolbar"]], "1. T\u1ed5ng quan": [[2, "tong-quan"]], "1. Y\u00eau c\u1ea7u h\u1ec7 th\u1ed1ng": [[6, "yeu-cau-he-thong"]], "10. S\u1ef1 c\u1ed1 ngu\u1ed3n \u0111i\u1ec7n": [[4, "su-co-nguon-dien"]], "10. T\u00e0i li\u1ec7u tham kh\u1ea3o": [[2, "tai-lieu-tham-khao"]], "11. Ki\u1ec3m tra nhanh b\u1eb1ng code test": [[4, "kiem-tra-nhanh-bang-code-test"]], "12. Li\u00ean h\u1ec7 h\u1ed7 tr\u1ee3": [[4, "lien-he-ho-tro"]], "2. C\u00e0i \u0111\u1eb7t driver CH340C": [[6, "cai-dat-driver-ch340c"]], "2. Danh m\u1ee5c kh\u1ed1i l\u1ec7nh (Toolbox)": [[7, "danh-muc-khoi-lenh-toolbox"]], "2. Flashing firmware qua USB": [[9, "flashing-firmware-qua-usb"]], "2. L\u1eadp tr\u00ecnh k\u00e9o th\u1ea3 v\u1edbi BeE IDE": [[3, "lap-trinh-keo-tha-voi-bee-ide"]], "2. L\u1eadp tr\u00ecnh v\u1edbi BeE Python": [[11, "lap-trinh-voi-bee-python"], [12, "lap-trinh-voi-bee-python"], [13, "lap-trinh-voi-bee-python"], [14, "lap-trinh-voi-bee-python"], [15, "lap-trinh-voi-bee-python"], [16, "lap-trinh-voi-bee-python"], [17, "lap-trinh-voi-bee-python"]], "2. L\u1ed7i khi k\u1ebft n\u1ed1i USB": [[4, "loi-khi-ket-noi-usb"]], "2. N\u1ea1p ch\u01b0\u01a1ng tr\u00ecnh qua USB": [[8, "nap-chuong-trinh-qua-usb"]], "2. S\u01a1 \u0111\u1ed3 t\u1ed5ng quan v\u00e0 th\u00e0nh ph\u1ea7n": [[2, "so-do-tong-quan-va-thanh-phan"]], "2. Truy c\u1eadp n\u1ec1n t\u1ea3ng l\u1eadp tr\u00ecnh": [[1, "truy-cap-nen-tang-lap-trinh"]], "3. C\u1eadp nh\u1eadt ch\u01b0\u01a1ng tr\u00ecnh qua OTA": [[8, "cap-nhat-chuong-trinh-qua-ota"]], "3. Khu v\u1ef1c l\u00e0m vi\u1ec7c (Workspace)": [[7, "khu-vuc-lam-viec-workspace"]], "3. K\u1ebft n\u1ed1i BeE Board V2": [[1, "ket-noi-bee-board-v2"]], "3. L\u1eadp tr\u00ecnh Python v\u1edbi BeE Python": [[3, "lap-trinh-python-voi-bee-python"]], "3. L\u1ed7i khi n\u1ea1p code OTA (Wi-Fi)": [[4, "loi-khi-nap-code-ota-wi-fi"]], "3. Th\u00f4ng s\u1ed1 k\u1ef9 thu\u1eadt chi ti\u1ebft": [[2, "thong-so-ky-thuat-chi-tiet"]], "3. Th\u1ea3o lu\u1eadn v\u00e0 m\u1edf r\u1ed9ng": [[11, "thao-luan-va-mo-rong"], [12, "thao-luan-va-mo-rong"], [13, "thao-luan-va-mo-rong"], [14, "thao-luan-va-mo-rong"], [15, "thao-luan-va-mo-rong"], [16, "thao-luan-va-mo-rong"], [17, "thao-luan-va-mo-rong"]], "3. Truy c\u1eadp n\u1ec1n t\u1ea3ng l\u1eadp tr\u00ecnh": [[6, "truy-cap-nen-tang-lap-trinh"]], "4. B\u1ea3ng Log": [[7, "bang-log"]], "4. C\u1eadp nh\u1eadt ch\u01b0\u01a1ng tr\u00ecnh OTA t\u1eeb BeE Python": [[3, "cap-nhat-chuong-trinh-ota-tu-bee-python"]], "4. C\u1eadp nh\u1eadt ch\u01b0\u01a1ng tr\u00ecnh qua Bluetooth": [[8, "cap-nhat-chuong-trinh-qua-bluetooth"]], "4. K\u1ebft lu\u1eadn": [[11, "ket-luan"], [12, "ket-luan"], [13, "ket-luan"], [14, "ket-luan"], [15, "ket-luan"], [16, "ket-luan"], [17, "ket-luan"]], "4. K\u1ebft n\u1ed1i BeE Board V2 qua USB": [[6, "ket-noi-bee-board-v2-qua-usb"]], "4. L\u1ed7i LED ho\u1eb7c Button": [[4, "loi-led-hoac-button"]], "4. Mapping c\u1ed5ng & ch\u00e2n k\u1ebft n\u1ed1i": [[2, "mapping-cong-chan-ket-noi"]], "4. Vi\u1ebft ch\u01b0\u01a1ng tr\u00ecnh \u0111\u1ea7u ti\u00ean": [[1, "viet-chuong-trinh-dau-tien"]], "5. C\u1ea5u h\u00ecnh k\u1ebft n\u1ed1i qua Wi-Fi (OTA)": [[6, "cau-hinh-ket-noi-qua-wi-fi-ota"]], "5. Giao di\u1ec7n t\u00f9y ch\u1ec9nh": [[7, "giao-dien-tuy-chinh"]], "5. G\u1ee3i \u00fd d\u1ef1 \u00e1n th\u1ef1c h\u00e0nh": [[3, "goi-y-du-an-thuc-hanh"]], "5. L\u1ed7i m\u00e0n h\u00ecnh OLED": [[4, "loi-man-hinh-oled"]], "5. Ngu\u1ed3n c\u1ea5p v\u00e0 c\u00f4ng t\u1eafc": [[2, "nguon-cap-va-cong-tac"]], "5. So s\u00e1nh nhanh gi\u1eefa BeE IDE v\u00e0 BeE Python": [[1, "so-sanh-nhanh-giua-bee-ide-va-bee-python"]], "6. BeE Assistant": [[7, "bee-assistant"]], "6. C\u1eadp nh\u1eadt ch\u01b0\u01a1ng tr\u00ecnh qua OTA (Wi-Fi)": [[1, "cap-nhat-chuong-trinh-qua-ota-wi-fi"]], "6. Ki\u1ec3m tra ho\u1ea1t \u0111\u1ed9ng c\u1ee7a board": [[6, "kiem-tra-hoat-dong-cua-board"]], "6. L\u1ed7i loa Buzzer": [[4, "loi-loa-buzzer"]], "6. T\u00e0i li\u1ec7u li\u00ean quan": [[3, "tai-lieu-lien-quan"]], "6. Vi x\u1eed l\u00fd trung t\u00e2m (ESP32-S3)": [[2, "vi-xu-ly-trung-tam-esp32-s3"]], "7. K\u1ebft n\u1ed1i v\u00e0 giao ti\u1ebfp": [[2, "ket-noi-va-giao-tiep"]], "7. L\u1ed7i khi l\u1eadp tr\u00ecnh Python": [[4, "loi-khi-lap-trinh-python"]], "7. M\u1eb9o cho ng\u01b0\u1eddi m\u1edbi b\u1eaft \u0111\u1ea7u": [[1, "meo-cho-nguoi-moi-bat-dau"]], "7. M\u1eb9o s\u1eed d\u1ee5ng hi\u1ec7u qu\u1ea3": [[7, "meo-su-dung-hieu-qua"]], "7. S\u1ef1 c\u1ed1 th\u01b0\u1eddng g\u1eb7p": [[6, "su-co-thuong-gap"]], "8. B\u01b0\u1edbc ti\u1ebfp theo": [[6, "buoc-tiep-theo"]], "8. L\u1ed7i trong BeE IDE": [[4, "loi-trong-bee-ide"]], "8. T\u00e0i li\u1ec7u li\u00ean quan": [[7, "tai-lieu-lien-quan"]], "8. T\u01b0\u01a1ng th\u00edch LEGO Technic": [[2, "tuong-thich-lego-technic"]], "9. C\u00e1c \u0111\u1ed1i t\u01b0\u1ee3ng trong l\u1eadp tr\u00ecnh Python": [[2, "cac-doi-tuong-trong-lap-trinh-python"]], "9. L\u1ed7i trong BeE Python (Code Editor)": [[4, "loi-trong-bee-python-code-editor"]], "About": [[27, null]], "BeE Board V2": [[27, null]], "BeE Board V2 l\u00e0 g\u00ec?": [[0, "bee-board-v2-la-gi"]], "BeE IDE": [[5, null], [27, null]], "BeE IDE l\u00e0 g\u00ec?": [[5, "bee-ide-la-gi"]], "BeE IDE \u2013 L\u1eadp tr\u00ecnh k\u00e9o th\u1ea3 (Blockly)": [[1, "bee-ide-lap-trinh-keo-tha-blockly"]], "BeE Python \u2013 L\u1eadp tr\u00ecnh b\u1eb1ng Python": [[1, "bee-python-lap-trinh-bang-python"]], "BeE STEM Solutions": [[26, null]], "BeE STEM Solutions Documentation": [[27, null]], "BeE Starter Kit": [[18, "bee-starter-kit"]], "BeeColorDetect": [[20, null]], "BeeLedSegment": [[22, null]], "BeeLineDetect": [[23, null]], "BeeRC522": [[24, null]], "BeeUltrasonic": [[25, null]], "Buzzer": [[13, null]], "B\u00e0i h\u00e1t \u201cHappy Birthday\u201d (n\u00e2ng cao)": [[13, "bai-hat-happy-birthday-nang-cao"]], "B\u00e0i t\u1eadp m\u1edf r\u1ed9ng": [[19, "bai-tap-mo-rong"], [20, "bai-tap-mo-rong"], [21, "bai-tap-mo-rong"], [22, "bai-tap-mo-rong"], [23, "bai-tap-mo-rong"], [24, "bai-tap-mo-rong"], [25, "bai-tap-mo-rong"]], "B\u01b0\u1edbc 1. C\u1eafm module v\u00e0o PORT th\u00edch h\u1ee3p": [[18, "buoc-1-cam-module-vao-port-thich-hop"]], "B\u01b0\u1edbc 2. Import th\u01b0 vi\u1ec7n trong ch\u01b0\u01a1ng tr\u00ecnh Python": [[18, "buoc-2-import-thu-vien-trong-chuong-trinh-python"]], "B\u01b0\u1edbc 3. L\u1eadp tr\u00ecnh v\u1edbi BeE IDE": [[18, "buoc-3-lap-trinh-voi-bee-ide"]], "B\u01b0\u1edbc 4. L\u1eadp tr\u00ecnh v\u1edbi BeE Python": [[18, "buoc-4-lap-trinh-voi-bee-python"]], "B\u1ea1n c\u1ea7n c\u00f3:": [[1, "ban-can-co"]], "B\u1eadt LED 1 m\u00e0u xanh d\u01b0\u01a1ng": [[11, "bat-led-1-mau-xanh-duong"]], "B\u1eadt LED 1 m\u00e0u \u0111\u1ecf": [[3, "bat-led-1-mau-do"], [11, "bat-led-1-mau-do"]], "B\u1eadt LED khi nh\u1ea5n n\u00fat A": [[12, "bat-led-khi-nhan-nut-a"], [12, "id1"]], "B\u1eadt LED khi qu\u00e1 n\u00f3ng": [[21, "bat-led-khi-qua-nong"]], "B\u1eaft \u0111\u1ea7u": [[0, null], [1, null]], "Callback b\u1ecb g\u1ecdi nhi\u1ec1u l\u1ea7n": [[19, null]], "Chu\u1ea9n b\u1ecb": [[18, "chuan-bi"]], "Ch\u1ea1y robot ti\u1ebfn 2 gi\u00e2y": [[15, "chay-robot-tien-2-giay"]], "C\u00e0i \u0111\u1eb7t": [[6, null]], "C\u00e1c kh\u1ed1i l\u1ec7nh ph\u1ed5 bi\u1ebfn:": [[21, "cac-khoi-lenh-pho-bien"]], "C\u00e1c module t\u00edch h\u1ee3p": [[10, "cac-module-tich-hop"]], "C\u00e1c nh\u00f3m kh\u1ed1i Blockly h\u1ed7 tr\u1ee3": [[0, "cac-nhom-khoi-blockly-ho-tro"]], "C\u00e1c n\u1ec1n t\u1ea3ng tr\u1ef1c tuy\u1ebfn c\u1ee7a BeE": [[26, "cac-nen-tang-truc-tuyen-cua-bee"]], "C\u00e1c ph\u01b0\u01a1ng th\u1ee9c ch\u00ednh": [[25, "cac-phuong-thuc-chinh"]], "C\u00e1c s\u1ea3n ph\u1ea9m v\u00e0 kh\u00f3a h\u1ecdc ti\u00eau bi\u1ec3u": [[26, "cac-san-pham-va-khoa-hoc-tieu-bieu"]], "C\u00e1c t\u00ednh n\u0103ng n\u1ed5i b\u1eadt": [[5, "cac-tinh-nang-noi-bat"]], "C\u00e1c v\u00ed d\u1ee5 th\u1ef1c h\u00e0nh": [[10, "cac-vi-du-thuc-hanh"]], "C\u00e1ch c\u00e0i \u0111\u1eb7t": [[18, "cach-cai-dat"]], "C\u00e1ch c\u00e0i \u0111\u1eb7t v\u00e0 s\u1eed d\u1ee5ng": [[18, "cach-cai-dat-va-su-dung"]], "C\u00e1ch k\u1ebft n\u1ed1i BeE Board V2": [[5, "cach-ket-noi-bee-board-v2"]], "C\u1ea3m bi\u1ebfn IMU": [[17, null]], "C\u1ea5u h\u00ecnh c\u1ea3m bi\u1ebfn": [[20, "cau-hinh-cam-bien"]], "C\u1eadp nh\u1eadt code qua OTA": [[0, "cap-nhat-code-qua-ota"]], "C\u1ed5ng m\u1edf r\u1ed9ng tr\u00ean BeE Board V2": [[18, "cong-mo-rong-tren-bee-board-v2"]], "Danh s\u00e1ch module m\u1edf r\u1ed9ng": [[18, "danh-sach-module-mo-rong"]], "D\u1eef li\u1ec7u tr\u1ea3 v\u1ec1 None ho\u1eb7c 0": [[21, null]], "Giao di\u1ec7n": [[7, null]], "Giao di\u1ec7n BeE IDE": [[5, "giao-dien-bee-ide"]], "Giao di\u1ec7n ch\u00ednh": [[3, "giao-dien-chinh"], [3, "id2"]], "Giao di\u1ec7n l\u1eadp tr\u00ecnh": [[20, "giao-dien-lap-trinh"], [22, "giao-dien-lap-trinh"], [23, "giao-dien-lap-trinh"], [24, "giao-dien-lap-trinh"], [25, "giao-dien-lap-trinh"]], "Giao di\u1ec7n l\u1eadp tr\u00ecnh Python": [[19, "giao-dien-lap-trinh-python"], [21, "giao-dien-lap-trinh-python"]], "Gi\u00e1 tr\u1ecb c\u1ed1t l\u00f5i": [[26, "gia-tri-cot-loi"]], "Gi\u00e1 tr\u1ecb nhi\u1ec7t \u0111\u1ed9 sai l\u1ec7ch": [[21, null]], "Gi\u1ea3i th\u00edch m\u00e3": [[19, "giai-thich-ma"], [20, "giai-thich-ma"], [21, "giai-thich-ma"], [22, "giai-thich-ma"], [23, "giai-thich-ma"], [24, "giai-thich-ma"], [25, "giai-thich-ma"]], "Gi\u1edbi thi\u1ec7u": [[0, null], [10, "gioi-thieu"], [18, null], [18, "id1"], [19, "gioi-thieu"], [20, "gioi-thieu"], [21, "gioi-thieu"], [22, "gioi-thieu"], [23, "gioi-thieu"], [24, "gioi-thieu"], [25, "gioi-thieu"], [27, "gioi-thieu"]], "Gi\u1edbi thi\u1ec7u ph\u1ea7n c\u1ee9ng": [[2, null]], "Grove Ecosystem": [[18, "grove-ecosystem"]], "G\u1ee3i \u00fd m\u1edf r\u1ed9ng": [[10, "goi-y-mo-rong"]], "Hi\u1ec3n th\u1ecb ch\u1eef c\u01a1 b\u1ea3n": [[14, "hien-thi-chu-co-ban"]], "Hi\u1ec3n th\u1ecb ch\u1eef tr\u00ean OLED": [[3, "hien-thi-chu-tren-oled"]], "Hi\u1ec3n th\u1ecb c\u1ea3m bi\u1ebfn IMU": [[14, "hien-thi-cam-bien-imu"]], "Hi\u1ec3n th\u1ecb d\u00f2ng ch\u1eef \u201cHello BeE!\u201d": [[14, "hien-thi-dong-chu-hello-bee"]], "Hi\u1ec3n th\u1ecb g\u00f3c nghi\u00eang": [[17, "hien-thi-goc-nghieng"], [17, "id1"]], "Hi\u1ec3n th\u1ecb nhi\u1ec1u d\u00f2ng": [[14, "hien-thi-nhieu-dong"], [14, "id1"]], "Hi\u1ec3n th\u1ecb nhi\u1ec7t \u0111\u1ed9": [[22, "hien-thi-nhiet-do"]], "Hi\u1ec3n th\u1ecb nhi\u1ec7t \u0111\u1ed9 & \u0111\u1ed9 \u1ea9m l\u00ean OLED": [[21, "hien-thi-nhiet-do-do-am-len-oled"]], "Hi\u1ec3n th\u1ecb s\u1ed1": [[22, "hien-thi-so"]], "Hi\u1ec3n th\u1ecb text": [[22, "hien-thi-text"]], "Hi\u1ec7u \u1ee9ng ch\u1eef ch\u1ea1y": [[14, "hieu-ung-chu-chay"], [14, "id2"]], "Hi\u1ec7u \u1ee9ng \u0111\u1ed5i m\u00e0u": [[11, "hieu-ung-doi-mau"]], "H\u01b0\u1edbng d\u1eabn c\u00e0i \u0111\u1eb7t th\u01b0 vi\u1ec7n module": [[18, "huong-dan-cai-dat-thu-vien-module"]], "H\u01b0\u1edbng d\u1eabn l\u1eadp tr\u00ecnh": [[3, null]], "H\u1ec7 sinh th\u00e1i BeE": [[26, "he-sinh-thai-bee"]], "Kh\u1edfi t\u1ea1o": [[19, "khoi-tao"], [20, "khoi-tao"], [22, "khoi-tao"], [23, "khoi-tao"], [24, "khoi-tao"], [25, "khoi-tao"]], "Kh\u1edfi t\u1ea1o module": [[21, "khoi-tao-module"]], "Ki\u1ec3m tra k\u1ebft n\u1ed1i": [[23, "kiem-tra-ket-noi"]], "Ki\u1ec3m tra tr\u1ea1ng th\u00e1i nh\u1ea5n": [[19, "kiem-tra-trang-thai-nhan"]], "Ki\u1ec3m tra tr\u1ea1ng th\u00e1i th\u1ea3": [[19, "kiem-tra-trang-thai-tha"]], "K\u1ebft h\u1ee3p A & B": [[12, "ket-hop-a-b"]], "K\u1ebft h\u1ee3p LED v\u00e0 Servo": [[16, "ket-hop-led-va-servo"]], "K\u1ebft h\u1ee3p c\u1ea3 hai n\u00fat": [[12, "ket-hop-ca-hai-nut"]], "K\u1ebft h\u1ee3p v\u1edbi LED": [[13, "ket-hop-voi-led"], [13, "id3"]], "K\u1ebft n\u1ed1i qua USB-C": [[0, "ket-noi-qua-usb-c"]], "K\u1ebft n\u1ed1i v\u00e0 l\u1eadp tr\u00ecnh": [[0, "ket-noi-va-lap-trinh"]], "LED RGB": [[11, null]], "Li\u00ean h\u1ec7 BeE STEM Solutions": [[27, "lien-he-bee-stem-solutions"]], "Long press kh\u00f4ng ho\u1ea1t \u0111\u1ed9ng": [[19, null]], "L\u1eadp tr\u00ecnh d\u1ec5 d\u00e0ng v\u1edbi BeE IDE": [[10, "lap-trinh-de-dang-voi-bee-ide"]], "L\u1eadp tr\u00ecnh n\u00e2ng cao v\u1edbi BeE Python": [[10, "lap-trinh-nang-cao-voi-bee-python"]], "L\u1eadp tr\u00ecnh v\u1edbi BeE IDE": [[19, "lap-trinh-voi-bee-ide"], [21, "lap-trinh-voi-bee-ide"]], "L\u1ed7i th\u01b0\u1eddng g\u1eb7p": [[19, "loi-thuong-gap"], [20, "loi-thuong-gap"], [21, "loi-thuong-gap"], [22, "loi-thuong-gap"], [23, "loi-thuong-gap"], [24, "loi-thuong-gap"], [25, "loi-thuong-gap"]], "L\u1ed7i: C\u1ea3m bi\u1ebfn kh\u00f4ng ph\u00e1t hi\u1ec7n m\u00e0u ch\u00ednh x\u00e1c": [[20, null]], "L\u1ed7i: C\u1ea3m bi\u1ebfn kh\u00f4ng ph\u00e1t hi\u1ec7n \u0111\u01b0\u1eddng": [[23, null]], "L\u1ed7i: C\u1ea3m bi\u1ebfn tr\u1ea3 v\u1ec1 -1 li\u00ean t\u1ee5c": [[25, null]], "L\u1ed7i: File JSON b\u1ecb l\u1ed7i": [[24, null]], "L\u1ed7i: Hi\u1ec3n th\u1ecb nh\u1ea5p nh\u00e1y ho\u1eb7c kh\u00f4ng \u1ed5n \u0111\u1ecbnh": [[22, null]], "L\u1ed7i: I2C communication failed": [[20, null]], "L\u1ed7i: Kh\u00f4ng giao ti\u1ebfp \u0111\u01b0\u1ee3c I2C": [[23, null]], "L\u1ed7i: Kh\u00f4ng \u0111\u1ecdc \u0111\u01b0\u1ee3c th\u1ebb": [[24, null]], "L\u1ed7i: K\u00fd t\u1ef1 hi\u1ec3n th\u1ecb sai": [[22, null]], "L\u1ed7i: M\u00e0u nh\u1eadn di\u1ec7n kh\u00f4ng \u1ed5n \u0111\u1ecbnh": [[20, null]], "L\u1ed7i: M\u1ed9t s\u1ed1 segment kh\u00f4ng s\u00e1ng": [[22, null]], "L\u1ed7i: Robot dao \u0111\u1ed9ng khi theo \u0111\u01b0\u1eddng": [[23, null]], "L\u1ed7i: \u0110o kho\u1ea3ng c\u00e1ch g\u1ea7n b\u1ecb sai": [[25, null]], "L\u1ed7i: \u0110o kho\u1ea3ng c\u00e1ch kh\u00f4ng ch\u00ednh x\u00e1c": [[25, null]], "L\u1ed7i: \u0110\u1ecba ch\u1ec9 I2C kh\u00f4ng \u0111\u00fang": [[24, null]], "L\u1eddi c\u1ea3m \u01a1n": [[26, "loi-cam-on"]], "MacOS & Linux": [[6, "macos-linux"]], "Module DHT11": [[21, null]], "Module N\u00fat Nh\u1ea5n": [[19, null]], "Module m\u1edf r\u1ed9ng": [[27, null]], "M\u00e0n h\u00ecnh OLED": [[14, null]], "M\u1eb9o h\u1ecdc hi\u1ec7u qu\u1ea3": [[5, "meo-hoc-hieu-qua"]], "M\u1eb9o s\u1eed d\u1ee5ng hi\u1ec7u qu\u1ea3": [[18, "meo-su-dung-hieu-qua"]], "M\u1ed9t s\u1ed1 h\u00e0m c\u01a1 b\u1ea3n trong Python": [[3, "mot-so-ham-co-ban-trong-python"]], "M\u1ed9t s\u1ed1 v\u00ed d\u1ee5 \u0111\u01a1n gi\u1ea3n": [[0, "mot-so-vi-du-don-gian"]], "M\u1ee5c ti\u00eau": [[6, "muc-tieu"], [7, "muc-tieu"], [11, "muc-tieu"], [12, "muc-tieu"], [13, "muc-tieu"], [14, "muc-tieu"], [15, "muc-tieu"], [16, "muc-tieu"], [17, "muc-tieu"]], "Nh\u00f3m kh\u1ed1i l\u1ec7nh ch\u00ednh": [[5, "nhom-khoi-lenh-chinh"]], "Nh\u00f3m kh\u1ed1i l\u1ec7nh c\u01a1 b\u1ea3n": [[3, "nhom-khoi-lenh-co-ban"]], "N\u00fat kh\u00f4ng ph\u1ea3n h\u1ed3i": [[19, null]], "N\u00fat nh\u1ea5n": [[12, null]], "N\u1ea1p Firmware": [[9, null]], "N\u1ea1p ch\u01b0\u01a1ng tr\u00ecnh": [[8, null]], "N\u1ea1p ch\u01b0\u01a1ng tr\u00ecnh t\u1eeb BeE IDE": [[3, "nap-chuong-trinh-tu-bee-ide"]], "N\u1ea1p qua OTA": [[3, "nap-qua-ota"]], "N\u1ea1p qua USB": [[3, "nap-qua-usb"]], "OhStem Ecosystem": [[18, "ohstem-ecosystem"]], "Ph\u00e1t 1 n\u1ed1t nh\u1ea1c": [[13, "phat-1-not-nhac"], [13, "id1"]], "Ph\u00e1t chu\u1ed7i n\u1ed1t nh\u1ea1c": [[13, "phat-chuoi-not-nhac"], [13, "id2"]], "Ph\u00e1t hi\u1ec7n rung (Shake Detection)": [[17, "phat-hien-rung-shake-detection"]], "Ph\u00e1t hi\u1ec7n rung l\u1eafc": [[17, "phat-hien-rung-lac"]], "Ph\u00e1t nh\u1ea1c khi nh\u1ea5n n\u00fat B": [[12, "phat-nhac-khi-nhan-nut-b"], [12, "id2"]], "Ph\u1ea7n c\u1ee9ng c\u1ea7n c\u00f3": [[11, "phan-cung-can-co"], [12, "phan-cung-can-co"], [13, "phan-cung-can-co"], [14, "phan-cung-can-co"], [15, "phan-cung-can-co"], [16, "phan-cung-can-co"], [17, "phan-cung-can-co"]], "Qua USB": [[5, "qua-usb"]], "Qua Wi-Fi (OTA)": [[5, "qua-wi-fi-ota"]], "Quay tr\u00e1i v\u00e0 quay ph\u1ea3i": [[15, "quay-trai-va-quay-phai"], [15, "id1"]], "Qu\u1ea3n l\u00fd danh s\u00e1ch th\u1ebb": [[24, "quan-ly-danh-sach-the"]], "Qu\u1ea3n l\u00fd d\u1ef1 \u00e1n": [[5, "quan-ly-du-an"]], "Robot ti\u1ebfn v\u00e0 quay": [[3, "robot-tien-va-quay"]], "Servo": [[16, null]], "S\u01a1 \u0111\u1ed3 k\u1ebft n\u1ed1i": [[19, "so-do-ket-noi"], [21, "so-do-ket-noi"]], "Thu\u1ed9c t\u00ednh port": [[23, "thuoc-tinh-port"]], "Th\u00e0nh ph\u1ea7n ch\u00ednh tr\u00ean board": [[0, "thanh-phan-chinh-tren-board"]], "Th\u00f4ng s\u1ed1 k\u1ef9 thu\u1eadt": [[0, "thong-so-ky-thuat"], [19, "thong-so-ky-thuat"], [20, "thong-so-ky-thuat"], [21, "thong-so-ky-thuat"], [22, "thong-so-ky-thuat"], [23, "thong-so-ky-thuat"], [24, "thong-so-ky-thuat"], [25, "thong-so-ky-thuat"]], "Th\u00f4ng tin li\u00ean h\u1ec7": [[26, "thong-tin-lien-he"]], "Th\u1ef1c h\u00e0nh v\u1edbi BeE IDE": [[5, "thuc-hanh-voi-bee-ide"]], "Ti\u1ebfp theo": [[1, "tiep-theo"]], "Tri\u1ebft l\u00fd gi\u00e1o d\u1ee5c": [[26, "triet-ly-giao-duc"]], "Trong BeE IDE": [[1, "trong-bee-ide"]], "Trong BeE Python": [[1, "trong-bee-python"]], "Truy c\u1eadp": [[3, "truy-cap"], [3, "id1"]], "T\u00e0i li\u1ec7u li\u00ean quan": [[5, "tai-lieu-lien-quan"], [18, "tai-lieu-lien-quan"]], "T\u00e0i nguy\u00ean li\u00ean quan": [[0, "tai-nguyen-lien-quan"]], "T\u00e0i nguy\u00ean tham kh\u1ea3o": [[19, "tai-nguyen-tham-khao"], [20, "tai-nguyen-tham-khao"], [21, "tai-nguyen-tham-khao"], [22, "tai-nguyen-tham-khao"], [23, "tai-nguyen-tham-khao"], [24, "tai-nguyen-tham-khao"], [25, "tai-nguyen-tham-khao"]], "T\u1ea1o hi\u1ec7u \u1ee9ng nh\u1ea5p nh\u00e1y LED": [[11, "tao-hieu-ung-nhap-nhay-led"]], "T\u1ea7m nh\u00ecn": [[26, "tam-nhin"]], "T\u1ed5ng quan giao di\u1ec7n": [[7, "tong-quan-giao-dien"]], "V\u00ed d\u1ee5 1: B\u1eadt LED 1 m\u00e0u xanh l\u00e1": [[0, "vi-du-1-bat-led-1-mau-xanh-la"]], "V\u00ed d\u1ee5 2: Hi\u1ec3n th\u1ecb d\u00f2ng ch\u1eef": [[0, "vi-du-2-hien-thi-dong-chu"]], "V\u00ed d\u1ee5 3: Robot di chuy\u1ec3n": [[0, "vi-du-3-robot-di-chuyen"]], "V\u00ed d\u1ee5 Blockly": [[5, null], [20, "vi-du-blockly"], [22, "vi-du-blockly"], [23, "vi-du-blockly"], [24, "vi-du-blockly"], [25, "vi-du-blockly"]], "V\u00ed d\u1ee5 Python": [[19, "vi-du-python"], [20, "vi-du-python"], [21, "vi-du-python"], [22, "vi-du-python"], [23, "vi-du-python"], [24, "vi-du-python"], [25, "vi-du-python"]], "V\u00ed d\u1ee5 ch\u01b0\u01a1ng tr\u00ecnh c\u01a1 b\u1ea3n": [[3, "vi-du-chuong-trinh-co-ban"]], "V\u00ed d\u1ee5 c\u01a1 b\u1ea3n - H\u1ec7 th\u1ed1ng ki\u1ec3m so\u00e1t truy c\u1eadp": [[24, "vi-du-co-ban-he-thong-kiem-soat-truy-cap"]], "V\u00ed d\u1ee5 c\u01a1 b\u1ea3n - Nh\u1eadn di\u1ec7n m\u00e0u \u0111\u01a1n gi\u1ea3n": [[20, "vi-du-co-ban-nhan-dien-mau-don-gian"]], "V\u00ed d\u1ee5 c\u01a1 b\u1ea3n - Theo \u0111\u01b0\u1eddng \u0111\u01a1n gi\u1ea3n": [[23, "vi-du-co-ban-theo-duong-don-gian"]], "V\u00ed d\u1ee5 c\u01a1 b\u1ea3n - \u0110o kho\u1ea3ng c\u00e1ch \u0111\u01a1n gi\u1ea3n": [[25, "vi-du-co-ban-do-khoang-cach-don-gian"]], "V\u00ed d\u1ee5 c\u01a1 b\u1ea3n - \u0110\u1ed3ng h\u1ed3 s\u1ed1": [[22, "vi-du-co-ban-dong-ho-so"]], "V\u00ed d\u1ee5 c\u01a1 b\u1ea3n \u2013 B\u1eadt t\u1eaft LED khi nh\u1ea5n n\u00fat": [[19, "vi-du-co-ban-bat-tat-led-khi-nhan-nut"]], "V\u00ed d\u1ee5 c\u01a1 b\u1ea3n:": [[20, "vi-du-co-ban"], [22, "vi-du-co-ban"], [23, "vi-du-co-ban"], [24, "vi-du-co-ban"], [25, "vi-du-co-ban"]], "V\u00ed d\u1ee5 kh\u1ed1i l\u1ec7nh:": [[19, "vi-du-khoi-lenh"]], "V\u00ed d\u1ee5 l\u1eadp tr\u00ecnh": [[10, null], [27, null]], "V\u00ed d\u1ee5 n\u00e2ng cao - H\u1ec7 th\u1ed1ng hi\u1ec3n th\u1ecb \u0111a ch\u1ee9c n\u0103ng": [[22, "vi-du-nang-cao-he-thong-hien-thi-da-chuc-nang"]], "V\u00ed d\u1ee5 n\u00e2ng cao - H\u1ec7 th\u1ed1ng \u0111i\u1ec3m danh th\u00f4ng minh": [[24, "vi-du-nang-cao-he-thong-diem-danh-thong-minh"]], "V\u00ed d\u1ee5 n\u00e2ng cao - Robot ph\u00e2n lo\u1ea1i m\u00e0u": [[20, "vi-du-nang-cao-robot-phan-loai-mau"]], "V\u00ed d\u1ee5 n\u00e2ng cao - Robot theo \u0111\u01b0\u1eddng th\u00f4ng minh v\u1edbi PID": [[23, "vi-du-nang-cao-robot-theo-duong-thong-minh-voi-pid"]], "V\u00ed d\u1ee5 n\u00e2ng cao - Robot tr\u00e1nh v\u1eadt c\u1ea3n": [[25, "vi-du-nang-cao-robot-tranh-vat-can"]], "V\u00ed d\u1ee5 n\u00e2ng cao \u2013 T\u1ea1o menu \u0111i\u1ec1u h\u01b0\u1edbng b\u1eb1ng 2 n\u00fat": [[19, "vi-du-nang-cao-tao-menu-dieu-huong-bang-2-nut"]], "V\u00ed d\u1ee5 n\u00e2ng cao:": [[20, "vi-du-nang-cao"], [22, "vi-du-nang-cao"], [23, "vi-du-nang-cao"], [24, "vi-du-nang-cao"], [25, "vi-du-nang-cao"]], "Windows": [[6, "windows"]], "Xoay servo \u0111\u1ebfn g\u00f3c 90\u00b0": [[16, "xoay-servo-den-goc-90"]], "Xoay servo \u0111\u1ebfn g\u00f3c c\u1ed1 \u0111\u1ecbnh": [[16, "xoay-servo-den-goc-co-dinh"]], "Xoay tu\u1ea7n t\u1ef1 nhi\u1ec1u g\u00f3c": [[16, "xoay-tuan-tu-nhieu-goc"], [16, "id1"]], "X\u1eed l\u00fd l\u1ed7i": [[4, null]], "X\u1eed l\u00fd s\u1ef1 c\u1ed1 th\u01b0\u1eddng g\u1eb7p": [[0, "xu-ly-su-co-thuong-gap"]], "brightness(value) - Thi\u1ebft l\u1eadp/\u0111\u1ecdc \u0111\u1ed9 s\u00e1ng": [[22, "brightness-value-thiet-lap-doc-do-sang"]], "check() - Ki\u1ec3m tra module c\u00f3 ho\u1ea1t \u0111\u1ed9ng kh\u00f4ng": [[23, "check-kiem-tra-module-co-hoat-dong-khong"]], "clear() - X\u00f3a m\u00e0n h\u00ecnh": [[22, "clear-xoa-man-hinh"]], "clear_list(list_name) - X\u00f3a to\u00e0n b\u1ed9 danh s\u00e1ch": [[24, "clear-list-list-name-xoa-toan-bo-danh-sach"]], "display(text) - Hi\u1ec3n th\u1ecb th\u00f4ng minh": [[22, "display-text-hien-thi-thong-minh"]], "distance_cm() - \u0110o kho\u1ea3ng c\u00e1ch (cm)": [[25, "distance-cm-do-khoang-cach-cm"]], "distance_mm() - \u0110o kho\u1ea3ng c\u00e1ch (mm)": [[25, "distance-mm-do-khoang-cach-mm"]], "gain - Thi\u1ebft l\u1eadp \u0111\u1ed9 khu\u1ebfch \u0111\u1ea1i": [[20, "gain-thiet-lap-do-khuech-dai"]], "get_all_colors_in_rgb() - \u0110\u1ecdc t\u1ea5t c\u1ea3 k\u00eanh RGB": [[20, "get-all-colors-in-rgb-doc-tat-ca-kenh-rgb"]], "get_color() - Nh\u1eadn di\u1ec7n m\u00e0u c\u01a1 b\u1ea3n": [[20, "get-color-nhan-dien-mau-co-ban"]], "html_hex() - Chuy\u1ec3n \u0111\u1ed5i sang m\u00e3 hex HTML": [[20, "html-hex-chuyen-doi-sang-ma-hex-html"]], "integration_time - Thi\u1ebft l\u1eadp th\u1eddi gian t\u00edch h\u1ee3p": [[20, "integration-time-thiet-lap-thoi-gian-tich-hop"]], "is_color(color_name) - Ki\u1ec3m tra m\u00e0u c\u1ee5 th\u1ec3": [[20, "is-color-color-name-kiem-tra-mau-cu-the"]], "is_object_present(threshold) - Ph\u00e1t hi\u1ec7n v\u1eadt th\u1ec3": [[25, "is-object-present-threshold-phat-hien-vat-the"]], "load_list(list_name) - T\u1ea3i danh s\u00e1ch th\u1ebb": [[24, "load-list-list-name-tai-danh-sach-the"]], "pin(pin_number) - \u0110\u1ecdc tr\u1ea1ng th\u00e1i m\u1ed9t c\u1ea3m bi\u1ebfn": [[23, "pin-pin-number-doc-trang-thai-mot-cam-bien"]], "port - \u0110\u1ecdc tr\u1ea1ng th\u00e1i port (4 bit cao)": [[23, "port-doc-trang-thai-port-4-bit-cao"]], "readID(detail=False) - \u0110\u1ecdc ID th\u1ebb": [[24, "readid-detail-false-doc-id-the"]], "read_all() - \u0110\u1ecdc tr\u1ea1ng th\u00e1i t\u1ea5t c\u1ea3 c\u1ea3m bi\u1ebfn": [[23, "read-all-doc-trang-thai-tat-ca-cam-bien"]], "scan_and_add_card(list_name) - Qu\u00e9t v\u00e0 th\u00eam th\u1ebb": [[24, "scan-and-add-card-list-name-quet-va-them-the"]], "scan_and_check(list_name) - Ki\u1ec3m tra th\u1ebb c\u00f3 trong danh s\u00e1ch": [[24, "scan-and-check-list-name-kiem-tra-the-co-trong-danh-sach"]], "scan_and_remove_card(list_name) - X\u00f3a th\u1ebb kh\u1ecfi danh s\u00e1ch": [[24, "scan-and-remove-card-list-name-xoa-the-khoi-danh-sach"]], "scan_card() - Qu\u00e9t th\u1ebb nhanh": [[24, "scan-card-quet-the-nhanh"]], "scroll(text, delay) - Cu\u1ed9n text": [[22, "scroll-text-delay-cuon-text"]], "show(text, colon) - Hi\u1ec3n th\u1ecb text": [[22, "show-text-colon-hien-thi-text"]], "show_hex(value) - Hi\u1ec3n th\u1ecb s\u1ed1 hex": [[22, "show-hex-value-hien-thi-so-hex"]], "show_number(number) - Hi\u1ec3n th\u1ecb s\u1ed1 nguy\u00ean": [[22, "show-number-number-hien-thi-so-nguyen"]], "show_numbers(num1, num2, colon) - Hi\u1ec3n th\u1ecb hai s\u1ed1 v\u1edbi d\u1ea5u hai ch\u1ea5m": [[22, "show-numbers-num1-num2-colon-hien-thi-hai-so-voi-dau-hai-cham"]], "show_temperature(temp) - Hi\u1ec3n th\u1ecb nhi\u1ec7t \u0111\u1ed9": [[22, "show-temperature-temp-hien-thi-nhiet-do"]], "tagPresent() - Ki\u1ec3m tra c\u00f3 th\u1ebb kh\u00f4ng": [[24, "tagpresent-kiem-tra-co-the-khong"]], "\u0110i\u1ec1u khi\u1ec3n Servo theo g\u00f3c nghi\u00eang": [[17, "dieu-khien-servo-theo-goc-nghieng"], [17, "id2"]], "\u0110i\u1ec1u khi\u1ec3n b\u1eb1ng n\u00fat A/B": [[16, "dieu-khien-bang-nut-a-b"]], "\u0110i\u1ec1u khi\u1ec3n c\u01a1 b\u1ea3n": [[22, "dieu-khien-co-ban"]], "\u0110i\u1ec1u khi\u1ec3n servo": [[15, "dieu-khien-servo"]], "\u0110i\u1ec1u khi\u1ec3n servo b\u1eb1ng n\u00fat A/B": [[16, "dieu-khien-servo-bang-nut-a-b"]], "\u0110i\u1ec1u khi\u1ec3n servo xoay g\u00f3c": [[15, "dieu-khien-servo-xoay-goc"]], "\u0110i\u1ec1u khi\u1ec3n t\u1eebng motor ri\u00eang": [[15, "dieu-khien-tung-motor-rieng"]], "\u0110i\u1ec1u khi\u1ec3n \u0111\u1ed9 s\u00e1ng": [[22, "dieu-khien-do-sang"]], "\u0110\u0103ng k\u00fd s\u1ef1 ki\u1ec7n nh\u1ea5n": [[19, "dang-ky-su-kien-nhan"]], "\u0110\u0103ng k\u00fd s\u1ef1 ki\u1ec7n nh\u1ea5n gi\u1eef (long press)": [[19, "dang-ky-su-kien-nhan-giu-long-press"]], "\u0110\u0103ng k\u00fd s\u1ef1 ki\u1ec7n th\u1ea3": [[19, "dang-ky-su-kien-tha"]], "\u0110\u1ecdc c\u00f9ng l\u00fac c\u1ea3 hai gi\u00e1 tr\u1ecb": [[21, "doc-cung-luc-ca-hai-gia-tri"]], "\u0110\u1ecdc c\u1ea3m bi\u1ebfn \u0111\u01a1n l\u1ebb": [[23, "doc-cam-bien-don-le"]], "\u0110\u1ecdc d\u1eef li\u1ec7u m\u00e0u": [[20, "doc-du-lieu-mau"]], "\u0110\u1ecdc nhi\u1ec7t \u0111\u1ed9 v\u00e0 \u0111\u1ed9 \u1ea9m": [[21, "doc-nhiet-do-va-do-am"]], "\u0110\u1ecdc th\u1ebb c\u01a1 b\u1ea3n": [[24, "doc-the-co-ban"]], "\u0110\u1ecdc t\u1ea5t c\u1ea3 c\u1ea3m bi\u1ebfn": [[23, "doc-tat-ca-cam-bien"]], "\u0110\u1ed1i t\u01b0\u1ee3ng bee trong Python": [[0, "doi-tuong-bee-trong-python"]], "\u0110\u1ed1i t\u01b0\u1ee3ng ph\u1ee5c v\u1ee5": [[26, "doi-tuong-phuc-vu"]], "\u0110\u1ed5i m\u00e0u ng\u1eabu nhi\u00ean (n\u00e2ng cao)": [[11, "doi-mau-ngau-nhien-nang-cao"]], "\u0110\u1ed9ng c\u01a1": [[15, null]], "\u1ee8ng d\u1ee5ng th\u1ef1c t\u1ebf": [[19, "ung-dung-thuc-te"]], "\ud83d\udca1 C\u00e1c v\u00ed d\u1ee5 c\u01a1 b\u1ea3n": [[10, null]], "\ud83d\udd39 Ch\u1ea1y robot ti\u1ebfn & d\u1eebng": [[15, "chay-robot-tien-dung"]]}, "docnames": ["1.bee-board-v2/1.index", "1.bee-board-v2/2.getting-started", "1.bee-board-v2/3.hardware-overview", "1.bee-board-v2/4.programming-guide", "1.bee-board-v2/5.troubleshooting", "2.bee-ide/1.index", "2.bee-ide/2.installation", "2.bee-ide/3.user-interface", "2.bee-ide/4.flashing-guide", "2.bee-ide/5.flashing-image", "3.examples/1.index", "3.examples/2.led-example", "3.examples/3.button-example", "3.examples/4.buzzer-example", "3.examples/5.oled-example", "3.examples/6.motor-example", "3.examples/7.servo-example", "3.examples/8.imu-example", "4.extensions/1.index", "4.extensions/button", "4.extensions/color-detect", "4.extensions/dht11", "4.extensions/led-segment", "4.extensions/line-detect", "4.extensions/rc522", "4.extensions/ultrasonic", "5.about/index", "index"], "envversion": {"sphinx": 62, "sphinx.domains.c": 3, "sphinx.domains.changeset": 1, "sphinx.domains.citation": 1, "sphinx.domains.cpp": 9, "sphinx.domains.index": 1, "sphinx.domains.javascript": 3, "sphinx.domains.math": 2, "sphinx.domains.python": 4, "sphinx.domains.rst": 2, "sphinx.domains.std": 2, "sphinx.ext.intersphinx": 1, "sphinx.ext.todo": 2, "sphinx.ext.viewcode": 1}, "filenames": ["1.bee-board-v2/1.index.md", "1.bee-board-v2/2.getting-started.md", "1.bee-board-v2/3.hardware-overview.md", "1.bee-board-v2/4.programming-guide.md", "1.bee-board-v2/5.troubleshooting.md", "2.bee-ide/1.index.md", "2.bee-ide/2.installation.md", "2.bee-ide/3.user-interface.md", "2.bee-ide/4.flashing-guide.md", "2.bee-ide/5.flashing-image.md", "3.examples/1.index.md", "3.examples/2.led-example.md", "3.examples/3.button-example.md", "3.examples/4.buzzer-example.md", "3.examples/5.oled-example.md", "3.examples/6.motor-example.md", "3.examples/7.servo-example.md", "3.examples/8.imu-example.md", "4.extensions/1.index.md", "4.extensions/button.md", "4.extensions/color-detect.md", "4.extensions/dht11.md", "4.extensions/led-segment.md", "4.extensions/line-detect.md", "4.extensions/rc522.md", "4.extensions/ultrasonic.md", "5.about/index.md", "index.md"], "indexentries": {}, "objects": {}, "objnames": {}, "objtypes": {}, "terms": {"": [23, 24], "0": [0, 1, 2, 3, 4, 5, 6, 7, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 23, 24, 25], "000000": 20, "00ff": 22, "02": 23, "02d": 22, "04": 24, "04b": 23, "05": [12, 13, 14, 16, 17, 23, 24, 25], "06": 24, "0987845231": [4, 26], "0f": 23, "0x23": 23, "0x24": 23, "0x29": 20, "0x2c": 24, "0x2d": 24, "0x2e": 24, "0x2f": 24, "0x3c": 14, "0xabcd": 22, "1": [5, 19, 20, 21, 22, 23, 24], "10": [6, 20, 21, 22, 23, 24, 25], "100": [20, 22], "1000": 19, "10000": 22, "1000m": 19, "100m": 24, "100nf": 19, "106": 24, "10a1": 24, "10a2": 24, "10hz": 22, "10mm": 23, "10nf": 19, "11": [2, 6], "12": [2, 20, 22, 23, 24], "120": [16, 25], "123": 22, "1234": 22, "128x64": 0, "13": 24, "135": 20, "14": 22, "14500": 2, "15": [20, 22, 23, 24, 25], "150": [16, 22], "15ma": 25, "16": [2, 10, 20, 24, 26], "165": [22, 24], "16mm": 20, "16x": 20, "180": [15, 16, 17, 20], "181": 16, "18650": 2, "19": 4, "1f": [14, 17, 21, 22, 23, 25], "1khz": 22, "1m": 23, "1x": 20, "2": [5, 10, 20, 21, 22, 23, 24, 25], "20": [20, 21, 22, 23, 24, 25], "200": 19, "2000": 19, "200m": 20, "20hz": 23, "20ma": 23, "20mm": [20, 25], "215": 24, "216": 24, "22": 22, "24": 23, "240": 2, "240mhz": 2, "25": [14, 20, 22, 23, 24, 25], "255": [0, 1, 2, 3, 4, 6, 11, 12, 13, 16, 17, 20, 21, 22, 23, 24, 25], "26ma": 24, "2a": 4, "2cm": 25, "3": [19, 20, 21, 22, 23, 24, 25], "30": [16, 20, 21, 22, 23, 24, 25], "300": 25, "3000k": 20, "300m": 19, "300mm": 25, "30cm": 25, "32": 21, "34": 22, "35": [20, 22, 23, 24, 25], "36": 23, "38khz": 23, "38m": 25, "3mm": 25, "3v": [18, 19, 20, 21, 22, 23, 24, 25], "3v3": 19, "4": [0, 5, 10, 20, 22, 25], "40": [20, 22], "4000mm": 25, "40khz": 25, "440": 25, "45": [20, 24], "48": 23, "4m": [20, 25], "4v": [0, 15], "4x": 20, "5": [0, 11, 13, 15, 16, 20, 21, 22, 23, 24, 25], "50": [0, 3, 5, 20, 21, 22, 23, 24, 25], "50hz": 23, "50m": 20, "50mm": 24, "512": 2, "56": 22, "56mhz": 24, "5cm": [21, 24], "5v": [0, 2, 4, 15, 21, 22, 23, 25], "5\u03bca": 20, "6": [0, 5, 13, 18, 20, 22], "60": [0, 2, 3, 14, 15, 16, 20, 22], "60x": 20, "614m": 20, "64": 14, "65\u03bca": 20, "6v": 16, "7": [0, 14, 22], "70": [15, 20, 25], "7k\u03c9": [20, 23], "8": [0, 15, 22, 23, 25, 26], "80": [4, 15], "80ma": 22, "8888": 22, "8mm": 23, "9": 22, "90": [0, 2, 15, 17, 20, 21, 24], "96": [0, 2, 10, 14], "9999": 22, "99999": 22, "A": [0, 2, 3, 5, 7, 10, 14, 15, 18, 19, 20, 22, 23, 24], "IN": 24, "NOT": 24, "No": 24, "The": 1, "_": [20, 22], "__init__": [20, 22, 23, 24, 25], "__main__": [20, 22, 23, 24, 25], "__name__": [20, 22, 23, 24, 25], "a3": 24, "a4": [20, 22, 23, 24], "a5": 25, "a6": 25, "ab": 23, "abcd": 22, "absent": 24, "absent_count": 24, "academi": 26, "acceleromet": 17, "access": [24, 26], "access_control": 24, "active_sensor": 23, "ad": 24, "adc": [2, 18, 20], "add": 24, "add_new_card": 24, "add_stud": 24, "address": [23, 24], "adjust_bright": 22, "advanc": [0, 3, 5, 7, 10], "again": 20, "agv": 23, "ai": [2, 3, 5, 7, 14, 18, 26, 27], "air": 1, "all": 23, "alreadi": 24, "already_pres": 24, "an": 25, "analog": [0, 2, 3, 5, 7, 10, 18, 22], "analog_read": 22, "angl": [3, 16, 17], "anh": 7, "ani": [23, 24], "antenna": 24, "api": [19, 21, 25, 27], "append": [20, 24], "art": 20, "ascii": 22, "assist": [3, 5], "asw": 24, "attend": 24, "attendance_": 24, "attendance_log": 24, "attendance_record": 24, "attendancesystem": 24, "author": 24, "authorized_card": 24, "autom": [20, 23], "averag": 23, "avoid_obstacl": 25, "b": [0, 2, 3, 4, 5, 7, 10, 11, 14, 18, 19, 20, 22, 24, 25], "b2": 24, "b4": 24, "backup": 24, "bao": 19, "bar": 18, "bar_width": 25, "base": 19, "base_spe": 23, "be_quiet": [4, 6, 12, 13, 17, 25], "bee": [2, 8, 9, 20, 22, 23, 24, 25], "beebrain": [0, 1, 3, 4, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24], "beebuzz": 25, "beecolordetect": 27, "beedcmotor": 25, "beeledseg": 27, "beelinedetect": 27, "beemotor": 25, "beemus": 25, "beeneopixel": 25, "beeol": 25, "beep": [4, 19, 25], "beerc522": 27, "beestem": 4, "beestemsolut": [1, 3, 4, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 26], "beeultrason": 27, "bh1750": 18, "bit": [2, 20], "bi\u1ebfn": [0, 2, 3, 5, 7, 10, 15, 16, 18, 19, 22, 26, 27], "bi\u1ebft": [8, 9, 13, 15], "bi\u1ec3u": [3, 5, 6, 7, 21, 25], "bi\u1ec7t": [5, 18, 20, 22, 23], "black": 20, "ble": 2, "blind": 25, "block": [3, 4, 20, 22, 23, 24, 25], "blockli": [3, 6, 8, 11, 15, 26], "blog": 1, "blue": [20, 25], "bluetooth": [0, 2, 5, 7, 10], "bo": [0, 1, 2, 11, 12, 14, 15, 16, 17, 26], "board": [2, 3, 4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 22, 25, 26], "boot": [2, 9], "br": 22, "brain": 2, "brake": 25, "break": [20, 22, 23, 24], "broker": 21, "btn1": 19, "btn2": 19, "btn_down": 19, "btn_up": 19, "bu": 2, "button": [0, 2, 3, 5, 7, 9, 10, 13, 15, 16, 18, 19, 22, 24, 26], "button_a": [20, 22, 23, 24], "button_b": [20, 22, 23, 24], "buttona": [0, 2, 3, 4, 10, 11, 12, 13, 14, 15, 16], "buttonb": [0, 2, 10, 12, 16], "buzzer": [0, 2, 3, 5, 6, 7, 10, 12, 17, 18, 20, 22, 23, 24, 25, 26, 27], "bu\u1ed5i": [5, 26], "bu\u1ed9c": 6, "b\u00e0i": [7, 11, 12, 14, 15, 16, 17], "b\u00e1o": [3, 4, 5, 7, 9, 10, 13, 17, 19, 20, 21, 22, 24, 25], "b\u00ean": [1, 5, 14, 18], "b\u00ecnh": [4, 6, 20, 22, 23, 25], "b\u00edp": [6, 12, 17], "b\u01b0\u1edbc": [4, 15, 16], "b\u1ea1i": 4, "b\u1ea1n": [4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19], "b\u1ea3n": [0, 2, 5, 6, 7, 13, 17, 26], "b\u1ea3ng": 22, "b\u1ea3o": [4, 6, 18, 19, 20, 21, 22, 23, 24, 25], "b\u1ea5m": [12, 20, 22], "b\u1ea5t": 5, "b\u1eadt": [1, 2, 4, 7, 13, 17], "b\u1eaft": [2, 3, 5, 6, 7, 9, 20, 24, 27], "b\u1eb1ng": [0, 3, 6, 8, 9, 10, 11, 12, 13, 14, 15, 17, 20, 21, 22, 23, 24, 25, 26, 27], "b\u1ec1": 25, "b\u1ec3": 25, "b\u1ecb": [2, 3, 4, 5, 6, 7, 8, 17, 22, 23], "b\u1ed1": [7, 23], "b\u1ed9": [2, 12, 15, 21, 26, 27], "b\u1edfi": [0, 2, 5], "c": [1, 2, 3, 4, 5, 6, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 20, 21, 22, 24, 25], "c1": 24, "c3": [20, 24], "c4": [20, 22, 23, 24, 25], "c5": [0, 2, 3, 4, 5, 6, 12, 13, 23, 24, 25], "c6": [13, 17], "cai": 1, "calc": 22, "calcul": [22, 23], "calculate_pid": 23, "calculator_oper": 22, "calculator_valu": 22, "cam": [22, 24], "camera": 18, "cao": [1, 2, 3, 26], "card": 24, "card_cooldown": 24, "card_id": 24, "card_info": 24, "centimet": 25, "ch340": [1, 6], "ch340c": [1, 2, 4], "chao": 0, "che": [20, 24], "check": 20, "check_attend": 24, "checksum": 21, "chi": [20, 24], "chia": [5, 7], "chip": [1, 2, 6, 16, 20, 22, 23, 24], "chi\u1ebfm": 4, "chi\u1ebfu": 20, "chi\u1ec1u": [15, 18], "cho": [0, 2, 3, 4, 6, 7, 8, 9, 10, 15, 18, 20, 21, 22, 23, 24, 25, 26, 27], "choic": [13, 25], "chrome": [1, 6, 11, 12, 13, 14, 17], "chromebook": [5, 6], "chu": [20, 21], "chung": [3, 5, 7, 18, 25], "chuy\u00ean": 23, "chuy\u1ec3n": [1, 2, 3, 5, 7, 10, 14, 15, 16, 17, 18, 22, 23, 25, 26], "chu\u00f4ng": 3, "chu\u1ea9n": [2, 6, 25], "chu\u1ed7i": [19, 24], "chu\u1ed9t": 7, "ch\u00e0o": [1, 12, 14, 27], "ch\u00e2n": [18, 19, 21], "ch\u00e8n": [11, 13], "ch\u00e9o": 1, "ch\u00ednh": [2, 7, 11, 12, 14, 15, 16, 17, 18, 22, 23, 24, 26, 27], "ch\u00f3ng": 4, "ch\u00fa": [2, 7, 11, 12, 13, 14, 15, 16, 17, 18], "ch\u00fang": 26, "ch\u00fat": [23, 25], "ch\u01a1i": [12, 19, 22, 24], "ch\u01b0a": [0, 1, 4, 6, 24], "ch\u01b0\u01a1ng": [0, 2, 4, 5, 7, 12, 15, 16, 20, 22, 23, 24, 25, 26, 27], "ch\u1ea1m": 26, "ch\u1ea1y": [0, 1, 2, 3, 4, 5, 7, 13, 20, 22, 23, 24, 25], "ch\u1ea5m": 24, "ch\u1ea5t": [6, 20, 22], "ch\u1eadm": 18, "ch\u1eadp": 4, "ch\u1eafc": [1, 8, 9, 23], "ch\u1eafn": [1, 8, 9, 20, 23], "ch\u1ebf": [2, 4, 19, 22, 24, 25, 26], "ch\u1ec9": [1, 3, 4, 5, 6, 8, 10, 14, 18, 19, 20, 22, 23, 26], "ch\u1ec9nh": [19, 20, 22, 23, 24, 25], "ch\u1ecdn": [0, 1, 3, 4, 5, 6, 7, 8, 9, 13, 14, 15, 18, 22, 23, 25], "ch\u1ed1i": 24, "ch\u1ed1ng": 19, "ch\u1edd": [7, 13, 14, 15, 16, 20, 23, 24], "ch\u1ee7": [24, 26], "ch\u1ee9": 26, "ch\u1ee9a": [3, 5, 7], "ch\u1ee9c": [2, 3, 5, 7, 24], "ch\u1ee9ng": 4, "ch\u1eef": [4, 5, 6, 22], "class": [20, 22, 23, 24, 25], "classic": 24, "cleanup": 20, "clear": [0, 4, 14, 17, 18, 19, 20, 21, 23, 24, 25], "click": 18, "clk": 22, "clock": 22, "code": [1, 2, 3, 5, 6, 7, 8, 10, 11], "colon_st": 22, "color": [18, 20, 22, 25], "color_count": 20, "color_info": 20, "color_read": 20, "color_sensor": 20, "color_stat": 20, "colorsortingrobot": 20, "com": [1, 3, 4, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 26], "com3": [0, 1, 6], "comment": 7, "commun": 26, "complet": 20, "comx": [3, 5, 6, 8], "con": [0, 17, 26], "cone": 25, "conf": 20, "confid": 20, "confirm": 24, "cong": 23, "connect": [4, 7], "connot": 4, "consol": [3, 4], "contact": 26, "continu": 23, "control": [19, 20, 22, 23, 24], "convers": 20, "convert": 22, "cooldown": 24, "core": 2, "correct": 20, "corrupt": 24, "count": 20, "counter": 22, "counter_valu": 22, "cpu": 2, "creativ": 26, "cross": 23, "ctrl": 7, "cu": [0, 1, 5, 6, 8], "cung": [23, 25], "current": [19, 20, 22], "current_bright": 22, "current_gain": 20, "current_item": 20, "current_mod": 22, "current_test": 20, "current_tim": [20, 22, 24], "cu\u1ed1i": 23, "cu\u1ed9c": 23, "cu\u1ed9n": [7, 14], "cyan": [20, 22, 23, 24], "cycl": 20, "c\u00e0i": [0, 1, 2, 3, 4, 5, 7, 8, 9, 27], "c\u00e0ng": 19, "c\u00e1": [24, 25], "c\u00e1c": [3, 4, 6, 7, 11, 12, 13, 14, 15, 16, 18, 19, 20, 22, 23, 24], "c\u00e1ch": [0, 3, 4, 6, 8, 9, 11, 12, 13, 14, 15, 16, 17, 20, 21, 23, 24, 27], "c\u00e1i": 22, "c\u00e1nh": [2, 16], "c\u00e1o": 24, "c\u00e1p": [0, 1, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17], "c\u00e2u": [11, 12, 13, 14, 15, 16, 17], "c\u00e2y": 12, "c\u00f2i": [13, 19], "c\u00f2n": [18, 26], "c\u00f3": [2, 3, 4, 5, 6, 7, 9, 10, 18, 19, 20, 21, 22, 25, 26], "c\u00f4": 26, "c\u00f4ng": [1, 3, 4, 5, 6, 8, 18, 24, 25, 26], "c\u00f9ng": [4, 6, 11, 12, 15, 16, 17, 26, 27], "c\u00fa": [1, 3, 4, 19], "c\u0169": 4, "c\u01a1": [0, 2, 5, 13, 26, 27], "c\u01b0ng": 24, "c\u1ea1nh": 18, "c\u1ea3": [2, 4, 6, 7, 10, 11, 14, 18, 22, 24], "c\u1ea3m": [0, 2, 3, 5, 7, 10, 15, 16, 18, 19, 21, 22, 27], "c\u1ea3n": [3, 10, 15, 26], "c\u1ea3nh": [7, 10, 13, 17, 19, 20, 21, 25], "c\u1ea5p": [0, 3, 4, 5, 6, 15, 18, 25], "c\u1ea5u": [3, 5, 21], "c\u1ea7n": [5, 6, 9, 18, 20, 22], "c\u1eadp": [2, 4, 5, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 22, 23, 25], "c\u1eady": 20, "c\u1eafm": [3, 4, 5, 6, 8, 9, 16, 19, 21], "c\u1ed5ng": [0, 1, 3, 4, 5, 6, 7, 8, 9, 15, 16, 19, 20, 21], "c\u1ed9ng": [22, 26], "c\u1ed9t": [14, 21], "c\u1ee1": 14, "c\u1ee5": [3, 5, 26], "c\u1ee5c": 7, "c\u1ee7a": [3, 7, 13, 14, 17, 18, 19, 20, 21, 23, 25], "c\u1ee9ng": [0, 4, 5, 6, 10, 18, 19, 27], "c\u1eeda": [3, 24], "d": 24, "d2": 24, "d4": 24, "d5": 13, "danger": 25, "danger_dist": 25, "danh": [3, 5, 10, 12], "dash": 22, "dashboard": [14, 21, 26], "data": 4, "databas": 24, "datasheet": [20, 22, 23, 24, 25], "dc": [0, 2, 10, 15], "dcmotor": 23, "debounc": [18, 19, 20, 22], "decis": 25, "def": [19, 20, 22, 23, 24, 25], "defin": 4, "delai": 25, "delet": 7, "demo": [20, 22], "deni": 24, "deriv": 23, "detail": 20, "detect": [18, 20, 23], "detect_and_classify_color": 20, "detect_intersect": 23, "detect_item_pres": 20, "detected_color": 20, "dev": [0, 1, 5, 6, 8], "devic": 6, "dht": 21, "dht11": [3, 10, 14, 18, 27], "di": [1, 2, 3, 5, 10, 14, 15, 17, 20, 23, 25, 26], "dialog": 9, "digit": [2, 7, 10, 18, 19, 21, 22], "digital_clock": 22, "digital_read": 2, "dio": 22, "displai": [0, 2, 3, 5, 10], "display_dist": 25, "display_statu": 20, "distanc": 25, "distance_monitor": 25, "di\u1ec5n": 25, "di\u1ec7n": [1, 26, 27], "dmesg": 6, "do": 1, "doc": 27, "document": [19, 21, 24, 25], "down": 19, "draw_rect": 25, "driver": [1, 2, 4, 8, 9], "dual": 2, "dui": [1, 3, 26], "dump": [20, 24], "dung": [4, 14, 22, 26], "durat": 13, "duy\u1ec7t": [1, 4, 5, 6, 7, 11, 12, 13, 14, 17], "dynam": 22, "d\u00e0i": [4, 22], "d\u00e0ng": [18, 21, 26], "d\u00e0nh": [0, 1, 2, 26, 27], "d\u00e1n": [4, 6], "d\u00e2y": [0, 1, 2, 3, 4, 5, 6, 8, 18, 19, 21, 22], "d\u00f2": [18, 23], "d\u00f2ng": [4, 6, 10, 20, 22, 23, 24, 25], "d\u00f5i": [3, 5, 20, 23], "d\u00f9ng": [0, 1, 3, 4, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20], "d\u01a1i": 25, "d\u01b0": 24, "d\u01b0\u01a1ng": [12, 20, 22, 23, 24, 25], "d\u01b0\u1edbi": [1, 10], "d\u1ea1ng": [18, 24], "d\u1ea1y": [2, 26], "d\u1ea3i": [18, 21, 25], "d\u1ea5u": 4, "d\u1ea7n": [1, 3, 11, 13, 16], "d\u1eabn": [0, 1, 2, 6, 27], "d\u1eb9p": 23, "d\u1ec5": [5, 18, 20, 21, 26], "d\u1ecdn": 23, "d\u1ed9i": 19, "d\u1ee5": [1, 2, 7, 8, 16, 18, 26], "d\u1ee5c": [20, 23, 27], "d\u1ee5ng": [0, 2, 3, 4, 6, 12, 14, 17, 20, 22, 23, 24, 25, 26], "d\u1eebng": [13, 16, 20, 23, 25], "d\u1eef": [4, 6, 14, 17, 18, 22, 26], "d\u1ef1": [0, 7, 10, 19, 20, 21, 23, 26], "d\u1ef1a": [20, 23, 25, 26], "d\u1ef1ng": [3, 5, 7, 12, 26], "e": [1, 20, 22, 24], "e3": 24, "e4": [20, 22, 23, 24, 25], "e5": [13, 23, 24], "echo": 25, "edg": [1, 6, 11, 12, 13, 14, 17], "editor": 3, "elif": [12, 16, 20, 22, 23, 24, 25], "els": [12, 17, 19, 20, 21, 22, 23, 24, 25], "em": [3, 5, 26, 27], "email": [4, 26, 27], "encod": 22, "encode_char": 22, "end": [24, 25], "end_sess": 24, "enodev": 4, "enumer": [19, 20, 23], "environment": 22, "equal": 20, "errno": 4, "error": [9, 20, 21, 22, 23, 24], "esp32": [0, 14, 19, 21, 26], "ex": 6, "exampl": 10, "except": [20, 22, 23, 24, 25], "exist": 24, "ext_btn": 19, "extens": 18, "extern": 19, "f": [14, 17, 18, 19, 20, 21, 22, 23, 24, 25], "f4": 20, "f5": 13, "fa": 13, "facebook": [4, 26, 27], "fail": 7, "fake_valu": 22, "fallback": 23, "fals": [20, 22, 23], "featur": 22, "feedback": [20, 22, 23, 24], "feeder": 24, "ff0000": 20, "fi": [0, 2, 3, 7, 8], "file": [1, 3, 4, 5, 6, 20], "filenam": 24, "finish": 23, "firmwar": [2, 18, 27], "flash": 2, "follow": 23, "follow_lin": 23, "forev": [20, 22, 23, 24, 25], "format": 24, "forward": [23, 25], "frame": 2, "from": [0, 1, 3, 4, 6, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "function": 19, "g": [3, 4, 11, 20, 25], "g4": [20, 22, 23, 24, 25], "g5": [13, 23], "gain": 23, "game": [12, 19, 20, 22], "game_scor": 22, "getattr": 22, "ghi": [0, 2, 3, 4, 5, 7, 10, 11, 12, 13, 14, 15, 16, 17, 18, 24], "gh\u00e9p": [3, 5, 7], "gia": [0, 17, 26], "giai": [0, 3, 5, 12, 13], "gian": [3, 10, 11, 14, 19, 21, 22, 23, 24, 25], "giao": [0, 1, 6, 10, 14, 18, 27], "gi\u00e1": [4, 11, 14, 17, 19, 20, 22, 23, 24, 25], "gi\u00e1o": [7, 20, 23, 27], "gi\u00e2i": [3, 4, 5, 7, 11, 12, 13, 14, 16, 18, 19, 21, 22, 23, 24, 25], "gi\u00fap": [0, 1, 3, 4, 5, 7, 10, 15, 17, 18, 19, 20, 26], "gi\u1ea3": [22, 23], "gi\u1ea3i": [3, 5, 7, 18], "gi\u1ea3m": [22, 23, 25], "gi\u1ea3n": [13, 19, 22, 24], "gi\u1ea3ng": 26, "gi\u1ecdng": 26, "gi\u1ed1ng": [1, 3], "gi\u1edbi": [1, 5, 6, 26], "gi\u1edd": 22, "gi\u1eef": [9, 12, 17], "gi\u1eefa": [4, 11, 13, 15, 16, 19, 20, 22, 23, 24, 25], "global": [19, 20, 22, 23, 24], "gmail": 4, "gnd": [18, 19, 21], "got": 20, "gpio": [2, 12, 19, 21], "grant": 24, "green": [20, 24, 25], "grep": 6, "ground": 18, "grove": [0, 2, 3, 26], "guid": [20, 22, 23], "gyroscop": [0, 3, 5, 17], "gyroz": 17, "g\u00ec": 26, "g\u00f3c": [0, 7, 14, 18, 20, 23, 25], "g\u00f3i": 26, "g\u00f5": 5, "g\u0169i": 26, "g\u1ea5p": 25, "g\u1ea7n": [3, 21, 24, 26], "g\u1eadt": 16, "g\u1eafn": [0, 15, 19], "g\u1eb7p": [4, 7, 15], "g\u1ecdi": [4, 16, 22], "g\u1ed3m": [18, 19, 26, 27], "g\u1ee3i": [1, 7, 11, 12, 13, 14, 15, 16, 17, 26], "g\u1eedi": 21, "h": [18, 21, 24], "hai": [1, 3, 6, 15, 27], "handl": [20, 23], "handle_intersect": 23, "hasattr": [23, 24], "hc": 25, "hello": [0, 1, 2, 3, 5, 22], "heo": 25, "hex_color": 20, "hi": 22, "high": 19, "hi\u1ec3m": 25, "hi\u1ec3n": [1, 2, 4, 5, 6, 7, 8, 10, 12, 18, 19, 20, 23, 24, 25], "hi\u1ec3u": [5, 7], "hi\u1ec7n": [1, 3, 5, 7, 10, 12, 19, 22, 24], "hi\u1ec7u": [0, 2, 3, 10, 13, 16, 19, 20, 21, 22, 23, 25], "hotlin": [4, 26, 27], "hour": 22, "ho\u00e0n": [4, 5, 6, 7, 20, 23], "ho\u1ea1t": [1, 3, 4, 5, 15, 20, 21, 22, 24, 25], "ho\u1eb7c": [0, 1, 2, 3, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 23, 24, 25, 26, 27], "hsv": 20, "http": [1, 3, 4, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 26], "hub": 4, "humid": 21, "huynh": 26, "hu\u1ed1ng": 18, "h\u00e0m": [14, 17, 19, 22, 23], "h\u00e0ng": 23, "h\u00e0nh": [2, 6, 12, 14, 16, 17, 25, 26], "h\u00e3y": [1, 4, 7, 8, 9, 12, 13, 15, 16, 17], "h\u00ecnh": [0, 2, 3, 5, 8, 10, 15, 17, 19, 21, 26, 27], "h\u01a1n": [15, 18, 19, 20, 22, 24, 25], "h\u01b0\u1edbng": [0, 1, 2, 6, 10, 15, 17, 23, 25, 26, 27], "h\u01b0\u1edfng": 25, "h\u1ea1n": [4, 22, 23], "h\u1ea7u": 6, "h\u1eb3n": 23, "h\u1ebft": [6, 22], "h\u1ec7": [3, 18, 19, 20, 23, 25], "h\u1ecda": 0, "h\u1ecdc": [0, 1, 2, 3, 7, 10, 11, 12, 13, 14, 15, 16, 17, 18, 22, 24, 27], "h\u1ecfi": [4, 6, 7, 11, 12, 13, 14, 15, 16, 17], "h\u1ecfng": [0, 22, 24], "h\u1ed3i": [4, 12, 17, 20, 23], "h\u1ed3ng": 23, "h\u1ed7": [1, 2, 3, 5, 6, 7, 8, 9, 10, 13, 18, 19, 21, 22, 24, 25, 26], "h\u1ed9p": 20, "h\u1ee3p": [0, 2, 5, 7, 11, 14, 15, 17, 19, 21, 22, 23, 24, 25, 26], "h\u1eefu": 20, "i": [4, 19, 20, 23, 24], "i2c": [2, 14, 15, 16, 18], "icon": [0, 14], "id": [0, 2, 6, 7, 8, 9, 20, 22, 23, 25, 26], "id_format": 24, "imag": 14, "import": [0, 1, 3, 4, 6, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25], "imu": [0, 2, 3, 5, 7, 10, 15, 16, 18, 26, 27], "inch": [0, 22], "info": 4, "init_be": [3, 4, 11, 15], "input": [10, 18, 19, 25], "instal": [6, 18, 20], "int": [17, 20, 22, 23, 25], "integr": [20, 23], "intens": 22, "interact": [20, 22], "interactive_color_test": 20, "interfac": 2, "intern": 2, "internet": 6, "interrupt": 19, "intersect": 23, "ion": [0, 2], "iot": [0, 10, 14, 18, 19, 20, 21, 25, 26], "ip": [0, 1, 3, 4, 5, 6, 7, 8], "ir": 23, "ir1": 23, "ir2": 23, "ir3": 23, "ir4": 23, "is_button_press": [20, 22, 23, 24], "is_press": [0, 2, 3, 4, 11, 12, 13, 14, 15, 16, 19], "is_releas": 19, "is_run": 20, "is_shak": [3, 15, 17], "item": [19, 20], "json": [5, 20], "kb": 2, "kbit": 24, "kd": 23, "kei": 20, "keyboardinterrupt": [20, 22, 23, 24, 25], "khi": [0, 3, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 20, 24, 26], "khi\u1ebfm": 20, "khi\u1ec3n": [0, 2, 3, 5, 7, 10, 11, 12, 13, 18, 19, 20, 23, 24, 25], "kho\u1ea3ng": [9, 15, 18, 20, 23, 24], "khu": [3, 5], "khung": [2, 15], "khuy\u1ebfn": 26, "khu\u1ea5t": 24, "kh\u00e1c": [6, 10, 11, 18, 19, 20, 22, 24, 25], "kh\u00e1m": 26, "kh\u00ed": 2, "kh\u00edch": 26, "kh\u00f3a": [19, 24], "kh\u00f4i": 7, "kh\u00f4ng": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 16, 17, 18, 26], "kh\u01a1i": 26, "kh\u1ea3": [18, 19], "kh\u1ea9u": [0, 1, 3, 4, 5, 6, 8], "kh\u1eafc": [0, 4, 6], "kh\u1ed1i": [1, 4, 6, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18], "kh\u1edbp": 7, "kh\u1edfi": [0, 1, 3, 4, 16], "ki": 23, "kid": 26, "kit": [22, 26], "ki\u1ebfm": 23, "ki\u1ebfn": [3, 27], "ki\u1ec3m": [0, 1, 3, 5, 7, 8, 9, 18, 21, 22, 25], "ki\u1ec3u": [1, 3], "ki\u1ec7n": [11, 20], "kp": 23, "k\u00e9m": 20, "k\u00e9o": [0, 5, 6, 7, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 26, 27], "k\u00ea": [20, 23, 24], "k\u00eanh": 26, "k\u00eau": [4, 17], "k\u00edch": [2, 20, 22], "k\u00fd": [2, 4, 14, 24], "k\u1ebb": 23, "k\u1ebf": [0, 3, 20, 23, 26], "k\u1ebft": [3, 7, 8, 9, 10, 18, 20, 22, 24, 25, 26], "k\u1ef3": [5, 21], "k\u1ef7": 26, "lai": 26, "lan": [4, 6], "lap": 22, "last_card_tim": 24, "last_error": 23, "last_posit": 23, "last_second": 22, "last_upd": 22, "le": 24, "learn": 26, "led": [1, 2, 5, 6, 7, 10, 14, 15, 17, 18, 20, 22, 23, 24, 25, 26, 27], "led1": [0, 1, 2, 3, 4, 5, 6, 10, 11, 12, 13, 15, 16, 17, 19, 20, 21, 22, 23, 24], "led2": [0, 2, 4, 10, 11], "left": [23, 25], "left_sensor": 23, "left_spe": 23, "lego": [0, 15, 16, 26], "len": [19, 20, 22, 24], "level": [22, 25], "li": [0, 2], "librari": 21, "light": 18, "line": [18, 20, 23], "line_sensor": 23, "linux": 5, "list": 24, "li\u00ean": [6, 11, 14, 17, 23, 24], "li\u1ec7u": [4, 6, 14, 17, 22, 25, 26, 27], "lo": 22, "loa": [0, 2, 12, 13, 26], "load": [7, 24], "load_stud": 24, "local": 4, "localtim": 22, "locker": 24, "log": [3, 4, 5, 24], "logic": [1, 7, 23, 25], "long": [18, 22], "loop": 23, "lost": 23, "lost_count": 23, "low": [19, 20], "lo\u1ea1i": [4, 18, 22, 24, 25], "lpt": 6, "lu\u00f4n": 1, "lx7": 2, "l\u00e0": [1, 2, 3, 4, 7, 10, 14, 19, 20, 21, 22, 23, 24, 25, 26], "l\u00e0m": [1, 2, 3, 4, 5, 10, 11, 12, 13, 14, 15, 16, 17, 23, 26], "l\u00e1": [12, 20], "l\u00e2u": 25, "l\u00ean": [1, 3, 6, 8, 9, 12, 13, 14, 18, 19, 24], "l\u00f2ng": 9, "l\u00f9i": [15, 25], "l\u00fac": 16, "l\u00fd": [3, 7, 10, 19, 20, 22, 23, 25, 27], "l\u01b0u": [3, 4, 5, 7, 8, 9, 20, 24], "l\u01b0\u1ee3ng": [6, 11, 12, 13, 14, 15, 16, 17, 20, 22, 23, 26], "l\u01b0\u1ee3t": [13, 15], "l\u1ea1i": [0, 1, 3, 4, 5, 6, 7, 11, 15, 16, 17, 21, 23, 24, 26], "l\u1ea1nh": [21, 22], "l\u1ea5y": [17, 20, 21, 22, 25], "l\u1ea7n": [1, 6, 11, 13, 15, 16, 18, 20, 21, 22, 23, 24, 25], "l\u1eabn": 3, "l\u1eadp": [5, 7, 8, 9, 26], "l\u1eafc": [0, 2, 3, 5, 7, 10], "l\u1eb7p": [4, 11, 12, 13, 14, 15, 16, 17, 20, 22, 23, 24], "l\u1ec7": [24, 25], "l\u1ec7ch": 23, "l\u1ec7nh": [0, 1, 4, 10, 11, 14, 15, 18], "l\u1ecdc": [19, 20, 25], "l\u1ecfng": [22, 23], "l\u1ed7": 2, "l\u1ed7i": [0, 3, 5, 7, 9, 27], "l\u1ed9": 23, "l\u1edbn": 25, "l\u1edbp": [3, 26], "m": [19, 20, 24], "m1": [0, 2, 3, 5, 10, 15], "m2": [0, 2, 10, 15], "mach": 1, "machin": [19, 25], "maco": 5, "magenta": 20, "main": 4, "make": [25, 26], "maker": 26, "manag": [6, 22, 24], "mang": 26, "map": 25, "mass": [18, 25], "max": [17, 20, 22, 23], "max_lost_count": 23, "max_search_tim": 23, "max_spe": 23, "maze": 23, "mb": 2, "mechan": 24, "melodi": 13, "menu": 14, "mhz": 2, "mi": 13, "microphon": 18, "micropython": [0, 2, 3, 19, 21], "mifar": 24, "mili": 19, "milimet": 25, "min": [17, 23, 25], "min_spe": 23, "minh": [0, 2, 3, 10, 16, 17, 25, 26], "mini": [3, 5, 10, 13, 14, 16, 17], "minut": 22, "mix": 20, "ml": 2, "mm": 2, "mode": [22, 25], "mode_": 22, "mode_calcul": 22, "mode_clock": 22, "mode_count": 22, "mode_funct": 22, "mode_gam": 22, "mode_nam": 22, "mode_sensor": 22, "mode_tim": 22, "modul": [2, 4, 5, 6, 7, 20, 22, 24, 25, 26], "monaco": [1, 3], "mong": [16, 23, 26], "monitor": [22, 25], "most_common": 20, "motion": 10, "motor": [0, 2, 3, 5, 7, 10, 18, 19, 21, 23, 25, 26], "motor1": [0, 2, 3, 10, 15], "motor2": [2, 10, 15], "motor_control": 23, "move": 23, "move_backward": 25, "move_down": 19, "move_forward": [0, 3, 15, 23, 25], "move_up": 19, "movement": [0, 3, 5], "mpu6050": [0, 2, 10, 17, 18], "mqtt": [0, 21], "multi": [20, 22, 23, 25], "multidisplaysystem": 22, "mu\u1ed1n": [16, 18, 23, 26], "m\u00e0": [4, 10, 14, 16, 23, 26], "m\u00e0n": [0, 2, 3, 5, 6, 8, 10, 17, 19, 21, 27], "m\u00e0u": [2, 4, 5, 7, 10, 12, 13, 17, 18, 21, 22, 25], "m\u00e1t": 21, "m\u00e1y": [0, 1, 2, 3, 4, 5, 6, 8, 9, 11, 12, 13, 14, 15, 16, 17, 20, 22, 24], "m\u00e3i": [11, 14, 15], "m\u00ea": [23, 26], "m\u00f4": [0, 2, 3, 5, 7, 8, 9, 10, 18, 19, 21, 24, 26], "m\u00f4i": [18, 20, 21, 22, 23, 25, 26], "m\u01b0\u1ee3t": [7, 14, 16, 23], "m\u1ea1ch": [0, 1, 2, 4, 11, 12, 14, 15, 16, 17, 26], "m\u1ea1ng": [0, 3, 4, 5, 6, 8], "m\u1ea1nh": [15, 17, 20, 23, 25], "m\u1ea5t": [9, 23], "m\u1eabu": [20, 24], "m\u1eadt": [0, 1, 3, 4, 5, 6, 8, 19, 24], "m\u1eafc": 7, "m\u1eaft": 18, "m\u1eb7c": [19, 20, 22, 23, 24], "m\u1eb7t": [23, 25], "m\u1ec1m": [1, 5, 6, 21, 25], "m\u1ecdi": [1, 5, 26], "m\u1ed7i": [3, 5, 7, 11, 13, 15, 16, 17, 18, 19, 21, 22, 26], "m\u1ed9t": [7, 13, 18, 25, 26], "m\u1edbi": [3, 4, 5, 6, 7, 18, 24], "m\u1edf": [0, 1, 2, 3, 5, 6, 7, 8, 9, 26], "m\u1ee5c": [3, 5, 26], "m\u1ee9c": [22, 25], "m\u1eebng": [1, 27], "n8r8": 2, "nam": [26, 27], "name": [7, 24], "nameerror": 4, "nap": 1, "navig": 23, "neopixel": [2, 18, 20, 22, 23, 24], "new": 24, "new_bright": 22, "nfc": [18, 24], "ngai": [4, 5, 10, 14], "ngang": 23, "nghe": [4, 6, 12, 13], "nghi\u00eang": [0, 2, 3, 5, 7, 10, 14, 16], "nghi\u1ec7m": [13, 26], "nghi\u1ec7p": 25, "ngh\u0129a": [4, 25], "ngh\u1ec7": [20, 25, 26], "ngo\u00e0i": [2, 4, 5, 18, 19, 20, 22, 25], "ngo\u1ea1i": [2, 23], "ngui": 25, "nguyen": 24, "nguy\u00ean": [4, 6, 26], "ngu\u1ed3n": [0, 6, 9, 15, 16, 18, 20, 21, 22, 25], "ng\u00f4n": [0, 3, 6, 7, 26], "ng\u0169": 4, "ng\u01b0\u1eddi": [3, 10, 12, 19, 20, 22, 26], "ng\u01b0\u1ee1ng": 20, "ng\u01b0\u1ee3c": [15, 22, 23], "ng\u1eabu": [13, 16, 19, 25], "ng\u1eafn": [4, 19, 22], "ng\u1eaft": [4, 9], "ng\u1eef": [0, 3, 6, 7, 26], "nhanh": [6, 12, 13, 19, 21, 22], "nhau": [3, 7, 10, 11, 15, 18, 19, 20, 21, 22, 25], "nhi\u00ean": [13, 16, 19, 25], "nhi\u1ec1u": [2, 4, 10, 13, 18, 20, 22, 25], "nhi\u1ec5u": [18, 19, 20, 22, 25], "nhi\u1ec7t": [3, 10, 14, 18, 20, 25], "nh\u00e0": [26, 27], "nh\u00e1y": [1, 4, 13, 16], "nh\u00e2n": [0, 2, 4, 6, 19, 20, 21, 22, 23, 24, 25], "nh\u00e9": [12, 13, 14, 16, 17], "nh\u00ecn": 25, "nh\u00f3m": [7, 10, 18, 19, 21, 26], "nh\u01b0": [6, 12, 13, 14, 15, 18, 25], "nh\u01b0ng": [4, 19], "nh\u1ea1c": [0, 1, 2, 3, 7, 10], "nh\u1ea1y": 20, "nh\u1ea3": 9, "nh\u1ea5n": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 18, 27], "nh\u1ea5p": [1, 4, 13, 16], "nh\u1ea5t": [6, 18, 20, 21, 23, 25, 26], "nh\u1eadn": [1, 4, 6, 7, 10, 18, 24, 26], "nh\u1eadp": [0, 1, 3, 4, 5, 6, 8, 11, 19, 25], "nh\u1eadt": [2, 4, 14, 18, 20, 22, 23, 25], "nh\u1eb9": 17, "nh\u1ecbp": 13, "nh\u1ecf": [4, 7, 25], "nh\u1ed9n": [11, 26], "nh\u1eefng": 25, "ni\u1ec1m": 26, "none": [20, 22, 23], "note": [3, 13], "ntag": 24, "ntag213": 24, "number_of_l": 25, "n\u00e0o": [5, 8, 9], "n\u00e0y": [3, 4, 7, 11, 12, 13, 14, 15, 16, 17, 18, 21, 22, 23, 24, 25, 27], "n\u00e2ng": [1, 3, 26], "n\u00e9": [10, 26], "n\u00ean": 21, "n\u00f3": [5, 13, 19], "n\u00f3i": 26, "n\u00f3ng": [4, 22], "n\u00fat": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 18, 20, 22, 23, 24, 27], "n\u0103ng": [2, 3, 7, 18, 19, 24], "n\u01a1i": [3, 5, 7, 26], "n\u01b0\u1edbc": [20, 25], "n\u1ea1p": [0, 1, 2, 5, 6, 7, 18, 27], "n\u1ebfu": [1, 3, 4, 5, 6, 12, 16, 17, 19, 20, 23], "n\u1ec1n": [3, 5, 23, 27], "n\u1ed1i": [3, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 20, 22, 25, 26], "n\u1ed1t": [3, 5, 7, 12], "n\u1ed9i": [2, 4, 12, 14, 22, 26], "n\u1eeda": 13, "obstacleavoidancerobot": 25, "off": [4, 11, 12, 13, 16, 17], "ok": [4, 23], "ol": [0, 1, 2, 5, 6, 7, 8, 10, 12, 17, 18, 19, 20, 22, 23, 24, 25, 26, 27], "on_long_press": 19, "on_press": 19, "on_releas": 19, "onboard": 10, "onlin": [19, 20, 21, 22, 23, 24, 25], "open": [5, 20, 24], "optim": 23, "oserror": [4, 23], "ota": [2, 7, 10], "other": 20, "out": 25, "output": [2, 18, 23], "over": 1, "overshoot": 23, "page": 20, "page1": 20, "page2": 20, "paramet": 23, "pass": 24, "password": 4, "path": 23, "pattern": 23, "pca9685": [10, 16], "pcb": 24, "pcf8574": 23, "percent": 22, "pet": 24, "phi\u00ean": [6, 14, 17, 24], "phong": [5, 19], "ph\u00e1": 26, "ph\u00e1p": [1, 3, 4, 18, 19, 20, 21, 22, 23, 24, 25], "ph\u00e1t": [0, 1, 2, 3, 4, 5, 7, 10, 14, 19, 26, 27], "ph\u00e2n": [6, 23, 25], "ph\u00e9p": [4, 6, 10, 21, 24, 25], "ph\u00f2ng": 21, "ph\u00f3ng": 7, "ph\u00f9": [5, 20, 21, 23], "ph\u00fa": [5, 19], "ph\u00fat": [9, 22], "ph\u01b0\u01a1ng": [3, 8, 9], "ph\u1ea1m": [20, 22, 25], "ph\u1ea3i": [14, 17, 20, 23, 24, 25], "ph\u1ea3n": [4, 12, 15, 16, 17, 20, 23, 25], "ph\u1ea7n": [1, 4, 5, 6, 7, 10, 18, 19, 21, 26, 27], "ph\u1ea9m": 20, "ph\u1ecfng": 24, "ph\u1ed1i": 6, "ph\u1ed5": 25, "ph\u1ee5": 26, "ph\u1ee5c": [0, 4, 6, 7], "piano": [5, 10], "pid_output": 23, "pin": [0, 2, 4, 15, 16, 19, 25], "pir": 18, "pitch": [14, 17], "pitchdeg": [14, 17], "pixel": 0, "plai": [20, 22, 24, 25], "play_song": [20, 22, 23, 24, 25], "play_ton": [0, 2, 3, 4, 6, 12, 13, 17, 20, 25], "pleas": 24, "plot_bar": 21, "po": 23, "point": 22, "port": [3, 6, 19, 21], "port1": [0, 2, 3, 5, 14, 18, 19, 21, 22, 23, 24, 25], "port2": [2, 18, 19, 21, 22, 23, 24, 25], "port3": 2, "port4": 2, "port5": 2, "port6": [0, 2, 18, 19, 21], "port_stat": 23, "portx": [18, 19, 21], "posit": [0, 2, 3, 15, 16, 17, 20, 23, 24], "power": [2, 3], "prefix": 19, "presenc": 20, "present": 24, "present_count": 24, "press": [9, 18, 22, 23, 24], "print": [4, 19, 20, 21, 22, 23, 24, 25], "program": [20, 22, 23, 24], "project": [5, 7], "proport": 23, "psram": 2, "pull": [2, 19, 20, 23], "pull_down": 19, "pull_up": 19, "pwm": [2, 3, 5], "pwr": 2, "py": 4, "python": [6, 8, 26, 27], "qua": [2, 4, 10, 12, 14, 15, 16, 18, 19, 21, 23, 26], "quai": [0, 5, 10, 17, 18, 23, 26], "qualiti": 20, "quan": [1, 10, 20, 25], "quanh": 25, "quen": [1, 3, 10, 11, 26], "quy\u1ebft": 25, "quy\u1ec1n": [1, 4, 6], "qu\u00e1": [4, 9, 19, 22, 23, 24, 25], "qu\u00e9t": [22, 25], "qu\u00ean": 4, "qu\u1ea3": [3, 4, 11, 16, 20, 24], "qu\u1ea3n": [3, 22], "r": [3, 4, 11, 20, 24, 25], "ra": [4, 12, 13, 18, 19, 21, 24, 26], "ram": 2, "randint": [11, 16, 22], "random": [11, 13, 16, 22, 25], "rang": [14, 16, 20, 22, 24, 25], "rate": 23, "rc": [0, 2, 10, 16], "rc522": [18, 24], "read": [0, 3, 5, 7, 10, 18, 19, 20, 21, 23], "read_line_posit": 23, "reader": 24, "readi": [6, 20, 22, 23, 24], "red": [20, 24, 25], "refer": [19, 21], "refresh": 22, "relai": 18, "remov": 24, "remove_card": 24, "render": 4, "repeat": 25, "reset": [0, 1, 2, 3, 4, 5, 7, 9, 10, 22, 23], "resistor": [20, 23], "resolut": 2, "retri": 23, "return": [20, 22, 23, 24, 25], "rfid": [18, 24], "rfid_read": 24, "rgb": [0, 2, 3, 5, 10, 18, 22, 27], "rh": 21, "right": [23, 25], "right_sensor": 23, "right_spe": 23, "ri\u00eang": [18, 21], "robot": [1, 2, 5, 10, 13, 14, 16, 17, 19, 24, 26, 27], "roll": [14, 17], "rolldeg": [2, 14, 17], "rotat": 18, "run": [1, 3, 5, 8, 11, 14, 15, 16, 20, 22, 23, 24, 25], "run_sorting_cycl": 20, "rung": [0, 2, 3, 5, 10], "runtim": 23, "r\u00e8": 4, "r\u00ea": 13, "r\u00f5": 7, "r\u00fat": 6, "r\u01a1i": 20, "r\u1ea5t": [13, 25], "r\u1eb1ng": [8, 9], "r\u1ebd": [23, 25], "r\u1ed3i": [11, 16, 24], "r\u1ed9ng": [0, 1, 2, 3, 5, 6, 7, 26], "r\u1eddi": 18, "s1": [0, 2, 10, 15, 16], "s3": [0, 14, 26], "s4": [0, 2, 10, 15, 16], "safari": [11, 12, 13, 14, 17], "safe": 25, "safe_dist": 25, "safeti": 25, "sai": [0, 4, 18, 20, 23, 24], "sampl": 20, "sang": [1, 3, 11, 14, 16, 17], "sao": [11, 12, 13, 14, 15, 16, 17], "sau": [1, 3, 4, 5, 6, 7, 11, 13, 15], "save": [5, 7, 20], "save_attendance_log": 24, "save_stud": 24, "scan": [23, 24], "scan_environ": 25, "scanner": 24, "scl": [18, 20, 23], "score": [20, 22], "scroll": 14, "sda": [18, 20, 23], "search": 23, "search_lin": 23, "search_tim": 23, "second": [20, 22, 24, 25], "segment": 18, "self": [20, 22, 23, 24, 25], "sensor": [18, 19, 20, 21, 22, 23, 25], "sensor_valu": [22, 23], "serial": [0, 1, 2, 3, 4, 5, 7, 8, 9], "servo": [0, 2, 3, 5, 7, 10, 18, 20, 24, 26, 27], "servo1": [2, 3, 10, 15, 16, 17], "servo2": 16, "servo3": [0, 2, 16], "servo4": 16, "servo_posit": 20, "session": 24, "session_act": 24, "set": [19, 20, 22, 23, 24, 25], "set_led_color": 25, "set_rgb": [0, 1, 2, 3, 4, 6, 11, 12, 13, 15, 16, 17, 20, 21, 22, 23, 24, 25], "set_volum": [4, 13], "setup": [0, 1, 3, 4, 5, 6, 7, 8, 20, 22, 23, 24], "sg90": 16, "shake_strength": 17, "show": [1, 20, 24, 25], "show_menu": 19, "show_statist": [20, 24], "show_temperature_demo": 22, "sig": [18, 19, 21], "sig2": 18, "signal": [0, 3, 5], "sil": [22, 23, 24, 25], "simpl": [20, 23], "simple_color_detect": 20, "sinh": [0, 1, 2, 3, 5, 7, 10, 18, 24, 27], "size": 3, "si\u00eau": 25, "skip": 20, "sleep": [4, 6, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "smart": [23, 24], "smartlinefollow": 23, "snap": 7, "solut": [0, 1, 2, 3, 4, 5, 14], "solver": 23, "son": 13, "sort": 20, "sort_item": 20, "sorted_color": 20, "sound": [10, 24, 25], "so\u1ea1n": [1, 3], "space": 22, "speed": [0, 2, 3, 15, 23, 25], "spi": 2, "sr04": 25, "ssid": [4, 6, 8], "star": 22, "start": [20, 22, 23, 24], "start_sess": 24, "start_tim": [23, 24], "starter": 26, "stat": 20, "state": [22, 23, 25], "statist": [20, 23, 24], "stats_pag": 20, "statu": 22, "stem": [0, 1, 2, 3, 4, 5, 14, 18, 20, 21, 22, 23], "stop": [20, 22, 23, 25], "stop_robot": [0, 3, 15, 23], "stopwatch": 22, "storag": 4, "str": [20, 24], "strftime": 24, "student": 24, "student_": 24, "student_class": 24, "student_nam": 24, "studio": [1, 3, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 26], "success": 24, "successfulli": 24, "sum": 23, "sumo": 23, "su\u1ea5t": 23, "switch": 24, "switch_mod": 22, "syntaxerror": 4, "system": [20, 22, 24, 25], "s\u00e0ng": [2, 20, 23], "s\u00e1ch": [10, 12], "s\u00e1ng": [0, 1, 4, 6, 11, 12, 13, 17, 18, 19, 20, 23, 26], "s\u00e1t": 1, "s\u00f3ng": 25, "s\u1ea1c": [2, 4, 6], "s\u1ea1ch": 23, "s\u1ea3n": [20, 24], "s\u1eafc": [10, 20], "s\u1eafp": [14, 17, 18, 23], "s\u1eb5n": [2, 6, 7, 12, 13, 14, 15, 17, 18, 19, 20, 21, 23], "s\u1ebb": 5, "s\u1ebd": [0, 1, 4, 6, 7, 9, 11, 12, 13, 14, 15, 16, 17, 22], "s\u1ed1": [4, 5, 11, 12, 13, 14, 15, 16, 17, 18], "s\u1eed": [0, 2, 3, 4, 6, 12, 14, 17, 19, 20, 22, 23, 24, 25, 26], "t": [18, 19, 21], "tai": [2, 12, 13, 15, 16, 26], "target_color": 20, "target_posit": 20, "tb6612": 2, "tcr5000": 18, "tcs34725": 20, "tech": 26, "technic": [0, 26], "temperatur": [21, 22], "tensilica": 2, "term": 23, "test": [20, 22], "test_color": 20, "text": 3, "thai": [11, 13, 14, 17, 22], "tham": 4, "thanh": [0, 2, 3, 4, 5, 10, 13, 18, 19, 20, 24, 25], "thao": [1, 5, 7, 19], "thc": [0, 1, 3, 5, 26], "theo": [3, 4, 5, 7, 10, 11, 12, 13, 14, 15, 16, 20, 21, 22, 25], "theori": [20, 23, 25], "thi": [20, 23, 24], "think": 26, "thi\u1ebft": [0, 2, 4, 5, 6, 7, 8, 18, 23, 25, 26], "thi\u1ebfu": 4, "thi\u1ec7u": [1, 5, 6, 26], "tho\u00e1t": [20, 23], "thu": 7, "th\u00e0nh": [1, 6, 7, 8, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 24, 26], "th\u00e1i": [2, 3, 9, 20, 21, 22, 24, 25, 27], "th\u00eam": [4, 8, 9, 11, 12, 13, 14, 15, 16, 18, 19, 20, 22], "th\u00ec": [3, 5, 12, 17], "th\u00edch": [0, 3, 5, 7, 26], "th\u00f4": 20, "th\u00f4ng": [3, 4, 5, 7, 8, 9, 10, 12, 14, 16, 17, 18], "th\u00fa": 24, "th\u00fac": [23, 24], "th\u0103ng": 17, "th\u01b0": [0, 1, 2, 3, 4, 5, 10, 21, 24], "th\u01b0\u1edbc": [2, 20, 22], "th\u01b0\u1eddng": [4, 7], "th\u1ea3": [0, 5, 6, 7, 10, 12, 18, 26, 27], "th\u1ea3m": 25, "th\u1ea3o": [1, 3], "th\u1ea5p": [2, 19, 20, 22, 23], "th\u1ea5t": 4, "th\u1ea5y": [0, 4, 6, 9, 23, 25, 26], "th\u1ea7y": 26, "th\u1eadt": [1, 3, 22, 26], "th\u1eafc": 7, "th\u1eb3ng": [15, 23], "th\u1ebb": 18, "th\u1ec3": [1, 2, 3, 4, 5, 6, 7, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23, 24, 26], "th\u1ecb": [1, 2, 4, 5, 6, 7, 8, 10, 12, 18, 19, 20, 23, 24, 25], "th\u1ed1ng": [3, 18, 19, 20, 23, 25], "th\u1eddi": [3, 10, 11, 14, 19, 21, 22, 23, 24, 25, 26], "th\u1ee5": [2, 20, 22, 23, 24, 25], "th\u1ee5t": 4, "th\u1ee7": 18, "th\u1ee9": 22, "th\u1ee9c": [3, 8, 9], "th\u1eed": [0, 1, 4, 5, 12, 13, 14, 16, 17, 22, 23, 24, 25, 26], "th\u1ef1c": [0, 1, 14, 20, 22, 23, 24, 25, 26], "tiktok": [4, 26, 27], "time": [4, 6, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "timeout": [24, 25], "timer": [3, 22], "timer_run": 22, "timer_second": 22, "timestamp": [20, 24], "tin": [4, 8, 9, 10, 14, 20, 21, 22, 23, 24, 25], "ti\u00ean": 14, "ti\u00eau": [2, 20, 22, 23, 24, 25], "ti\u1ebfn": [5, 23, 25], "ti\u1ebfng": [4, 6, 7, 12, 13, 17], "ti\u1ebfp": [0, 3, 4, 5, 14, 15, 18, 19, 20, 21, 22, 24, 25], "ti\u1ebft": [20, 24], "ti\u1ec3u": [0, 1, 3, 5, 26], "tm1637": 22, "toggl": 19, "toggle_l": 19, "tone": [0, 2, 4, 6, 12, 13, 17, 20], "total": [20, 24], "total_count": 24, "total_dist": 23, "total_sensor": 23, "total_sort": 20, "to\u00e0n": [5, 6, 23, 25, 26], "to\u00e1n": [23, 24], "to\u1ea1": 14, "tra": [0, 1, 3, 5, 7, 8, 9, 15, 18, 21, 22, 25], "track": [20, 23], "tran": 24, "trang": [20, 26], "trigger": 25, "tri\u1ec3n": [1, 2, 5, 26, 27], "tri\u1ec7u": [2, 4, 10], "trong": [5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 19, 20, 21, 22, 25], "true": [4, 11, 12, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "trui": [4, 5, 10, 11, 12, 13, 14, 15, 16, 17, 18], "trung": [20, 22, 25, 26], "truy\u1ec1n": [4, 6, 24], "try": [9, 20, 22, 23, 24, 25], "tr\u00e1i": [14, 17, 23, 25], "tr\u00e1nh": [3, 4, 18, 19, 21, 22, 23, 24], "tr\u00ean": [1, 2, 4, 5, 6, 7, 9, 11, 13, 14, 17, 19, 20, 21, 22, 23, 24, 25, 26], "tr\u00ecnh": [5, 7, 9, 26], "tr\u00ed": [0, 14, 16, 20, 23], "tr\u00f2": [12, 19, 22, 24], "tr\u00fac": 27, "tr\u01b0\u1edbc": [0, 4, 13, 26], "tr\u01b0\u1eddng": [18, 20, 21, 22, 23, 25, 26], "tr\u1ea1ng": [2, 3, 9, 20, 21, 22, 24, 25], "tr\u1ea3": [4, 19], "tr\u1ea3i": [13, 26], "tr\u1eafng": [20, 23], "tr\u1ebb": [3, 5, 26, 27], "tr\u1ecb": [4, 11, 14, 17, 19, 20, 22, 23, 24, 25], "tr\u1ecdn": 26, "tr\u1ecdng": 23, "tr\u1ed9n": 20, "tr\u1edf": [16, 20, 26], "tr\u1ee3": [1, 2, 3, 5, 6, 7, 8, 9, 10, 13, 18, 19, 21, 22, 24, 25, 26], "tr\u1ee5c": 14, "tr\u1eeb": 22, "tr\u1eef": [5, 24], "tr\u1ef1c": [1, 3, 4, 5, 6, 10, 21, 25], "ttyusb": 6, "ttyusbx": [6, 8], "turn": [23, 25], "turn_direct": 25, "turn_left": [0, 3, 15, 23, 25], "turn_right": [15, 23, 25], "turn_spe": 23, "tutori": 23, "tuy\u1ebfn": [1, 6], "tu\u1ea7n": 15, "tu\u1ef3": 21, "type": 24, "t\u00e0i": 27, "t\u00e1c": [0, 1, 5, 7, 12, 13, 19, 20, 22, 24], "t\u00e2m": 26, "t\u00ean": [0, 1, 6, 7, 8, 22], "t\u00ecm": [4, 23, 24], "t\u00ecnh": 18, "t\u00edch": [0, 2, 5, 7, 11, 13, 18, 19, 22, 23, 24, 26], "t\u00edm": [11, 12, 22, 23, 24], "t\u00edn": [0, 3, 5, 7, 10, 18, 19, 21], "t\u00ednh": [0, 1, 3, 4, 6, 8, 9, 11, 12, 13, 14, 15, 16, 17, 20, 22, 25], "t\u00f4i": 26, "t\u00f9y": [3, 19, 22, 23, 24], "t\u0103ng": [13, 16, 19, 20, 22, 23], "t\u0129nh": 22, "t\u01b0": [1, 3, 26], "t\u01b0\u01a1ng": [0, 10, 12, 13, 16, 18, 20, 21, 22, 23, 24, 25, 26], "t\u01b0\u1edfng": [22, 24], "t\u01b0\u1ee3ng": [1, 3, 5, 6, 7, 10, 19], "t\u1ea1i": [3, 4, 5, 6, 8, 9, 14, 18, 19, 20, 22, 23, 24, 26], "t\u1ea1o": [0, 1, 2, 3, 4, 5, 7, 12, 13, 14, 15, 16, 17, 26], "t\u1ea3": [0, 2, 3, 5, 7, 8, 9, 10, 18, 19, 21, 26], "t\u1ea3i": [1, 3, 4, 5, 6, 7, 18], "t\u1ea3ng": [3, 5, 27], "t\u1ea5t": [4, 6, 7, 10, 18, 22, 24], "t\u1ea7n": [13, 22, 23, 24, 25], "t\u1eadp": 26, "t\u1eafc": 4, "t\u1eaft": [2, 3, 4, 5, 7, 9, 11, 12, 13, 20, 25], "t\u1ebf": [0, 1, 10, 20, 22, 23, 24, 25, 26], "t\u1ed1c": [0, 2, 3, 5, 15, 17, 22, 23, 24, 25], "t\u1ed1i": [20, 23, 25], "t\u1ed1t": [4, 19, 20, 26], "t\u1ed3n": 24, "t\u1ed5ng": 20, "t\u1edbi": 26, "t\u1ee5": 19, "t\u1ee5c": [4, 11, 14, 17, 23, 24], "t\u1ee7": 24, "t\u1eeb": [0, 2, 5, 6, 7, 10, 13, 15, 16, 18, 21, 22, 23, 24, 25], "t\u1eebng": [4, 7, 13, 16, 18, 20, 21, 22, 23], "t\u1ef1": [2, 3, 4, 5, 7, 10, 14, 15, 17, 20, 23, 24, 25, 26], "uart": 2, "ultrason": [18, 25], "unicod": 22, "unknown": 24, "unpack": 23, "up": [2, 19, 20, 23], "updat": [0, 14, 17, 23], "update_displai": 23, "upload": [0, 1, 3, 5, 6, 7, 8], "usb": [1, 2, 7, 11, 12, 13, 14, 15, 16, 17], "usbmodem": [0, 1, 3, 5], "usbmodemxxxx": [6, 8], "v": [1, 3, 18], "v2": [2, 3, 4, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 19, 21, 26], "valu": 17, "van": 24, "vcc": [18, 19, 21], "vehicl": 23, "vi": [0, 12, 16, 20, 22, 25], "visual": [22, 23, 24, 25], "vi\u00ean": [7, 26, 27], "vi\u1ebft": [3, 10], "vi\u1ec7c": [1, 2, 3, 5, 22, 23], "vi\u1ec7n": [0, 1, 2, 3, 4, 5, 10, 21, 24], "vi\u1ec7t": [7, 26, 27], "vl53l0x": 18, "vn": [1, 3, 4, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 26], "volum": [4, 25], "vui": [1, 9, 11, 13, 26], "vu\u00f4ng": [15, 25], "v\u00e0": [4, 5, 6, 7, 8, 9, 10, 19, 20, 22, 23, 25, 27], "v\u00e0i": [4, 10], "v\u00e0ng": [11, 20, 22, 23, 24, 25], "v\u00e0o": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 16, 19, 20, 21, 24, 26], "v\u00ec": 6, "v\u00ed": [1, 2, 7, 8, 16, 18, 26], "v\u00edt": 2, "v\u00f2ng": [4, 11, 12, 14, 15, 16, 20, 22, 23, 24], "v\u00f9ng": [1, 3, 5, 7, 24], "v\u0103n": [7, 10, 14], "v\u1ea1ch": 23, "v\u1ea3i": 25, "v\u1ea5n": [0, 6], "v\u1eabn": 4, "v\u1eaby": 15, "v\u1eadn": [17, 23], "v\u1eadt": [3, 10, 15, 20, 26], "v\u1ebd": 25, "v\u1ec1": [4, 6, 16, 19, 20, 23], "v\u1ecb": [0, 14, 16, 20, 23, 25], "v\u1edbi": [0, 1, 2, 4, 6, 7, 8, 9, 20, 24, 25, 27], "v\u1eeba": 19, "v\u1ef1c": [3, 5], "w": [20, 24], "wait": [20, 22, 24, 25], "warn": 25, "warning_dist": 25, "wch": 6, "web": [1, 2, 3, 5, 6, 11, 12, 13, 14, 17, 21], "websit": [4, 26, 27], "weight": 23, "weighted_sum": 23, "when": [20, 22, 23, 24], "while": [4, 9, 11, 12, 14, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "white": 20, "wi": [0, 2, 3, 7, 8], "wifi": [0, 2, 18], "window": 5, "wire": [18, 21, 22], "workspac": 18, "world": 22, "write": [0, 1, 2, 3, 4, 5, 6, 7, 10, 14, 17, 18, 19, 20, 21, 22, 23, 24, 25], "write_bar": 17, "wrong": 20, "wroom": 2, "ws2812": 2, "x": [3, 14, 20], "xa": [21, 24, 25], "xanh": [6, 12, 20, 21, 22, 23, 24, 25], "xe": [2, 3, 15, 23, 25], "xem": [8, 9], "xin": [0, 12], "xoai": [10, 17], "xong": [4, 13], "xung": [20, 25], "xuy\u00ean": [7, 24], "xu\u1ea5t": [5, 20], "xu\u1ed1ng": 3, "x\u00e1c": [4, 15, 17, 18, 23, 24], "x\u00e2m": 25, "x\u00e2y": [3, 5, 7, 12, 26], "x\u00f3a": [4, 7, 14], "x\u00fac": 24, "x\u1ea1": [12, 19, 20, 25], "x\u1ebfp": 23, "x\u1ed1p": 25, "x\u1eed": [10, 19, 20, 23, 27], "y": [3, 7, 14, 24], "yellow": [20, 25], "your": 24, "y\u1ebfu": [0, 4, 6, 20], "z": [7, 22], "zone": 25, "\u00e1n": [0, 7, 10, 19, 20, 21, 23, 26], "\u00e1nh": [11, 13, 18, 20, 23], "\u00e1p": [3, 19, 20, 21, 22, 23, 24, 25], "\u00e2m": [0, 2, 3, 4, 5, 7, 10, 13, 18, 19, 20, 22, 25], "\u00edch": 20, "\u00edt": 21, "\u00fd": [1, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17], "\u0103n": 24, "\u0111a": 25, "\u0111am": 26, "\u0111ang": [4, 15, 19, 24, 25], "\u0111en": 23, "\u0111i": [13, 23], "\u0111i\u1ec1u": [0, 2, 3, 5, 6, 7, 10, 11, 12, 13, 18, 20, 23, 24, 25, 26], "\u0111i\u1ec3m": [1, 3, 22], "\u0111i\u1ec7n": [2, 19, 20, 21, 22, 23, 24, 25], "\u0111i\u1ec7p": [12, 21], "\u0111i\u1ec7u": [0, 3, 5, 12, 13], "\u0111o": [2, 10, 17, 18, 20, 21], "\u0111o\u1ea1n": [4, 6, 22], "\u0111\u00e1p": 7, "\u0111\u00e2y": [3, 4, 5, 6, 7, 8, 9, 10, 24], "\u0111\u00e3": [1, 3, 4, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 19, 24, 25, 26], "\u0111\u00e8": 4, "\u0111\u00e8n": [1, 3, 4, 5, 6, 10, 11, 13, 18], "\u0111\u00ecnh": 26, "\u0111\u00edch": [6, 26], "\u0111\u00f3": [1, 3, 5, 15, 17, 26], "\u0111\u00f3ng": 24, "\u0111\u00f4": 13, "\u0111\u00f4i": 21, "\u0111\u00fang": [0, 1, 4, 8, 9, 18, 19, 23], "\u0111\u01a1n": [13, 19, 22, 24], "\u0111\u01b0a": 24, "\u0111\u01b0\u01a1ng": 16, "\u0111\u01b0\u1ee3c": [0, 1, 2, 3, 4, 5, 6, 7, 10, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 25, 26], "\u0111\u1ea1i": 1, "\u0111\u1ea3m": [4, 6, 18, 19, 20, 21, 22, 23, 24, 25], "\u0111\u1ea5t": [17, 19, 21, 23], "\u0111\u1ea7u": [3, 4, 5, 6, 7, 9, 14, 16, 19, 20, 24, 26, 27], "\u0111\u1ea7y": [4, 10], "\u0111\u1eb7c": [1, 3, 5, 20, 22, 23], "\u0111\u1eb7t": [0, 1, 2, 3, 4, 5, 7, 8, 9, 15, 19, 21, 25, 27], "\u0111\u1ebfm": [19, 20, 22], "\u0111\u1ebfn": [1, 15, 17, 20, 23, 24, 25, 27], "\u0111\u1ec1": [0, 6], "\u0111\u1ec1u": [1, 18, 26], "\u0111\u1ec3": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "\u0111\u1ecba": [1, 3, 5, 6, 8, 14, 18, 20, 23], "\u0111\u1ecbnh": [4, 17, 18, 19, 21, 23, 24, 25], "\u0111\u1ecdc": [0, 2, 3, 5, 7, 10, 17, 18, 26], "\u0111\u1ecf": [0, 4, 5, 13, 17, 20, 21, 22, 23, 24, 25], "\u0111\u1ed1i": [1, 3, 10, 19], "\u0111\u1ed3": [17, 25], "\u0111\u1ed3ng": 26, "\u0111\u1ed5i": [3, 5, 7, 10, 12, 13, 14, 17, 19, 22], "\u0111\u1ed7": 25, "\u0111\u1ed9": [2, 3, 5, 10, 14, 15, 16, 17, 18, 19, 23, 24, 25], "\u0111\u1ed9c": [0, 1, 3], "\u0111\u1ed9i": 4, "\u0111\u1ed9ng": [0, 1, 3, 4, 5, 7, 10, 12, 16, 17, 18, 20, 21, 22, 24, 25, 26, 27], "\u0111\u1ed9t": 20, "\u0111\u1ee3i": [11, 21], "\u0111\u1ee5c": 20, "\u0111\u1ee7": [4, 10, 20], "\u01b0u": [20, 23], "\u1ea3nh": [0, 14, 25, 26], "\u1ea9m": [14, 18, 22, 25], "\u1ed5n": [4, 18, 19, 21, 25], "\u1edf": [4, 5, 14, 16, 18, 19, 21, 22, 23, 24], "\u1ee9ng": [2, 7, 10, 12, 13, 15, 16, 17, 18, 20, 22, 23, 24, 25], "\u2460": [2, 3, 5, 7], "\u2461": [2, 3, 5, 7], "\u2462": [2, 3, 5, 7], "\u2463": [2, 3, 5, 7], "\u2464": [2, 3, 5, 7], "\u2465": [2, 3, 5, 7], "\u2466": 2, "\u2467": 2, "\u2468": 2, "\u2469": 2, "\u246a": 2, "\u246b": 2, "\u246c": 2, "\u246d": 2, "\u246e": 2, "\u246f": 2, "\u2470": 2, "\u2471": 2, "\u2472": 2, "\u2473": 2, "\u3251": 2}, "titles": ["Gi\u1edbi thi\u1ec7u", "B\u1eaft \u0111\u1ea7u", "Gi\u1edbi thi\u1ec7u ph\u1ea7n c\u1ee9ng", "H\u01b0\u1edbng d\u1eabn l\u1eadp tr\u00ecnh", "X\u1eed l\u00fd l\u1ed7i", "BeE IDE", "C\u00e0i \u0111\u1eb7t", "Giao di\u1ec7n", "N\u1ea1p ch\u01b0\u01a1ng tr\u00ecnh", "N\u1ea1p Firmware", "V\u00ed d\u1ee5 l\u1eadp tr\u00ecnh", "LED RGB", "N\u00fat nh\u1ea5n", "Buzzer", "M\u00e0n h\u00ecnh OLED", "\u0110\u1ed9ng c\u01a1", "Servo", "C\u1ea3m bi\u1ebfn IMU", "Gi\u1edbi thi\u1ec7u", "Module N\u00fat Nh\u1ea5n", "BeeColorDetect", "Module DHT11", "BeeLedSegment", "BeeLineDetect", "BeeRC522", "BeeUltrasonic", "BeE STEM Solutions", "BeE STEM Solutions Documentation"], "titleterms": {"0": 21, "1": [0, 1, 2, 3, 4, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 25], "10": [2, 4], "11": 4, "12": 4, "2": [0, 1, 2, 3, 4, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17, 18, 19], "3": [0, 1, 2, 3, 4, 6, 7, 8, 11, 12, 13, 14, 15, 16, 17, 18], "4": [1, 2, 3, 4, 6, 7, 8, 11, 12, 13, 14, 15, 16, 17, 18, 23], "5": [1, 2, 3, 4, 6, 7], "6": [1, 2, 3, 4, 6, 7], "7": [1, 2, 4, 6, 7], "8": [2, 4, 6, 7], "9": [2, 4], "90": 16, "A": [12, 16], "about": 27, "assist": 7, "b": [12, 16], "bee": [0, 1, 3, 4, 5, 6, 7, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 26, 27], "beecolordetect": 20, "beeledseg": 22, "beelinedetect": 23, "beerc522": 24, "beeultrason": 25, "birthdai": 13, "bit": 23, "bi\u1ebfn": [14, 17, 20, 21, 23, 25], "bi\u1ec3u": 26, "blockli": [0, 1, 5, 20, 22, 23, 24, 25], "bluetooth": 8, "board": [0, 1, 5, 6, 18, 27], "bright": 22, "button": 4, "buzzer": [4, 13], "b\u00e0i": [13, 19, 20, 21, 22, 23, 24, 25], "b\u01b0\u1edbc": [6, 18], "b\u1ea1n": 1, "b\u1ea3n": [3, 10, 14, 19, 20, 22, 23, 24, 25], "b\u1ea3ng": 7, "b\u1eadt": [0, 3, 5, 11, 12, 19, 21], "b\u1eaft": [0, 1], "b\u1eb1ng": [1, 4, 16, 19], "b\u1ecb": [1, 18, 19, 24, 25], "b\u1ed9": 24, "c": 0, "callback": 19, "cao": [10, 11, 13, 19, 20, 22, 23, 24, 25], "ch340c": 6, "check": 23, "chi": 2, "cho": 1, "chuy\u1ec3n": [0, 20], "chu\u1ea9n": [1, 18], "chu\u1ed7i": 13, "ch\u00e2n": 2, "ch\u00ednh": [0, 3, 5, 20, 25], "ch\u01b0\u01a1ng": [1, 3, 8, 18], "ch\u1ea1y": [14, 15], "ch\u1ea5m": 22, "ch\u1ec9": 24, "ch\u1ec9nh": 7, "ch\u1ee9c": 22, "ch\u1eef": [0, 3, 14], "clear": 22, "clear_list": 24, "cm": 25, "code": [0, 4], "colon": 22, "color_nam": 20, "commun": 20, "cu\u1ed9n": 22, "c\u00e0i": [6, 18], "c\u00e1c": [0, 2, 5, 10, 21, 25, 26], "c\u00e1ch": [5, 18, 25], "c\u00f3": [1, 11, 12, 13, 14, 15, 16, 17, 23, 24], "c\u00f4ng": [2, 7], "c\u00f9ng": 21, "c\u01a1": [3, 10, 14, 15, 19, 20, 22, 23, 24, 25], "c\u1ea3": [12, 20, 21, 23], "c\u1ea3m": [14, 17, 20, 23, 25, 26], "c\u1ea3n": 25, "c\u1ea5p": 2, "c\u1ea5u": [6, 20], "c\u1ea7n": [1, 11, 12, 13, 14, 15, 16, 17], "c\u1ea7u": 6, "c\u1eadp": [0, 1, 3, 6, 8, 24], "c\u1eafm": 18, "c\u1ed1": [0, 4, 6, 16], "c\u1ed1t": 26, "c\u1ed5ng": [2, 18], "c\u1ee5": [7, 20], "c\u1ee7a": [6, 26], "c\u1ee9ng": [2, 11, 12, 13, 14, 15, 16, 17], "danh": [7, 18, 24], "dao": 23, "delai": 22, "detail": 24, "detect": 17, "dht11": 21, "di": 0, "displai": 22, "distance_cm": 25, "distance_mm": 25, "di\u1ec7n": [3, 5, 7, 19, 20, 21, 22, 23, 24, 25], "document": 27, "driver": 6, "d\u00e0ng": 10, "d\u00f2ng": [0, 14], "d\u01b0\u01a1ng": 11, "d\u1ea5u": 22, "d\u1eabn": [3, 18], "d\u1ec5": 10, "d\u1ee5": [0, 3, 5, 10, 19, 20, 21, 22, 23, 24, 25, 27], "d\u1ee5c": 26, "d\u1ee5ng": [7, 18, 19], "d\u1eebng": 15, "d\u1eef": [20, 21], "d\u1ef1": [3, 5], "ecosystem": 18, "editor": 4, "esp32": 2, "fail": 20, "fals": 24, "fi": [1, 4, 5, 6], "file": 24, "firmwar": 9, "flash": 9, "gain": 20, "get_all_colors_in_rgb": 20, "get_color": 20, "gian": 20, "giao": [2, 3, 5, 7, 19, 20, 21, 22, 23, 24, 25], "gi\u00e1": [21, 26], "gi\u00e1o": 26, "gi\u00e2i": 15, "gi\u1ea3i": [19, 20, 21, 22, 23, 24, 25], "gi\u1ea3n": [0, 20, 23, 25], "gi\u1edbi": [0, 2, 3, 4, 8, 9, 10, 18, 19, 20, 21, 22, 23, 24, 25, 27], "gi\u1eef": 19, "gi\u1eefa": 1, "grove": 18, "g\u00ec": [0, 5], "g\u00f3c": [15, 16, 17], "g\u1ea7n": 25, "g\u1eb7p": [0, 6, 19, 20, 21, 22, 23, 24, 25], "g\u1ecdi": 19, "g\u1ee3i": [3, 10], "hai": [12, 21, 22], "happi": 13, "hello": 14, "hex": [20, 22], "hi\u1ec3n": [0, 3, 14, 17, 21, 22], "hi\u1ec7n": [17, 20, 23, 25], "hi\u1ec7u": [5, 7, 11, 14, 18], "ho\u1ea1t": [6, 19, 23], "ho\u1eb7c": [4, 21, 22], "html": 20, "html_hex": 20, "h\u00e0m": 3, "h\u00e0nh": [3, 5, 10], "h\u00e1t": 13, "h\u00ecnh": [4, 6, 14, 20, 22], "h\u01b0\u1edbng": [3, 18, 19], "h\u1ec7": [4, 6, 22, 24, 26, 27], "h\u1ecdc": [5, 26], "h\u1ed3": 22, "h\u1ed3i": 19, "h\u1ed7": [0, 4], "h\u1ee3p": [10, 12, 13, 16, 18, 20], "i2c": [20, 23, 24], "id": [1, 3, 4, 5, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 24, 27], "import": 18, "imu": [14, 17], "integration_tim": 20, "is_color": 20, "is_object_pres": 25, "json": 24, "khi": [1, 4, 12, 19, 21, 23], "khi\u1ec3n": [15, 16, 17, 22], "kho\u1ea3ng": 25, "khu": 7, "khu\u1ebfch": 20, "kh\u00f3a": 26, "kh\u00f4ng": [19, 20, 22, 23, 24, 25], "kh\u1ea3o": [2, 19, 20, 21, 22, 23, 24, 25], "kh\u1ecfi": 24, "kh\u1ed1i": [0, 3, 5, 7, 19, 21], "kh\u1edfi": [19, 20, 21, 22, 23, 24, 25], "kit": 18, "ki\u1ec3m": [4, 6, 19, 20, 23, 24], "ki\u1ec7n": 19, "k\u00e9o": [1, 3], "k\u00eanh": 20, "k\u00fd": [19, 22], "k\u1ebft": [0, 1, 2, 4, 5, 6, 11, 12, 13, 14, 15, 16, 17, 19, 21, 23], "k\u1ef9": [0, 2, 19, 20, 21, 22, 23, 24, 25], "led": [0, 3, 4, 11, 12, 13, 16, 19, 21], "lego": 2, "linux": 6, "list_nam": 24, "li\u00ean": [0, 3, 4, 5, 7, 18, 25, 26, 27], "li\u1ec7u": [2, 3, 5, 7, 18, 20, 21], "loa": 4, "load_list": 24, "log": 7, "long": 19, "lo\u1ea1i": 20, "lu\u1eadn": [11, 12, 13, 14, 15, 16, 17], "l\u00e0": [0, 5], "l\u00e0m": 7, "l\u00e1": 0, "l\u00ean": 21, "l\u00f5i": 26, "l\u00fac": 21, "l\u00fd": [0, 2, 4, 5, 24, 26], "l\u1ea7n": 19, "l\u1eadp": [0, 1, 2, 3, 4, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27], "l\u1eafc": 17, "l\u1ebb": 23, "l\u1ec7ch": 21, "l\u1ec7nh": [3, 5, 7, 19, 21], "l\u1ed7i": [4, 19, 20, 21, 22, 23, 24, 25], "l\u1eddi": 26, "maco": 6, "map": 2, "menu": 19, "minh": [22, 23, 24], "mm": 25, "modul": [10, 18, 19, 21, 23, 27], "motor": 15, "m\u00e0n": [4, 14, 22], "m\u00e0u": [0, 3, 11, 20], "m\u00e3": [19, 20, 21, 22, 23, 24, 25], "m\u1eb9o": [1, 5, 7, 18], "m\u1ed9t": [0, 3, 22, 23], "m\u1edbi": 1, "m\u1edf": [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27], "m\u1ee5c": [6, 7, 11, 12, 13, 14, 15, 16, 17], "nghi\u00eang": 17, "nguy\u00ean": [0, 19, 20, 21, 22, 23, 24, 25], "ngu\u1ed3n": [2, 4], "ng\u01b0\u1eddi": 1, "ng\u1eabu": 11, "nhanh": [1, 4, 24], "nhi\u00ean": 11, "nhi\u1ec1u": [14, 16, 19], "nhi\u1ec7t": [21, 22], "nh\u00e1y": [11, 22], "nh\u00ecn": 26, "nh\u00f3m": [0, 3, 5], "nh\u1ea1c": [12, 13], "nh\u1ea5n": [12, 19], "nh\u1ea5p": [11, 22], "nh\u1eadn": 20, "nh\u1eadt": [0, 1, 3, 8], "none": 21, "num1": 22, "num2": 22, "number": 22, "n\u00e2ng": [10, 11, 13, 19, 20, 22, 23, 24, 25], "n\u00f3ng": 21, "n\u00fat": [12, 16, 19], "n\u0103ng": [5, 22], "n\u1ea1p": [3, 4, 8, 9], "n\u1ec1n": [1, 6, 26], "n\u1ed1i": [0, 1, 2, 4, 5, 6, 19, 21, 23], "n\u1ed1t": 13, "n\u1ed5i": 5, "ohstem": 18, "ol": [3, 4, 14, 21], "ota": [0, 1, 3, 4, 5, 6, 8], "ph\u00e1t": [12, 13, 17, 20, 23, 25], "ph\u00e2n": 20, "ph\u01b0\u01a1ng": 25, "ph\u1ea3i": 15, "ph\u1ea3n": 19, "ph\u1ea7n": [0, 2, 11, 12, 13, 14, 15, 16, 17], "ph\u1ea9m": 26, "ph\u1ed5": 21, "ph\u1ee5c": 26, "pid": 23, "pin": 23, "pin_numb": 23, "port": [18, 23], "press": 19, "python": [0, 1, 2, 3, 4, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "qua": [0, 1, 3, 5, 6, 8, 9], "quai": [3, 15], "quan": [0, 2, 3, 5, 7, 18], "qu\u00e1": 21, "qu\u00e9t": 24, "qu\u1ea3": [5, 7, 18], "qu\u1ea3n": [5, 24], "read_al": 23, "readid": 24, "rgb": [11, 20], "ri\u00eang": 15, "robot": [0, 3, 15, 20, 23, 25], "rung": 17, "r\u1ed9ng": [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27], "s3": 2, "sai": [21, 22, 25], "sang": 20, "scan_and_add_card": 24, "scan_and_check": 24, "scan_and_remove_card": 24, "scan_card": 24, "scroll": 22, "segment": 22, "servo": [15, 16, 17], "shake": 17, "show": 22, "show_hex": 22, "show_numb": 22, "show_temperatur": 22, "sinh": 26, "so": 1, "solut": [26, 27], "so\u00e1t": 24, "starter": 18, "stem": [26, 27], "s\u00e1ch": [18, 24], "s\u00e1ng": 22, "s\u00e1nh": 1, "s\u01a1": [2, 19, 21], "s\u1ea3n": 26, "s\u1ed1": [0, 2, 3, 19, 20, 21, 22, 23, 24, 25], "s\u1eed": [7, 18], "s\u1ef1": [0, 4, 6, 19], "tagpres": 24, "technic": 2, "temp": 22, "test": 4, "text": 22, "tham": [2, 19, 20, 21, 22, 23, 24, 25], "thanh": 7, "theo": [1, 6, 17, 23], "thi\u1ebft": [20, 22], "thi\u1ec7u": [0, 2, 3, 4, 8, 9, 10, 18, 19, 20, 21, 22, 23, 24, 25, 27], "threshold": 25, "thu\u1eadt": [0, 2, 19, 20, 21, 22, 23, 24, 25], "thu\u1ed9c": 23, "th\u00e0nh": [0, 2], "th\u00e1i": [19, 23, 26], "th\u00eam": 24, "th\u00edch": [2, 18, 19, 20, 21, 22, 23, 24, 25], "th\u00f4ng": [0, 2, 19, 20, 21, 22, 23, 24, 25, 26], "th\u01b0": 18, "th\u01b0\u1eddng": [0, 6, 19, 20, 21, 22, 23, 24, 25], "th\u1ea3": [1, 3, 19], "th\u1ea3o": [11, 12, 13, 14, 15, 16, 17], "th\u1ebb": 24, "th\u1ec3": [20, 25], "th\u1ecb": [0, 3, 14, 17, 21, 22], "th\u1ed1ng": [6, 22, 24], "th\u1eddi": 20, "th\u1ee9c": 25, "th\u1ef1c": [3, 5, 10, 19], "tin": 26, "ti\u00ean": 1, "ti\u00eau": [6, 7, 11, 12, 13, 14, 15, 16, 17, 26], "ti\u1ebfn": [3, 15], "ti\u1ebfp": [1, 2, 6, 23], "ti\u1ebft": 2, "toolbar": 7, "toolbox": 7, "to\u00e0n": 24, "tra": [4, 6, 19, 20, 23, 24], "tri\u1ebft": 26, "trong": [0, 1, 2, 3, 4, 18, 24], "trui": [1, 3, 6, 24], "trung": 2, "tr\u00e1i": 15, "tr\u00e1nh": 25, "tr\u00ean": [0, 3, 18], "tr\u00ecnh": [0, 1, 2, 3, 4, 6, 8, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27], "tr\u01b0\u1edbc": 1, "tr\u1ea1ng": [19, 23], "tr\u1ea3": [21, 25], "tr\u1ecb": [21, 26], "tr\u1ee3": [0, 4], "tr\u1ef1c": 26, "tuy\u1ebfn": 26, "tu\u1ea7n": 16, "t\u00e0i": [0, 2, 3, 5, 7, 18, 19, 20, 21, 22, 23, 24, 25], "t\u00e2m": 2, "t\u00edch": [10, 20], "t\u00ednh": [5, 23], "t\u00f9y": 7, "t\u01b0\u01a1ng": 2, "t\u01b0\u1ee3ng": [0, 2, 26], "t\u1ea1o": [11, 19, 20, 21, 22, 23, 24, 25], "t\u1ea3i": 24, "t\u1ea3ng": [1, 6, 26], "t\u1ea5t": [20, 23], "t\u1ea7m": 26, "t\u1eadp": [19, 20, 21, 22, 23, 24, 25], "t\u1eafc": 2, "t\u1eaft": 19, "t\u1ebf": 19, "t\u1ed5ng": [2, 7], "t\u1ee5c": 25, "t\u1eeb": 3, "t\u1eebng": 15, "t\u1ef1": [16, 22], "usb": [0, 3, 4, 5, 6, 8, 9], "v2": [0, 1, 5, 6, 18, 27], "valu": 22, "vi": 2, "vi\u1ebft": 1, "vi\u1ec7c": 7, "vi\u1ec7n": 18, "v\u00e0": [0, 1, 2, 3, 11, 12, 13, 14, 15, 16, 17, 18, 21, 24, 26], "v\u00e0o": 18, "v\u00ed": [0, 3, 5, 10, 19, 20, 21, 22, 23, 24, 25, 27], "v\u1eadt": 25, "v\u1ec1": [21, 25], "v\u1edbi": [3, 5, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 21, 22, 23], "v\u1ee5": 26, "v\u1ef1c": 7, "wi": [1, 4, 5, 6], "window": 6, "workspac": 7, "xanh": [0, 11], "xoai": [15, 16], "x\u00e1c": [20, 25], "x\u00f3a": [22, 24], "x\u1eed": [0, 2, 4], "y\u00eau": 6, "\u00e1n": [3, 5], "\u00fd": [3, 10], "\u0111a": 22, "\u0111i\u1ec1u": [15, 16, 17, 19, 22], "\u0111i\u1ec3m": 24, "\u0111i\u1ec7n": 4, "\u0111o": 25, "\u0111\u00fang": 24, "\u0111\u0103ng": 19, "\u0111\u01a1n": [0, 20, 23, 25], "\u0111\u01b0\u1eddng": 23, "\u0111\u01b0\u1ee3c": [23, 24], "\u0111\u1ea1i": 20, "\u0111\u1ea7u": [0, 1], "\u0111\u1eb7t": [6, 18], "\u0111\u1ebfn": 16, "\u0111\u1ecba": 24, "\u0111\u1ecbnh": [16, 20, 22], "\u0111\u1ecdc": [20, 21, 22, 23, 24], "\u0111\u1ecf": [3, 11], "\u0111\u1ed1i": [0, 2, 26], "\u0111\u1ed3": [2, 19, 21], "\u0111\u1ed3ng": 22, "\u0111\u1ed5i": [11, 20], "\u0111\u1ed9": [20, 21, 22], "\u0111\u1ed9ng": [6, 15, 19, 23], "\u01a1n": 26, "\u1ea9m": 21, "\u1ed5n": [20, 22], "\u1ee9ng": [11, 14, 19]}})