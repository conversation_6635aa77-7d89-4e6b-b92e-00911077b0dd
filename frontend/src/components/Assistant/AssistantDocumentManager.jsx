import React, { useState, useEffect } from "react";
import {
	<PERSON>,
	<PERSON>po<PERSON>,
	<PERSON>ton,
	Dialog,
	DialogTitle,
	DialogContent,
	DialogActions,
	CircularProgress,
	Alert,
	Chip,
	Card,
	CardContent,
	IconButton,
	Tooltip,
} from "@mui/material";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import Grid from "@mui/material/Grid2";
import {
	CloudUpload as UploadIcon,
	Delete as DeleteIcon,
	Visibility as ViewIcon,
	Description as DocumentIcon,
	ContentCopy as CopyIcon,
} from "@mui/icons-material";
import axiosInstance from "../../services/axiosInstance";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import CustomButton from "../Common/CustomButton";

const AssistantDocumentManager = ({
	assistantType,
	assistantName,
	showNotification,
}) => {
	const [documents, setDocuments] = useState([]);
	const [loading, setLoading] = useState(false);
	const [uploading, setUploading] = useState(false);
	const [viewDialog, setViewDialog] = useState({
		open: false,
		document: null,
		content: "",
		loading: false,
	});

	// Custom Code Block Component with Syntax Highlighting
	const CodeBlock = ({ node, inline, className, children, ...props }) => {
		const match = /language-(\w+)/.exec(className || "");
		const language = match ? match[1] : "";
		const [copied, setCopied] = useState(false);

		const handleCopy = async () => {
			try {
				const codeText = String(children).replace(/\n$/, "");
				await navigator.clipboard.writeText(codeText);
				setCopied(true);
				setTimeout(() => setCopied(false), 2000);
			} catch (err) {
				console.error("Failed to copy code:", err);
			}
		};

		if (language === "") {
			return (
				<Box
					component="code"
					sx={{
						backgroundColor: "#f8f9fa",
						padding: "2px 6px",
						borderRadius: "6px",
						fontSize: "13px",
						fontFamily:
							'"Roboto Mono", "SF Mono", Monaco, monospace',
						color: "#d73a49",
						border: "1px solid #e1e4e8",
					}}
					{...props}
				>
					{children}
				</Box>
			);
		}

		return (
			<Box sx={{ margin: "12px 0", position: "relative" }}>
				{/* Copy Button */}
				<Tooltip title={copied ? "Đã copy!" : "Copy code"}>
					<IconButton
						onClick={handleCopy}
						sx={{
							position: "absolute",
							top: 8,
							right: 8,
							zIndex: 1,
							backgroundColor: "rgba(255, 255, 255, 0.1)",
							color: "rgba(255, 255, 255, 0.7)",
							width: 32,
							height: 32,
							"&:hover": {
								backgroundColor: "rgba(255, 255, 255, 0.2)",
								color: "rgba(255, 255, 255, 0.9)",
							},
							transition: "all 0.2s ease",
						}}
					>
						<CopyIcon sx={{ fontSize: 16 }} />
					</IconButton>
				</Tooltip>

				{/* Language Label */}
				{language && (
					<Box
						sx={{
							position: "absolute",
							top: 8,
							left: 12,
							zIndex: 1,
							backgroundColor: "rgba(255, 255, 255, 0.1)",
							color: "rgba(255, 255, 255, 0.7)",
							padding: "2px 8px",
							borderRadius: "4px",
							fontSize: "11px",
							fontWeight: 500,
							textTransform: "uppercase",
							letterSpacing: "0.5px",
						}}
					>
						{language}
					</Box>
				)}

				<SyntaxHighlighter
					style={vscDarkPlus}
					language={language || "text"}
					PreTag="div"
					customStyle={{
						margin: 0,
						borderRadius: "8px",
						fontSize: "13px",
						lineHeight: 1.45,
						fontFamily:
							'"Roboto Mono", "SF Mono", Monaco, monospace',
						paddingTop: "40px", // Add padding to make room for buttons
					}}
					{...props}
				>
					{String(children).replace(/\n$/, "")}
				</SyntaxHighlighter>
			</Box>
		);
	};

	// Load documents for this assistant
	const loadDocuments = async () => {
		try {
			setLoading(true);
			const response = await axiosInstance.get(
				"/api/assistant/documents/"
			);

			// Filter documents for this assistant type
			const assistantDocs = response.data.filter(
				(doc) => doc.assistant_type === assistantType
			);

			setDocuments(assistantDocs);
		} catch (error) {
			console.error("Error loading documents:", error);
			showNotification("Lỗi khi tải danh sách tài liệu", "error");
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		loadDocuments();
	}, [assistantType]);

	// Handle file upload
	const handleFileUpload = async (event) => {
		const file = event.target.files[0];
		if (!file) return;

		const formData = new FormData();
		formData.append("file", file);
		formData.append("assistant_type", assistantType);

		try {
			setUploading(true);
			await axiosInstance.post(
				"/api/assistant/upload-assistant/",
				formData,
				{
					headers: {
						"Content-Type": "multipart/form-data",
					},
				}
			);

			showNotification("Tài liệu đã được upload thành công!", "success");
			loadDocuments(); // Reload documents
		} catch (error) {
			console.error("Upload error:", error);
			const errorMessage =
				error.response?.data?.error || "Lỗi khi upload tài liệu";
			showNotification(errorMessage, "error");
		} finally {
			setUploading(false);
			// Reset file input
			event.target.value = "";
		}
	};

	// Handle document deletion
	const handleDeleteDocument = async (documentId) => {
		if (!window.confirm("Bạn có chắc chắn muốn xóa tài liệu này?")) {
			return;
		}

		try {
			await axiosInstance.delete(
				`/api/assistant/documents/${documentId}/`
			);
			showNotification("Tài liệu đã được xóa thành công!", "success");
			loadDocuments(); // Reload documents
		} catch (error) {
			console.error("Delete error:", error);
			showNotification("Lỗi khi xóa tài liệu", "error");
		}
	};

	// Handle view document
	const handleViewDocument = async (document) => {
		try {
			setViewDialog({ open: true, document, content: "", loading: true });

			const response = await axiosInstance.get(
				`/api/assistant/documents/${document.id}/content/`
			);

			setViewDialog((prev) => ({
				...prev,
				content:
					response.data.content || "Không thể đọc nội dung file này.",
				loading: false,
			}));
		} catch (error) {
			setViewDialog((prev) => ({
				...prev,
				content: "Lỗi khi tải nội dung file. Vui lòng thử lại sau.",
				loading: false,
			}));
			showNotification("Lỗi khi tải nội dung file", "error");
			console.error("View document error:", error);
		}
	};

	const handleCloseViewDialog = () => {
		setViewDialog({
			open: false,
			document: null,
			content: "",
			loading: false,
		});
	};

	const formatFileSize = (bytes) => {
		if (bytes === 0) return "0 Bytes";
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
	};

	// Helper function to check if file is markdown
	const isMarkdownFile = (filename) => {
		return (
			filename &&
			(filename.toLowerCase().endsWith(".md") ||
				filename.toLowerCase().endsWith(".markdown"))
		);
	};

	// Render document content based on file type
	const renderDocumentContent = (document, content) => {
		if (isMarkdownFile(document?.title || "")) {
			return (
				<Box
					sx={{
						fontFamily:
							'"Google Sans", "Roboto", "Helvetica Neue", Arial, sans-serif',
						fontSize: "14px",
						lineHeight: 1.6,
						letterSpacing: "0.2px",
						color: "#3c4043",
						"& p": {
							margin: "0 0 12px 0",
							lineHeight: 1.65,
							fontSize: "14px",
							"&:last-child": {
								marginBottom: 0,
							},
						},
						"& h1, & h2, & h3, & h4, & h5, & h6": {
							margin: "20px 0 12px 0",
							fontWeight: 500,
							color: "#202124",
							letterSpacing: "-0.2px",
							"&:first-child": {
								marginTop: 0,
							},
						},
						"& h1": { fontSize: "1.75rem", fontWeight: 400 },
						"& h2": { fontSize: "1.5rem", fontWeight: 400 },
						"& h3": { fontSize: "1.25rem", fontWeight: 500 },
						// Code styling is handled by CodeBlock component
						"& blockquote": {
							borderLeft: "4px solid #1a73e8",
							paddingLeft: "16px",
							margin: "12px 0",
							fontStyle: "italic",
							color: "#5f6368",
							backgroundColor: "#f8f9fa",
							padding: "12px 16px",
							borderRadius: "4px",
						},
						"& ul, & ol": {
							paddingLeft: "24px",
							margin: "12px 0",
							"& li": {
								marginBottom: "6px",
								lineHeight: 1.6,
							},
						},
						"& table": {
							borderCollapse: "collapse",
							width: "100%",
							margin: "16px 0",
							fontSize: "14px",
							border: "1px solid #dadce0",
							borderRadius: "8px",
							overflow: "hidden",
						},
						"& th, & td": {
							border: "1px solid #dadce0",
							padding: "12px 16px",
							textAlign: "left",
						},
						"& th": {
							backgroundColor: "#f8f9fa",
							fontWeight: 500,
							color: "#202124",
						},
						"& td": {
							color: "#3c4043",
						},
						"& a": {
							color: "#1a73e8",
							textDecoration: "none",
							"&:hover": {
								textDecoration: "underline",
							},
						},
						"& strong": {
							fontWeight: 500,
							color: "#202124",
						},
						"& em": {
							fontStyle: "italic",
							color: "#5f6368",
						},
					}}
				>
					<ReactMarkdown
						remarkPlugins={[remarkGfm]}
						components={{
							code: CodeBlock,
						}}
					>
						{content}
					</ReactMarkdown>
				</Box>
			);
		} else {
			// Plain text rendering for non-markdown files
			return (
				<Typography
					component="pre"
					sx={{
						whiteSpace: "pre-wrap",
						fontFamily: "monospace",
						fontSize: "0.875rem",
						lineHeight: 1.5,
					}}
				>
					{content}
				</Typography>
			);
		}
	};

	const getFileTypeColor = (fileType) => {
		switch (fileType) {
			case "pdf":
				return "error";
			case "txt":
				return "primary";
			case "md":
				return "secondary";
			default:
				return "default";
		}
	};

	return (
		<Box>
			<Box
				sx={{
					display: "flex",
					justifyContent: "space-between",
					alignItems: "center",
					mb: 2,
				}}
			>
				<Typography
					variant="h6"
					sx={{
						color: "#1976d2",
						display: { xs: "none", md: "block" },
					}}
				>
					Tài liệu cho {assistantName}
				</Typography>
				<CustomButton
					variant="contained"
					component="label"
					startIcon={
						uploading ? (
							<CircularProgress size={20} />
						) : (
							<UploadIcon />
						)
					}
					disabled={uploading}
				>
					{uploading ? "Đang upload..." : "Upload Tài liệu"}
					<input
						type="file"
						hidden
						accept=".txt,.md,.pdf"
						onChange={handleFileUpload}
					/>
				</CustomButton>
			</Box>

			{loading ? (
				<Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
					<CircularProgress />
				</Box>
			) : documents.length === 0 ? (
				<Alert severity="info" sx={{ mt: 2 }}>
					Chưa có tài liệu nào cho {assistantName}. Upload tài liệu
					đầu tiên để bắt đầu!
				</Alert>
			) : (
				<Box sx={{ maxHeight: 400, overflow: "auto", p: 2 }}>
					<Box
						sx={{
							display: "flex",
							flexDirection: "column",
							gap: 2,
						}}
					>
						<Grid container spacing={2}>
							{documents.map((document) => (
								<Grid
									size={{ xs: 12, sm: 6, md: 4 }}
									key={document.id}
								>
									<Card
										key={document.id}
										sx={{
											border: "1px solid",
											borderRadius: "20px",
											borderColor: "divider",
											transition: "all 0.2s ease-in-out",
											"&:hover": {
												boxShadow: 2,
												borderColor: "primary.main",
												transform: "translateY(-1px)",
											},
										}}
									>
										<CardContent sx={{ p: 3 }}>
											<Box
												sx={{
													display: "flex",
													alignItems: "flex-start",
													gap: 2,
												}}
											>
												{/* Document Icon */}
												<Box
													sx={{
														p: 1.5,
														borderRadius: 2,
														backgroundColor:
															"primary.main",
														color: "white",
														alignItems: "center",
														justifyContent:
															"center",
														display: {
															xs: "none",
															md: "flex",
														},
													}}
												>
													<DocumentIcon />
												</Box>

												{/* Document Info */}
												<Box
													sx={{
														flex: 1,
														minWidth: 0,
													}}
												>
													{/* Title and Chips */}
													<Box
														sx={{
															display: "flex",
															alignItems:
																"center",
															gap: 1,
															mb: 1,
														}}
													>
														<Typography
															variant="h6"
															sx={{
																fontWeight: 600,
																fontSize:
																	"1rem",
																color: "text.primary",
																overflow:
																	"hidden",
																textOverflow:
																	"ellipsis",
																whiteSpace:
																	"nowrap",
																flex: 1,
															}}
														>
															{document.title}
														</Typography>
														<Chip
															label={document.file_type.toUpperCase()}
															size="small"
															color={getFileTypeColor(
																document.file_type
															)}
															sx={{
																fontWeight: 600,
																display: {
																	xs: "none",
																	md: "block",
																},
															}}
														/>
														{document.is_processed && (
															<Chip
																label="Đã xử lý"
																size="small"
																color="success"
																sx={{
																	fontWeight: 600,
																	display: {
																		xs: "none",
																		md: "block",
																	},
																}}
															/>
														)}
													</Box>

													{/* File Info */}
													<Typography
														variant="body2"
														color="text.secondary"
														sx={{ mb: 2 }}
													>
														{formatFileSize(
															document.file_size
														)}{" "}
														• Tải lên:{" "}
														{new Date(
															document.uploaded_at
														).toLocaleDateString(
															"vi-VN",
															{
																year: "numeric",
																month: "long",
																day: "numeric",
															}
														)}
													</Typography>

													{/* Action Buttons */}
													<Box
														sx={{
															display: {
																xs: "none",
																md: "flex",
															},
															gap: 1,
														}}
													>
														<Button
															variant="outlined"
															size="small"
															startIcon={
																<ViewIcon />
															}
															onClick={() =>
																handleViewDocument(
																	document
																)
															}
															sx={{
																borderRadius: 2,
																textTransform:
																	"none",
																fontWeight: 500,
															}}
														>
															Xem nội dung
														</Button>
														<Button
															variant="outlined"
															size="small"
															color="error"
															startIcon={
																<DeleteIcon />
															}
															onClick={() =>
																handleDeleteDocument(
																	document.id
																)
															}
															sx={{
																borderRadius: 2,
																textTransform:
																	"none",
																fontWeight: 500,
															}}
														>
															Xóa
														</Button>
													</Box>
													<Box
														sx={{
															display: {
																xs: "flex",
																md: "none",
															},
															gap: 1,
														}}
													>
														<IconButton
															onClick={() =>
																handleViewDocument(
																	document
																)
															}
															sx={{
																color: "text.primary",
															}}
														>
															<ViewIcon />
														</IconButton>
														<IconButton
															onClick={() =>
																handleDeleteDocument(
																	document.id
																)
															}
															sx={{
																color: "error.main",
															}}
														>
															<DeleteIcon />
														</IconButton>
													</Box>
												</Box>
											</Box>
										</CardContent>
									</Card>
								</Grid>
							))}
						</Grid>
					</Box>
				</Box>
			)}

			{/* View Document Dialog */}
			<Dialog
				open={viewDialog.open}
				onClose={handleCloseViewDialog}
				maxWidth="md"
				fullWidth
				PaperProps={{
					sx: { height: "80vh", borderRadius: "20px" },
				}}
			>
				<DialogTitle>
					<Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
						<Typography variant="h6" component="span">
							{viewDialog.document?.title}
						</Typography>
						{viewDialog.document &&
							isMarkdownFile(viewDialog.document.title) && (
								<Chip
									label="Markdown"
									size="small"
									color="primary"
									sx={{ fontWeight: 600 }}
								/>
							)}
					</Box>
				</DialogTitle>
				<DialogContent dividers>
					{viewDialog.loading ? (
						<Box
							sx={{
								display: "flex",
								justifyContent: "center",
								py: 4,
							}}
						>
							<CircularProgress />
						</Box>
					) : (
						renderDocumentContent(
							viewDialog.document,
							viewDialog.content
						)
					)}
				</DialogContent>
				<DialogActions>
					<Button onClick={handleCloseViewDialog}>Đóng</Button>
				</DialogActions>
			</Dialog>
		</Box>
	);
};

export default AssistantDocumentManager;
