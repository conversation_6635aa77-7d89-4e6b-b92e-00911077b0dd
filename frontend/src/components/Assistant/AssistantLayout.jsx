import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
	Box,
	Drawer,
	AppBar,
	Toolbar,
	List,
	Typography,
	Divider,
	IconButton,
	ListItem,
	ListItemButton,
	ListItemIcon,
	ListItemText,
	Avatar,
	Menu,
	MenuItem,
	useTheme,
	useMediaQuery,
} from "@mui/material";
import {
	Menu as MenuIcon,
	Dashboard as DashboardIcon,
	Description as DocumentIcon,
	Settings as SettingsIcon,
	Chat as ChatIcon,
	SmartToy as AssistantIcon,
	Home as HomeIcon,
	AccountCircle,
	Logout,
	ChevronLeft,
} from "@mui/icons-material";
import { beeColors } from "../Common/CustomButton";

const drawerWidth = 280;
const miniDrawerWidth = 64;

const AssistantLayout = ({ user, setUser, children }) => {
	const navigate = useNavigate();
	const location = useLocation();
	const theme = useTheme();
	const isMobile = useMediaQuery(theme.breakpoints.down("md"));

	const [drawerOpen, setDrawerOpen] = useState(!isMobile);
	const [anchorEl, setAnchorEl] = useState(null);

	const menuItems = [
		{
			text: "Tổng quan",
			icon: <DashboardIcon />,
			path: "/assistant",
			color: beeColors.primary.main,
		},
		{
			text: "Tài liệu",
			icon: <DocumentIcon />,
			path: "/assistant/documents",
			color: beeColors.secondary.main,
		},
		{
			text: "Cài đặt",
			icon: <SettingsIcon />,
			path: "/assistant/settings",
			color: beeColors.neutral.main,
		},
		{
			text: "Chat Playground",
			icon: <ChatIcon />,
			path: "/assistant/chat",
			color: beeColors.accent.main,
		},
		{
			text: "Trang chủ",
			icon: <HomeIcon />,
			path: "/",
			color: beeColors.warning?.main || "#FF9800",
		},
	];

	const handleDrawerToggle = () => {
		setDrawerOpen(!drawerOpen);
	};

	const handleMenuClick = (event) => {
		setAnchorEl(event.currentTarget);
	};

	const handleMenuClose = () => {
		setAnchorEl(null);
	};

	const handleLogout = () => {
		setUser(null);
		navigate("/");
		handleMenuClose();
	};

	const isActivePath = (path) => {
		if (path === "/assistant" && location.pathname === "/assistant")
			return true;
		if (path !== "/assistant" && location.pathname.startsWith(path))
			return true;
		return false;
	};

	const drawer = (isExpanded = true) => (
		<Box sx={{ height: "100%", display: "flex", flexDirection: "column" }}>
			{/* Header */}
			<Box
				sx={{
					p: isExpanded ? 3 : 1,
					background: `linear-gradient(135deg, ${beeColors.primary.main} 0%, ${beeColors.secondary.main} 100%)`,
					color: "white",
					textAlign: "center",
					minHeight: isExpanded ? "auto" : 64,
					display: "flex",
					flexDirection: "column",
					justifyContent: "center",
				}}
			>
				{isExpanded ? (
					<>
						<Typography
							variant="h5"
							fontWeight="bold"
							sx={{ mb: 1 }}
						>
							BeE Assistant
						</Typography>
						<Typography variant="body2" sx={{ opacity: 0.9 }}>
							AI Dashboard
						</Typography>
					</>
				) : (
					<Typography variant="h6" fontWeight="bold">
						BeE
					</Typography>
				)}
			</Box>

			<Divider />

			{/* Navigation Menu */}
			<List sx={{ flexGrow: 1, px: isExpanded ? 2 : 1, py: 2 }}>
				{menuItems.map((item) => (
					<React.Fragment key={item.text}>
						{item.text === "Trang chủ" && (
							<Divider sx={{ mb: 1 }} />
						)}
						<ListItem disablePadding sx={{ mb: 1 }}>
							<ListItemButton
								onClick={() => {
									navigate(item.path);
									if (isMobile) setDrawerOpen(false);
								}}
								sx={{
									minHeight: 48,
									backgroundColor: isActivePath(item.path)
										? `${item.color}15`
										: "transparent",
									color: isActivePath(item.path)
										? item.color
										: theme.palette.text.primary,
									"&:hover": {
										backgroundColor: `${item.color}10`,
										color: item.color,
									},
									borderRadius: "20px",
									transition: "all 0.2s ease-in-out",
									justifyContent: isExpanded
										? "initial"
										: "center",
									px: isExpanded ? 2 : 1,
								}}
							>
								<ListItemIcon
									sx={{
										minWidth: 0,
										mr: isExpanded ? 2 : "auto",
										justifyContent: "center",
										color: isActivePath(item.path)
											? item.color
											: "inherit",
									}}
								>
									{item.icon}
								</ListItemIcon>
								{isExpanded && (
									<ListItemText
										primary={item.text}
										sx={{
											"& .MuiListItemText-primary": {
												fontWeight: isActivePath(
													item.path
												)
													? 600
													: 400,
											},
										}}
									/>
								)}
							</ListItemButton>
						</ListItem>
					</React.Fragment>
				))}
			</List>

			{/* User Profile Section */}
			{isExpanded && (
				<Box
					sx={{
						p: 2,
						borderTop: `1px solid ${theme.palette.divider}`,
					}}
				>
					<Box
						sx={{
							display: "flex",
							alignItems: "center",
							p: 1,
							borderRadius: 2,
							backgroundColor: `${beeColors.neutral.main}05`,
						}}
					>
						<Avatar
							sx={{
								width: 32,
								height: 32,
								mr: 2,
								bgcolor: beeColors.primary.main,
							}}
						>
							{user?.username?.charAt(0).toUpperCase() || "U"}
						</Avatar>
						<Box sx={{ flexGrow: 1 }}>
							<Typography variant="body2" fontWeight="medium">
								{user?.username || "User"}
							</Typography>
							<Typography
								variant="caption"
								color="text.secondary"
							>
								AI Assistant User
							</Typography>
						</Box>
						<IconButton size="small" onClick={handleMenuClick}>
							<AccountCircle />
						</IconButton>
					</Box>
				</Box>
			)}
		</Box>
	);

	return (
		<Box sx={{ display: "flex", height: "100vh" }}>
			{/* AppBar */}
			<AppBar
				position="fixed"
				sx={{
					width: {
						md: drawerOpen
							? `calc(100% - ${drawerWidth}px)`
							: `calc(100% - ${miniDrawerWidth}px)`,
					},
					ml: {
						md: drawerOpen
							? `${drawerWidth}px`
							: `${miniDrawerWidth}px`,
					},
					backgroundColor: beeColors.background.paper,
					color: beeColors.neutral.main,
					boxShadow: `0 2px 12px ${beeColors.neutral.main}10`,
					borderBottom: `1px solid ${beeColors.neutral.main}10`,
					zIndex: theme.zIndex.drawer - 1,
					transition: theme.transitions.create(["width", "margin"], {
						easing: theme.transitions.easing.sharp,
						duration: theme.transitions.duration.enteringScreen,
					}),
				}}
			>
				<Toolbar>
					<IconButton
						color="inherit"
						aria-label="toggle drawer"
						onClick={handleDrawerToggle}
						edge="start"
						sx={{ mr: 2 }}
					>
						{drawerOpen ? <ChevronLeft /> : <MenuIcon />}
					</IconButton>

					<Typography
						variant="h6"
						noWrap
						component="div"
						sx={{ flexGrow: 1, fontWeight: 600 }}
					>
						BeE Assistant Dashboard
					</Typography>

					<IconButton color="inherit" onClick={handleMenuClick}>
						<AccountCircle />
					</IconButton>
				</Toolbar>
			</AppBar>

			{/* Drawer */}
			<Box
				component="nav"
				sx={{
					width: { md: drawerOpen ? drawerWidth : miniDrawerWidth },
					flexShrink: { md: 0 },
				}}
			>
				{/* Mobile drawer */}
				<Drawer
					variant="temporary"
					open={drawerOpen}
					onClose={handleDrawerToggle}
					ModalProps={{ keepMounted: true }}
					sx={{
						display: { xs: "block", md: "none" },
						"& .MuiDrawer-paper": {
							boxSizing: "border-box",
							width: drawerWidth,
							backgroundColor: beeColors.background.paper,
							borderRight: `1px solid ${beeColors.neutral.main}10`,
						},
					}}
				>
					{drawer(true)}
				</Drawer>

				{/* Desktop drawer */}
				<Drawer
					variant="permanent"
					sx={{
						display: { xs: "none", md: "block" },
						"& .MuiDrawer-paper": {
							boxSizing: "border-box",
							width: drawerOpen ? drawerWidth : miniDrawerWidth,
							backgroundColor: beeColors.background.paper,
							borderRight: `1px solid ${beeColors.neutral.main}10`,
							transition: theme.transitions.create("width", {
								easing: theme.transitions.easing.sharp,
								duration:
									theme.transitions.duration.enteringScreen,
							}),
							overflowX: "hidden",
						},
					}}
					open={drawerOpen}
				>
					{drawer(drawerOpen)}
				</Drawer>
			</Box>

			{/* Main content */}
			<Box
				component="main"
				sx={{
					overflow: "auto",
					flexGrow: 1,
					p: 3,
					mt: "56px",
					backgroundColor: beeColors.background.main,
					// minHeight: "calc(100vh - 64px)",
					width: {
						md: drawerOpen
							? `calc(100% - ${drawerWidth}px)`
							: `calc(100% - ${miniDrawerWidth}px)`,
					},
					transition: theme.transitions.create(["width", "margin"], {
						easing: theme.transitions.easing.sharp,
						duration: theme.transitions.duration.enteringScreen,
					}),
				}}
			>
				{children}
			</Box>

			{/* User Menu */}
			<Menu
				anchorEl={anchorEl}
				open={Boolean(anchorEl)}
				onClose={handleMenuClose}
			>
				<MenuItem onClick={handleMenuClose}>
					<ListItemIcon>
						<AccountCircle fontSize="small" />
					</ListItemIcon>
					Profile
				</MenuItem>
				<MenuItem onClick={handleLogout}>
					<ListItemIcon>
						<Logout fontSize="small" />
					</ListItemIcon>
					Logout
				</MenuItem>
			</Menu>
		</Box>
	);
};

export default AssistantLayout;
