import React, { useState, useEffect, useRef } from "react";
import {
    Box,
    Typography,
    Paper,
    TextField,
    IconButton,
    Avatar,
    Chip,
    Card,
    CardContent,
    Switch,
    FormControlLabel,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Divider,
    Alert,
    CircularProgress,
    Tooltip,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import { styled } from "@mui/material/styles";
import {
    Send as SendIcon,
    SmartToy as BotIcon,
    Person as UserIcon,
    Description as DocumentIcon,
    Clear as ClearIcon,
    Add as NewChatIcon,
    Delete as DeleteIcon,
    ContentCopy as CopyIcon,
    Refresh as RetryIcon,
} from "@mui/icons-material";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";

import { beeColors } from "../Common/CustomButton";
import CustomButton from "../Common/CustomButton";
import axiosInstance from "../../services/axiosInstance";
import { getOrCreateSession, loadSessionMessages, isAdminOrSupervisor } from "../../utils/sessionUtils";
import AssistantDocumentViewer from "./AssistantDocumentViewer";
import { grey } from "@mui/material/colors";

// Custom Code Block Component with Syntax Highlighting
const CodeBlock = ({ node, inline, className, children, ...props }) => {
    const match = /language-(\w+)/.exec(className || "");
    const language = match ? match[1] : "";
    const [copied, setCopied] = useState(false);

    const handleCopy = async () => {
        try {
            const codeText = String(children).replace(/\n$/, "");
            await navigator.clipboard.writeText(codeText);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error("Failed to copy code:", err);
        }
    };

    if (language === "") {
        return (
            <Box
                component="code"
                sx={{
                    backgroundColor: "#f8f9fa",
                    padding: "2px 6px",
                    borderRadius: "6px",
                    fontSize: "13px",
                    fontFamily: '"Roboto Mono", "SF Mono", Monaco, monospace',
                    color: "#d73a49",
                    border: "1px solid #e1e4e8",
                }}
                {...props}
            >
                {children}
            </Box>
        );
    }

    return (
        <Box sx={{ margin: "12px 0", position: "relative" }}>
            {/* Copy Button */}
            <Tooltip title={copied ? "Đã copy!" : "Copy code"}>
                <IconButton
                    onClick={handleCopy}
                    sx={{
                        position: "absolute",
                        top: 8,
                        right: 8,
                        zIndex: 1,
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                        color: "rgba(255, 255, 255, 0.7)",
                        width: 32,
                        height: 32,
                        "&:hover": {
                            backgroundColor: "rgba(255, 255, 255, 0.2)",
                            color: "rgba(255, 255, 255, 0.9)",
                        },
                        transition: "all 0.2s ease",
                    }}
                >
                    <CopyIcon sx={{ fontSize: 16 }} />
                </IconButton>
            </Tooltip>

            {/* Language Label */}
            {language && (
                <Box
                    sx={{
                        position: "absolute",
                        top: 8,
                        left: 12,
                        zIndex: 1,
                        backgroundColor: "rgba(255, 255, 255, 0.1)",
                        color: "rgba(255, 255, 255, 0.7)",
                        padding: "2px 8px",
                        borderRadius: "4px",
                        fontSize: "11px",
                        fontWeight: 500,
                        textTransform: "uppercase",
                        letterSpacing: "0.5px",
                    }}
                >
                    {language}
                </Box>
            )}

            <SyntaxHighlighter
                style={vscDarkPlus}
                language={language || "text"}
                PreTag="div"
                customStyle={{
                    margin: 0,
                    fontSize: "13px",
                    lineHeight: 1.45,
                    fontFamily: '"Roboto Mono", "SF Mono", Monaco, monospace',
                    paddingTop: "40px", // Add padding to make room for buttons
                    borderRadius: "20px",
                }}
                {...props}
            >
                {String(children).replace(/\n$/, "")}
            </SyntaxHighlighter>
        </Box>
    );
};

const ChatContainer = styled(Box)(({ theme }) => ({
    height: "calc(100vh - 200px)", // Sử dụng toàn bộ chiều cao màn hình trừ header/padding
    display: "flex",
    flexDirection: "column",
    backgroundColor: beeColors.background.main,
    borderRadius: "16px",
    overflow: "hidden",
}));

const MessagesArea = styled(Box)(({ theme }) => ({
    flex: 1,
    overflowY: "auto",
    padding: "16px",
    display: "flex",
    flexDirection: "column",
    gap: "16px",
    "&::-webkit-scrollbar": {
        width: "6px",
    },
    "&::-webkit-scrollbar-track": {
        background: beeColors.background.main,
    },
    "&::-webkit-scrollbar-thumb": {
        background: beeColors.neutral.light,
        borderRadius: "3px",
    },
}));

const MessageBubble = styled(Paper)(({ isUser }) => ({
    maxWidth: "70%",
    padding: "12px 16px",
    borderRadius: "18px",
    alignSelf: isUser ? "flex-end" : "flex-start",
    backgroundColor: isUser ? beeColors.primary.main : beeColors.background.paper,
    color: isUser ? beeColors.primary.contrastText : beeColors.neutral.main,
    boxShadow: `0 2px 8px ${beeColors.neutral.main}10`,
    position: "relative",
    "&::before": {
        content: '""',
        position: "absolute",
        width: 0,
        height: 0,
        border: "8px solid transparent",
        ...(isUser
            ? {
                  right: "-8px",
                  top: "12px",
                  borderLeftColor: beeColors.primary.main,
              }
            : {
                  left: "-8px",
                  top: "12px",
                  borderRightColor: beeColors.background.paper,
              }),
    },
}));

const InputArea = styled(Box)(({ theme }) => ({
    padding: "16px",
    backgroundColor: beeColors.background.paper,
    borderTop: `1px solid ${beeColors.neutral.main}10`,
    display: "flex",
    gap: "12px",
    alignItems: "flex-end",
    marginTop: "auto", // Đẩy input area xuống dưới cùng
}));

const TypingIndicator = styled(Box)(({ theme }) => ({
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "12px 16px",
    backgroundColor: beeColors.background.paper,
    borderRadius: "18px",
    alignSelf: "flex-start",
    boxShadow: `0 2px 8px ${beeColors.neutral.main}10`,
    "& .dot": {
        width: "8px",
        height: "8px",
        borderRadius: "50%",
        backgroundColor: beeColors.neutral.light,
        animation: "typing 1.4s infinite ease-in-out",
        "&:nth-of-type(1)": { animationDelay: "-0.32s" },
        "&:nth-of-type(2)": { animationDelay: "-0.16s" },
    },
    "@keyframes typing": {
        "0%, 80%, 100%": { transform: "scale(0)" },
        "40%": { transform: "scale(1)" },
    },
}));

const MarkdownContainer = styled(Box)(({ theme }) => ({
    fontFamily: '"Google Sans", "Roboto", "Helvetica Neue", Arial, sans-serif',
    fontSize: "14px",
    lineHeight: 1.6,
    letterSpacing: "0.2px",
    color: "#3c4043",
    "& p": {
        margin: "0 0 12px 0",
        lineHeight: 1.65,
        fontSize: "14px",
        "&:last-child": {
            marginBottom: 0,
        },
    },
    "& h1, & h2, & h3, & h4, & h5, & h6": {
        margin: "20px 0 12px 0",
        fontWeight: 500,
        color: "#202124",
        letterSpacing: "-0.2px",
        "&:first-child": {
            marginTop: 0,
        },
    },
    "& h1": { fontSize: "1.75rem", fontWeight: 400 },
    "& h2": { fontSize: "1.5rem", fontWeight: 400 },
    "& h3": { fontSize: "1.25rem", fontWeight: 500 },
    // Code styling is handled by CodeBlock component
    "& blockquote": {
        borderLeft: "4px solid #1a73e8",
        paddingLeft: "16px",
        margin: "12px 0",
        fontStyle: "italic",
        color: "#5f6368",
        backgroundColor: "#f8f9fa",
        padding: "12px 16px",
        borderRadius: "4px",
    },
    "& ul, & ol": {
        paddingLeft: "24px",
        margin: "12px 0",
        "& li": {
            marginBottom: "6px",
            lineHeight: 1.6,
        },
    },
    "& table": {
        borderCollapse: "collapse",
        width: "100%",
        margin: "16px 0",
        fontSize: "14px",
        border: "1px solid #dadce0",
        borderRadius: "8px",
        overflow: "hidden",
    },
    "& th, & td": {
        border: "1px solid #dadce0",
        padding: "12px 16px",
        textAlign: "left",
    },
    "& th": {
        backgroundColor: "#f8f9fa",
        fontWeight: 500,
        color: "#202124",
    },
    "& td": {
        color: "#3c4043",
    },
    "& a": {
        color: "#1a73e8",
        textDecoration: "none",
        "&:hover": {
            textDecoration: "underline",
        },
    },
    "& strong": {
        fontWeight: 500,
        color: "#202124",
    },
    "& em": {
        fontStyle: "italic",
        color: "#5f6368",
    },
}));

const ChatPlayground = ({ user, showNotification, setLoading }) => {
    const [messages, setMessages] = useState([]);
    const [inputMessage, setInputMessage] = useState("");
    const [isTyping, setIsTyping] = useState(false);
    const [currentSessionId, setCurrentSessionId] = useState(null);
    const [useDocuments, setUseDocuments] = useState(true);
    const [contextDocuments, setContextDocuments] = useState([]);
    const messagesEndRef = useRef(null);

    // Prompt selection state
    const [selectedPrompt, setSelectedPrompt] = useState("bee_assistant_prompt");
    const [availablePrompts, setAvailablePrompts] = useState({
        bee_assistant_prompt: "BeE Assistant",
        bee_ide_prompt: "BeE IDE Assistant",
        python_prompt: "BeE Python Assistant",
        teacher_prompt: "BeE Teacher Assistant",
    });
    const [promptsConfig, setPromptsConfig] = useState({
        bee_assistant_prompt: "",
        bee_ide_prompt: "",
        python_prompt: "",
        teacher_prompt: "",
    });

    // Session history state
    const [sessions, setSessions] = useState([]);
    const [loadingSessions, setLoadingSessions] = useState(false);
    const [isAdminUser, setIsAdminUser] = useState(false);

    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages, isTyping]);

    // Load prompts configuration
    useEffect(() => {
        const loadPromptsConfig = async () => {
            try {
                const response = await axiosInstance.get("/api/assistant/config/");
                if (response.data) {
                    setPromptsConfig({
                        bee_assistant_prompt: response.data.bee_assistant_prompt || "",
                        bee_ide_prompt: response.data.bee_ide_prompt || "",
                        python_prompt: response.data.python_prompt || "",
                        teacher_prompt: response.data.teacher_prompt || "",
                    });
                }
            } catch (error) {
                console.error("Error loading prompts config:", error);
            }
        };
        loadPromptsConfig();
    }, []);

    // Check user permissions and initialize session
    useEffect(() => {
        const initializeSession = async () => {
            try {
                // Check if user is admin or supervisor
                const hasPermission = await isAdminOrSupervisor();
                setIsAdminUser(hasPermission);

                const getAssistantType = (promptKey) => {
                    const mapping = {
                        bee_assistant_prompt: "bee_assistant",
                        bee_ide_prompt: "bee_ide",
                        python_prompt: "python",
                        teacher_prompt: "teacher",
                    };
                    return mapping[promptKey] || "bee_assistant";
                };

                const assistantType = getAssistantType(selectedPrompt);
                const sessionId = await getOrCreateSession(assistantType);
                setCurrentSessionId(sessionId);

                // Load messages from the session (only for admin/supervisor)
                if (hasPermission) {
                    const messages = await loadSessionMessages(sessionId);
                    setMessages(messages);
                } else {
                    console.error("ChatPlayground initialized with new session for regular user:", sessionId);
                }
            } catch (error) {
                console.error("Error initializing session:", error);
            }
        };

        initializeSession();
    }, []);

    // Load sessions when component mounts or when selectedPrompt changes (only for admin/supervisor)
    useEffect(() => {
        if (isAdminUser) {
            loadSessions();
        }
    }, [selectedPrompt, isAdminUser]);

    const loadSessions = async () => {
        try {
            setLoadingSessions(true);
            // Map selectedPrompt to assistant_type for session filtering
            const getAssistantType = (promptKey) => {
                const mapping = {
                    bee_assistant_prompt: "bee_assistant",
                    bee_ide_prompt: "bee_ide",
                    python_prompt: "python",
                    teacher_prompt: "teacher",
                };
                return mapping[promptKey] || "bee_assistant";
            };

            const assistantType = getAssistantType(selectedPrompt);
            const response = await axiosInstance.get(`/api/assistant/chat-sessions/?assistant_type=${assistantType}`);
            setSessions(response.data.results || response.data || []);
        } catch (error) {
            console.error("Error loading sessions:", error);
            setSessions([]);
        } finally {
            setLoadingSessions(false);
        }
    };

    const loadSession = async (sessionId) => {
        try {
            setLoading(true);
            const response = await axiosInstance.get(`/api/assistant/chat-sessions/${sessionId}/`);
            const session = response.data;

            // Load messages from session
            setMessages(session.messages || []);
            setCurrentSessionId(sessionId);

            // Clear input
            setInputMessage("");

            showNotification(`Đã tải session: ${session.title}`, "success");
        } catch (error) {
            console.error("Error loading session:", error);
            showNotification("Lỗi khi tải session", "error");
        } finally {
            setLoading(false);
        }
    };

    const deleteSession = async (sessionId, event) => {
        // Prevent triggering loadSession when clicking delete
        event.stopPropagation();

        if (!window.confirm("Bạn có chắc chắn muốn xóa session này?")) {
            return;
        }

        try {
            await axiosInstance.delete(`/api/assistant/chat-sessions/${sessionId}/`);

            // If deleting current session, clear chat and create new session
            if (currentSessionId === sessionId) {
                setMessages([]);
                setContextDocuments([]);
                const newSessionId = await getOrCreateSession();
                setCurrentSessionId(newSessionId);
            }

            // Reload sessions
            loadSessions();
            showNotification("Đã xóa session thành công", "success");
        } catch (error) {
            console.error("Error deleting session:", error);
            showNotification("Lỗi khi xóa session", "error");
        }
    };

    const createNewSession = async () => {
        try {
            // Map selectedPrompt to assistant_type
            const getAssistantType = (promptKey) => {
                const mapping = {
                    bee_assistant_prompt: "bee_assistant",
                    bee_ide_prompt: "bee_ide",
                    python_prompt: "python",
                    teacher_prompt: "teacher",
                };
                return mapping[promptKey] || "bee_assistant";
            };

            const response = await axiosInstance.post("/api/assistant/chat-sessions/", {
                title: `Chat ${new Date().toLocaleString()}`,
                assistant_type: getAssistantType(selectedPrompt),
            });
            return response.data.id;
        } catch (error) {
            console.error("Error creating session:", error);
            return null;
        }
    };

    const handleSendMessage = async () => {
        if (!inputMessage.trim()) return;

        const getAssistantType = (promptKey) => {
            const mapping = {
                bee_assistant_prompt: "bee_assistant",
                bee_ide_prompt: "bee_ide",
                python_prompt: "python",
                teacher_prompt: "teacher",
            };
            return mapping[promptKey] || "bee_assistant";
        };

        const userMessage = {
            id: Date.now(),
            role: "user",
            content: inputMessage,
            assistant_type: getAssistantType(selectedPrompt),
            timestamp: new Date().toISOString(),
        };

        setMessages((prev) => [...prev, userMessage]);
        setInputMessage("");
        setIsTyping(true);

        try {
            // Get or create session if we don't have one
            let sessionId = currentSessionId;
            if (!sessionId) {
                sessionId = await getOrCreateSession();
                if (!sessionId) {
                    throw new Error("Failed to get or create chat session");
                }
                setCurrentSessionId(sessionId);
            }

            // // Map selectedPrompt to assistant_type
            // const getAssistantType = (promptKey) => {
            //     const mapping = {
            //         bee_assistant_prompt: "bee_assistant",
            //         bee_ide_prompt: "bee_ide",
            //         python_prompt: "python",
            //         teacher_prompt: "teacher",
            //     };
            //     return mapping[promptKey] || "bee_assistant";
            // };

            const response = await axiosInstance.post("/api/assistant/chat/", {
                message: inputMessage,
                session_id: sessionId,
                use_documents: useDocuments,
                system_prompt: promptsConfig[selectedPrompt] || "",
                assistant_type: getAssistantType(selectedPrompt),
            });

            const assistantMessage = {
                id: Date.now() + 1,
                role: "assistant",
                content: response.data.message,
                timestamp: new Date().toISOString(),
                tokens_used: response.data.tokens_used,
                context_documents: response.data.context_documents || [],
            };

            setMessages((prev) => [...prev, assistantMessage]);
            setCurrentSessionId(response.data.session_id);
            setContextDocuments(response.data.context_documents || []);
            // Reload sessions to update message count
            loadSessions();
        } catch (error) {
            console.error("Chat error:", error);
            const errorMessage = {
                id: Date.now() + 1,
                role: "assistant",
                content: error.response?.data?.error || "Đã xảy ra lỗi khi gửi tin nhắn. Vui lòng thử lại.",
                timestamp: new Date().toISOString(),
                isError: true,
            };
            setMessages((prev) => [...prev, errorMessage]);
            showNotification("Lỗi khi gửi tin nhắn", "error");
        } finally {
            setIsTyping(false);
        }
    };

    const handleKeyPress = (e) => {
        if (e.key === "Enter" && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    const handleRetryMessage = async (messageIndex) => {
        // Find the user message that corresponds to this error message
        const userMessageIndex = messageIndex - 1;
        if (userMessageIndex < 0 || messages[userMessageIndex].role !== "user") {
            showNotification("Không thể tìm thấy tin nhắn gốc để thử lại", "error");
            return;
        }

        const userMessage = messages[userMessageIndex];

        // Remove the error message and set typing
        setMessages((prev) => prev.slice(0, messageIndex));
        setIsTyping(true);

        const getAssistantType = (promptKey) => {
            const mapping = {
                bee_assistant_prompt: "bee_assistant",
                bee_ide_prompt: "bee_ide",
                python_prompt: "python",
                teacher_prompt: "teacher",
            };
            return mapping[promptKey] || "bee_assistant";
        };

        try {
            // Get or create session if we don't have one
            let sessionId = currentSessionId;
            if (!sessionId) {
                sessionId = await getOrCreateSession();
                if (!sessionId) {
                    throw new Error("Failed to get or create chat session");
                }
                setCurrentSessionId(sessionId);
            }

            const response = await axiosInstance.post("/api/assistant/chat/", {
                message: userMessage.content,
                session_id: sessionId,
                use_documents: useDocuments,
                system_prompt: promptsConfig[selectedPrompt] || "",
                assistant_type: getAssistantType(selectedPrompt),
            });

            const assistantMessage = {
                id: Date.now() + 1,
                role: "assistant",
                content: response.data.message,
                timestamp: new Date().toISOString(),
                tokens_used: response.data.tokens_used,
                context_documents: response.data.context_documents || [],
            };

            setMessages((prev) => [...prev, assistantMessage]);
            setCurrentSessionId(response.data.session_id);
            setContextDocuments(response.data.context_documents || []);
            // Reload sessions to update message count
            loadSessions();
        } catch (error) {
            console.error("Retry error:", error);
            const errorMessage = {
                id: Date.now() + 1,
                role: "assistant",
                content: error.response?.data?.error || "Đã xảy ra lỗi khi gửi tin nhắn. Vui lòng thử lại.",
                timestamp: new Date().toISOString(),
                isError: true,
            };
            setMessages((prev) => [...prev, errorMessage]);
            showNotification("Lỗi khi thử lại tin nhắn", "error");
        } finally {
            setIsTyping(false);
        }
    };

    const clearChat = async () => {
        setMessages([]);
        setContextDocuments([]);
        // Create a new session for the new chat
        const newSessionId = await createNewSession();
        setCurrentSessionId(newSessionId);
        // Reload sessions to show the new one
        loadSessions();
        showNotification("Đã tạo cuộc trò chuyện mới", "info");
    };

    const formatTimestamp = (timestamp) => {
        return new Date(timestamp).toLocaleTimeString("vi-VN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
        });
    };

    return (
        <Box sx={{ p: 4 }}>
            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 3 }}>
                <Typography variant="h5" fontWeight="bold" sx={{ color: beeColors.neutral.main }}>
                    Chat Playground
                </Typography>
                <Box sx={{ display: { xs: "none", md: "flex" }, gap: 2, alignItems: "center" }}>
                    <FormControlLabel
                        control={
                            <Switch
                                checked={useDocuments}
                                onChange={(e) => setUseDocuments(e.target.checked)}
                                color="primary"
                            />
                        }
                        label="Sử dụng tài liệu"
                    />
                    <CustomButton
                        variant="outlined"
                        startIcon={<NewChatIcon />}
                        onClick={clearChat}
                        disabled={messages.length === 0}
                        sx={{ mr: 2 }}
                    >
                        Chat Mới
                    </CustomButton>
                </Box>
                <IconButton
                    onClick={clearChat}
                    disabled={messages.length === 0}
                    sx={{ display: { xs: "flex", md: "none" }, bgcolor: beeColors.primary.main }}
                >
                    <NewChatIcon sx={{ color: "white" }} />
                </IconButton>
            </Box>

            <Grid container spacing={3} sx={{ height: "calc(100vh - 200px)" }}>
                {/* Chat Area */}
                <Grid size={{ xs: 12, md: 8 }} sx={{ height: "100%" }}>
                    <ChatContainer>
                        <MessagesArea>
                            {messages.length === 0 && (
                                <Box
                                    sx={{
                                        textAlign: "center",
                                        py: 8,
                                        color: beeColors.neutral.light,
                                    }}
                                >
                                    <BotIcon sx={{ fontSize: 64, mb: 2, opacity: 0.5 }} />
                                    <Typography variant="h6" sx={{ mb: 1 }}>
                                        Chào mừng đến với BeE Assistant!
                                    </Typography>
                                    <Typography variant="body2">
                                        Hãy bắt đầu cuộc trò chuyện bằng cách gửi tin nhắn đầu tiên
                                    </Typography>
                                </Box>
                            )}

                            {messages.map((message) => (
                                <Box key={message.id} sx={{ display: "flex", alignItems: "flex-start", gap: 1 }}>
                                    <Avatar
                                        sx={{
                                            width: 32,
                                            height: 32,
                                            backgroundColor:
                                                message.role === "user"
                                                    ? beeColors.primary.main
                                                    : beeColors.secondary.main,
                                            order: message.role === "user" ? 2 : 0,
                                        }}
                                    >
                                        {message.role === "user" ? <UserIcon /> : <BotIcon />}
                                    </Avatar>

                                    <Box
                                        sx={{
                                            flex: 1,
                                            order: message.role === "user" ? 1 : 1,
                                            display: "flex",
                                            flexDirection: "column",
                                            alignItems: message.role === "user" ? "flex-end" : "flex-start",
                                        }}
                                    >
                                        <MessageBubble isUser={message.role === "user"} elevation={0}>
                                            {message.role === "assistant" ? (
                                                <MarkdownContainer
                                                    sx={{
                                                        color: message.isError ? "#E74C3C" : "inherit",
                                                    }}
                                                >
                                                    <ReactMarkdown
                                                        remarkPlugins={[remarkGfm]}
                                                        components={{
                                                            code: CodeBlock,
                                                        }}
                                                    >
                                                        {message.content}
                                                    </ReactMarkdown>
                                                </MarkdownContainer>
                                            ) : (
                                                <Typography
                                                    variant="body1"
                                                    sx={{
                                                        whiteSpace: "pre-wrap",
                                                        color: message.isError ? "#E74C3C" : "inherit",
                                                    }}
                                                >
                                                    {message.content}
                                                </Typography>
                                            )}

                                            {message.context_documents && message.context_documents.length > 0 && (
                                                <Box sx={{ mt: 1, display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                                                    {message.context_documents.map((doc, index) => (
                                                        <Chip
                                                            key={index}
                                                            icon={<DocumentIcon />}
                                                            label={doc.title}
                                                            size="small"
                                                            variant="outlined"
                                                            sx={{
                                                                backgroundColor: "rgba(255,255,255,0.2)",
                                                                color: "inherit",
                                                                borderColor: "rgba(255,255,255,0.3)",
                                                            }}
                                                        />
                                                    ))}
                                                </Box>
                                            )}
                                        </MessageBubble>

                                        <Box sx={{ display: "flex", alignItems: "center", gap: 1, mt: 0.5 }}>
                                            <Typography
                                                variant="caption"
                                                sx={{
                                                    color: beeColors.neutral.light,
                                                    fontSize: "0.75rem",
                                                }}
                                            >
                                                {formatTimestamp(message.timestamp)}
                                                {message.tokens_used && ` • ${message.tokens_used} tokens`}
                                            </Typography>

                                            {message.isError && (
                                                <Tooltip title="Thử lại tin nhắn">
                                                    <IconButton
                                                        size="small"
                                                        onClick={() => handleRetryMessage(messages.indexOf(message))}
                                                        sx={{
                                                            color: beeColors.neutral.light,
                                                            "&:hover": {
                                                                color: beeColors.primary.main,
                                                                backgroundColor: "rgba(25, 118, 210, 0.04)",
                                                            },
                                                            width: 20,
                                                            height: 20,
                                                        }}
                                                    >
                                                        <RetryIcon sx={{ fontSize: 14 }} />
                                                    </IconButton>
                                                </Tooltip>
                                            )}
                                        </Box>
                                    </Box>
                                </Box>
                            ))}

                            {isTyping && (
                                <Box sx={{ display: "flex", alignItems: "flex-start", gap: 1 }}>
                                    <Avatar
                                        sx={{
                                            width: 32,
                                            height: 32,
                                            backgroundColor: beeColors.secondary.main,
                                        }}
                                    >
                                        <BotIcon />
                                    </Avatar>
                                    <TypingIndicator>
                                        <Box className="dot" />
                                        <Box className="dot" />
                                        <Box className="dot" />
                                        <Typography variant="body2" sx={{ ml: 1, color: beeColors.neutral.light }}>
                                            BeE Assistant đang trả lời...
                                        </Typography>
                                    </TypingIndicator>
                                </Box>
                            )}

                            <div ref={messagesEndRef} />
                        </MessagesArea>

                        <InputArea>
                            <TextField
                                fullWidth
                                multiline
                                maxRows={4}
                                placeholder="Nhập tin nhắn của bạn..."
                                value={inputMessage}
                                onChange={(e) => setInputMessage(e.target.value)}
                                onKeyDown={handleKeyPress}
                                disabled={isTyping}
                                sx={{
                                    "& .MuiOutlinedInput-root": {
                                        borderRadius: "20px",
                                        backgroundColor: beeColors.background.main,
                                    },
                                }}
                            />
                            <Tooltip title="Gửi tin nhắn">
                                <IconButton
                                    color="primary"
                                    onClick={handleSendMessage}
                                    disabled={!inputMessage.trim() || isTyping}
                                    sx={{
                                        backgroundColor: beeColors.primary.main,
                                        color: "white",
                                        "&:hover": {
                                            backgroundColor: beeColors.primary.dark,
                                        },
                                        "&.Mui-disabled": {
                                            backgroundColor: beeColors.neutral.light,
                                            color: "white",
                                        },
                                        mb: 1,
                                    }}
                                >
                                    <SendIcon />
                                </IconButton>
                            </Tooltip>
                        </InputArea>
                    </ChatContainer>
                </Grid>

                {/* Context Panel */}
                <Grid
                    size={{ xs: 12, md: 4 }}
                    sx={{ height: "100%", display: "flex", flexDirection: "column", gap: 2 }}
                >
                    {/* Chat Session Info Card - 50% height */}
                    <Card
                        sx={{
                            borderRadius: "16px",
                            height: "50%",
                            display: "flex",
                            flexDirection: "column",
                        }}
                    >
                        <CardContent sx={{ flex: 1, overflow: "auto" }}>
                            <Typography variant="h6" fontWeight="bold" sx={{ mb: 2 }}>
                                Thông Tin Phiên Chat
                            </Typography>
                            <Divider sx={{ mb: 3 }} />
                            <FormControl fullWidth size="small" sx={{ mb: 2 }}>
                                <InputLabel id="prompt-select-label">Chọn Assistant</InputLabel>
                                <Select
                                    labelId="prompt-select-label"
                                    value={selectedPrompt}
                                    label="Chọn Assistant"
                                    onChange={(e) => {
                                        setSelectedPrompt(e.target.value);
                                        // Clear chat when switching prompts
                                        setMessages([]);
                                        setCurrentSessionId(null);
                                        setContextDocuments([]);
                                    }}
                                    sx={{
                                        "& .MuiOutlinedInput-root": {
                                            borderColor: beeColors.neutral.light,
                                            "&:hover": {
                                                borderColor: beeColors.primary.main,
                                            },
                                            "&.Mui-focused": {
                                                borderColor: beeColors.primary.main,
                                            },
                                        },
                                        borderRadius: "12px",
                                    }}
                                >
                                    {Object.entries(availablePrompts).map(([key, label]) => (
                                        <MenuItem key={key} value={key}>
                                            {label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                            <Grid container spacing={2}>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                        Số tin nhắn: {messages.length}
                                    </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                        Sử dụng tài liệu: {useDocuments ? "Có" : "Không"}
                                    </Typography>
                                </Grid>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <Typography variant="body2" color="text.secondary">
                                        Session ID: {currentSessionId?.slice(0, 8)}...
                                    </Typography>
                                </Grid>
                            </Grid>
                            {/* {contextDocuments.length > 0 && (
                                <>
                                    <Divider sx={{ my: 2 }} />
                                    <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                                        Tài Liệu Tham Khảo
                                    </Typography>
                                    <Box sx={{ maxHeight: "150px", overflowY: "auto" }}>
                                        {contextDocuments.map((doc, index) => (
                                            <Card key={index} variant="outlined" sx={{ mb: 1, borderRadius: "8px" }}>
                                                <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
                                                    <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                                                        <DocumentIcon sx={{ mr: 1, fontSize: 16 }} />
                                                        <Typography variant="body2" fontWeight="bold">
                                                            {doc.title}
                                                        </Typography>
                                                    </Box>
                                                    <Chip
                                                        label={doc.file_type?.toUpperCase()}
                                                        size="small"
                                                        variant="outlined"
                                                    />
                                                </CardContent>
                                            </Card>
                                        ))}
                                    </Box>
                                </>
                            )} */}
                            {!useDocuments && (
                                <Alert severity="info" sx={{ mt: 2, borderRadius: "8px" }}>
                                    Tính năng sử dụng tài liệu đang tắt. Bật để AI có thể tham khảo tài liệu đã upload.
                                </Alert>
                            )}

                            {/* Session History - Only for Admin/Supervisor */}
                            {isAdminUser && (
                                <>
                                    <Divider sx={{ my: 2 }} />
                                    <Typography
                                        variant="subtitle1"
                                        sx={{ mb: 2, fontWeight: "bold", color: beeColors.neutral.main }}
                                    >
                                        Lịch sử Chat
                                    </Typography>
                                    {loadingSessions ? (
                                        <Box sx={{ display: "flex", justifyContent: "center", p: 2 }}>
                                            <CircularProgress size={24} />
                                        </Box>
                                    ) : sessions.length === 0 ? (
                                        <Alert severity="info" sx={{ borderRadius: "8px" }}>
                                            Chưa có session nào. Bắt đầu chat để tạo session đầu tiên!
                                        </Alert>
                                    ) : (
                                        <Box sx={{ maxHeight: "200px", overflowY: "auto" }}>
                                            {sessions.map((session) => (
                                                <Card
                                                    key={session.id}
                                                    sx={{
                                                        mb: 1,
                                                        cursor: "pointer",
                                                        transition: "all 0.2s ease",
                                                        border:
                                                            currentSessionId === session.id
                                                                ? `2px solid ${beeColors.primary.main}`
                                                                : "1px solid #e0e0e0",
                                                        backgroundColor:
                                                            currentSessionId === session.id
                                                                ? beeColors.primary.light + "10"
                                                                : "white",
                                                        "&:hover": {
                                                            boxShadow: 2,
                                                            borderColor: beeColors.primary.main,
                                                        },
                                                        borderRadius: "12px",
                                                    }}
                                                    onClick={() => loadSession(session.id)}
                                                >
                                                    <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
                                                        <Box
                                                            sx={{
                                                                display: "flex",
                                                                justifyContent: "space-between",
                                                                alignItems: "flex-start",
                                                            }}
                                                        >
                                                            <Box sx={{ flex: 1 }}>
                                                                <Typography
                                                                    variant="subtitle2"
                                                                    sx={{
                                                                        fontWeight: "bold",
                                                                        color: beeColors.neutral.main,
                                                                        mb: 0.5,
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                        whiteSpace: "nowrap",
                                                                    }}
                                                                >
                                                                    {session.title}
                                                                </Typography>
                                                                <Typography
                                                                    variant="caption"
                                                                    sx={{ color: beeColors.neutral.light }}
                                                                >
                                                                    {new Date(session.updated_at).toLocaleString(
                                                                        "vi-VN"
                                                                    )}
                                                                </Typography>
                                                                <Typography
                                                                    variant="caption"
                                                                    sx={{ color: beeColors.neutral.light, ml: 1 }}
                                                                >
                                                                    • {session.message_count || 0} tin nhắn
                                                                </Typography>
                                                            </Box>
                                                            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                                                {currentSessionId === session.id && (
                                                                    <Chip
                                                                        label="Đang dùng"
                                                                        size="small"
                                                                        sx={{
                                                                            backgroundColor: beeColors.primary.main,
                                                                            color: "white",
                                                                            fontSize: "0.7rem",
                                                                        }}
                                                                    />
                                                                )}
                                                                <IconButton
                                                                    onClick={(e) => deleteSession(session.id, e)}
                                                                    sx={{
                                                                        color: grey[500],
                                                                        "&:hover": {
                                                                            color: "#d32f2f",
                                                                            backgroundColor: "#ffebee",
                                                                        },
                                                                    }}
                                                                >
                                                                    <DeleteIcon />
                                                                </IconButton>
                                                            </Box>
                                                        </Box>
                                                    </CardContent>
                                                </Card>
                                            ))}
                                        </Box>
                                    )}
                                </>
                            )}
                        </CardContent>
                    </Card>

                    {/* Assistant Documents Card - 50% height */}
                    <Card
                        sx={{
                            borderRadius: "16px",
                            height: "50%",
                            display: "flex",
                            flexDirection: "column",
                        }}
                    >
                        <CardContent sx={{ flex: 1, overflow: "auto" }}>
                            <AssistantDocumentViewer
                                assistantType={(() => {
                                    const mapping = {
                                        bee_assistant_prompt: "bee_assistant",
                                        bee_ide_prompt: "bee_ide",
                                        python_prompt: "python",
                                        teacher_prompt: "teacher",
                                    };
                                    return mapping[selectedPrompt] || "bee_assistant";
                                })()}
                                assistantName={availablePrompts[selectedPrompt] || "BeE Assistant"}
                                showNotification={showNotification}
                            />
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>
        </Box>
    );
};

export default ChatPlayground;
