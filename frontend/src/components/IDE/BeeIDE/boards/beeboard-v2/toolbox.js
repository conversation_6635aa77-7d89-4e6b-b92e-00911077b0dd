export default {
    id: "beeboard-v2",
    kind: "category",
    name: "BeE-V2",
    colour: "#1a237e",
    contents: [
        // ========== LED ========== //
        {
            kind: "label",
            text: "LED",
        },
        {
            kind: "block",
            type: "beeboard_onboard_led_rgb",
            blockxml: `
                <block type="beeboard_onboard_led_rgb">
                    <value name="red">
                        <shadow type="math_number">
                            <field name="NUM">255</field>
                        </shadow>
                    </value>
                    <value name="green">
                        <shadow type="math_number">
                            <field name="NUM">255</field>
                        </shadow>
                    </value>
                    <value name="blue">
                        <shadow type="math_number">
                            <field name="NUM">255</field>
                        </shadow>
                    </value>
                    <value name="led_number">
                        <shadow type="math_number">
                            <field name="NUM">2</field>
                        </shadow>
                    </value>
                </block>`,
        },
        // {
        //     kind: "block",
        //     type: "beeboard_onboard_led_on_picker",
        //     blockxml: `
        //         <block type="beeboard_onboard_led_on_picker">
        //             <value name="led_color">
        //                 <shadow type="field_colour">
        //                     <field name="COLOUR">#ff0000</field>
        //                 </shadow>
        //             </value>
        //             <value name="led_number">
        //                 <shadow type="math_number">
        //                     <field name="NUM">2</field>
        //                 </shadow>
        //             </value>
        //         </block>
        //     `
        // },
        {
            kind: "block",
            type: "beeboard_onboard_led_on",
            blockxml: `
                <block type="beeboard_onboard_led_on">
                    <value name="led_number">
                        <shadow type="math_number">
                            <field name="NUM">2</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_onboard_led_off",
            blockxml: `
                <block type="beeboard_onboard_led_off">
                    <value name="led_number">
                        <shadow type="math_number">
                            <field name="NUM">2</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        // ========== Display ========== //
        {
            kind: "label",
            text: "Display",
        },
        {
            kind: "block",
            type: "beeboard_clear_oled",
        },
        {
            kind: "block",
            type: "beeboard_display_oled",
            blockxml: `
                <block type="beeboard_display_oled">
                    <value name="message">
                        <shadow type="text">
                            <field name="TEXT">Hello</field>
                        </shadow>
                    </value>
                    <value name="x_pos">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="y_pos">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        // {
        //     kind: "block",
        //     type: "beeboard_oled_draw_pixel",
        //     blockxml: `
        //         <block type="beeboard_oled_draw_pixel">
        //             <value name="x">
        //                 <shadow type="math_number">
        //                     <field name="NUM">0</field>
        //                 </shadow>
        //             </value>
        //             <value name="y">
        //                 <shadow type="math_number">
        //                     <field name="NUM">0</field>
        //                 </shadow>
        //             </value>
        //         </block>
        //     `,
        // },
        {
            kind: "block",
            type: "beeboard_oled_draw_line",
            blockxml: `
                <block type="beeboard_oled_draw_line">
                    <value name="x1">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="y1">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="x2">
                        <shadow type="math_number">
                            <field name="NUM">10</field>
                        </shadow>
                    </value>
                    <value name="y2">
                        <shadow type="math_number">
                            <field name="NUM">10</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_oled_draw_rect",
            blockxml: `
                <block type="beeboard_oled_draw_rect">
                    <value name="x">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="y">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="width">
                        <shadow type="math_number">
                            <field name="NUM">10</field>
                        </shadow>
                    </value>
                    <value name="height">
                        <shadow type="math_number">
                            <field name="NUM">10</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_oled_draw_circle",
            blockxml: `
                <block type="beeboard_oled_draw_circle">
                    <value name="x">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="y">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="radius">
                        <shadow type="math_number">
                            <field name="NUM">10</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_oled_draw_icon",
            blockxml: `
                <block type="beeboard_oled_draw_icon">
                    <value name="image">
                        <shadow type="text">
                            <field name="TEXT">icon</field>
                        </shadow>
                    </value>
                    <value name="x">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="y">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_oled_draw_image",
            blockxml: `
                <block type="beeboard_oled_draw_image">
                    <value name="image">
                        <shadow type="text">
                            <field name="TEXT">image</field>
                        </shadow>
                    </value>
                    <value name="x">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="y">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_oled_load_pbm",
            blockxml: `
                <block type="beeboard_oled_load_pbm">
                    <value name="image">
                        <shadow type="text">
                            <field name="TEXT">image</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        // {
        //     kind: "block",
        //     type: "beeboard_render_oled",
        // },
        // ========== Button ========== //
        {
            kind: "label",
            text: "Button",
        },
        {
            kind: "block",
            type: "beeboard_onboard_button_on_press_start_program",
        },
        {
            kind: "block",
            type: "beeboard_onboard_button_status",
        },
        // ========== Buzzer ========== //
        {
            kind: "label",
            text: "Buzzer",
        },
        {
            kind: "block",
            type: "beeboard_buzzer_notes",
            blockxml: `
                <block type="beeboard_buzzer_notes">
                    <value name="notes">
                        <block type="beeboard_make_note">
                            <field name="notes">C5</field>
                        </block>
                    </value>
                    <field name="duration">1 / 4</field>
                </block>`,
        },
        {
            kind: "block",
            type: "beeboard_buzzer_note",
            blockxml: `
                <block type="beeboard_buzzer_note">
                    <value name="note">
                        <shadow type="math_number">
                            <field name="NUM">22</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_buzzer_volume",
            blockxml: `
                <block type="beeboard_buzzer_volume">
                    <value name="volume">
                        <shadow type="math_number">
                            <field name="NUM">80</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_be_quiet",
        },
        // ========== GPIO Output ========== //
        {
            kind: "label",
            text: "Write Signal",
        },
        {
            kind: "block",
            type: "beeboard_gpio_output",
            blockxml:
                '<block type="beeboard_gpio_output"><field name="signal">HIGH</field><field name="port">PORT1</field></block>',
        },
        {
            kind: "block",
            type: "beeboard_pwm_frequency",
            blockxml: `
                <block type="beeboard_pwm_frequency">
                    <value name="duty">
                        <shadow type="math_number">
                            <field name="NUM">50</field>
                        </shadow>
                    </value>
                    <field name="port">PORT1</field>
                </block>
            `,
        },
        // ========== DigitalRead ========== //
        {
            kind: "label",
            text: "Read Signal",
        },
        {
            kind: "block",
            type: "beeboard_gpio_input",
            blockxml: '<block type="beeboard_gpio_input"><field name="port">PORT1</field></block>',
        },
        {
            kind: "block",
            type: "beeboard_analog_input",
            blockxml: '<block type="beeboard_analog_input"><field name="port">PORT1</field></block>',
        },
        // ========== Gyroscope ========== //
        {
            kind: "label",
            text: "Gyroscope",
        },
        {
            kind: "block",
            type: "beeboard_gyro_calibrate",
        },
        {
            kind: "block",
            type: "beeboard_gyro_refresh",
        },
        {
            kind: "block",
            type: "beeboard_gyro_skaking",
        },
        {
            kind: "block",
            type: "beeboard_gyro_rpy",
        },
        // {
        //     kind: "block",
        //     type: "beeboard_gyro_roll",
        // },
        // {
        //     kind: "block",
        //     type: "beeboard_gyro_pitch",
        // },
        // ========== Motor & Servo ========== //
        {
            kind: "label",
            text: "Motor & Servo",
        },
        {
            kind: "block",
            type: "beeboard_run_dc_motor_onboard",
            blockxml: `
                <block type="beeboard_run_dc_motor_onboard">
                    <value name="power">
                        <shadow type="math_number">
                            <field name="NUM">50</field>
                        </shadow>
                    </value>
                    <value name="motor">
                        <shadow type="math_number">
                            <field name="NUM">2</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_stop_dc_motor",
            blockxml: `
                <block type="beeboard_stop_dc_motor">
                    <value name="motor">
                        <shadow type="math_number">
                            <field name="NUM">2</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_run_servo_onboard",
            blockxml: `
                <block type="beeboard_run_servo_onboard">
                    <value name="angle">
                        <shadow type="math_number">
                            <field name="NUM">0</field>
                        </shadow>
                    </value>
                    <value name="motor">
                        <shadow type="math_number">
                            <field name="NUM">8</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        // ========== Movement ========== //
        {
            kind: "label",
            text: "Movement",
        },
        {
            kind: "block",
            type: "beeboard_move_robot_forward",
            blockxml: `
                <block type="beeboard_move_robot_forward">
                    <value name="power">
                        <shadow type="math_number">
                            <field name="NUM">50</field>
                        </shadow>
                    </value>
                    <value name="sec">
                        <shadow type="math_number">
                            <field name="NUM">1</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_move_robot_backward",
            blockxml: `
                <block type="beeboard_move_robot_backward">
                    <value name="power">
                        <shadow type="math_number">
                            <field name="NUM">50</field>
                        </shadow>
                    </value>
                    <value name="sec">
                        <shadow type="math_number">
                            <field name="NUM">1</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_turn_robot_left",
            blockxml: `
                <block type="beeboard_turn_robot_left">
                    <value name="power">
                        <shadow type="math_number">
                            <field name="NUM">50</field>
                        </shadow>
                    </value>
                    <value name="sec">
                        <shadow type="math_number">
                            <field name="NUM">1</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_turn_robot_right",
            blockxml: `
                <block type="beeboard_turn_robot_right">
                    <value name="power">
                        <shadow type="math_number">
                            <field name="NUM">50</field>
                        </shadow>
                    </value>
                    <value name="sec">
                        <shadow type="math_number">
                            <field name="NUM">1</field>
                        </shadow>
                    </value>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_stop_robot",
        },
        // ========== Setup ========== //
        {
            kind: "label",
            text: "Advanced",
        },
        {
            kind: "block",
            type: "beeboard_reset",
        },
        {
            kind: "block",
            type: "beeboard_setup_ota",
            blockxml: `
                <block type="beeboard_setup_ota">
                    <field name="wifi_ssid">my_wifi</field>
                    <field name="wifi_pass">my_pass</field>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_ota_ip",
            blockxml: `
                <block type="beeboard_ota_ip">
                    <field name="ip_address">***********</field>
                </block>
            `,
        },
        {
            kind: "block",
            type: "beeboard_setup_serial",
        },
        {
            kind: "block",
            type: "beeboard_setup_bluetooth",
        },
        // Add more blocks here as needed
    ],
};
