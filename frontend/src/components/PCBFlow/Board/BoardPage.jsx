import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
    Box,
    Typography,
    Button,
    TextField,
    Card,
    CardContent,
    CardMedia,
    IconButton,
    Tooltip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    LinearProgress,
    Chip,
    Avatar,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import {
    Add as AddIcon,
    Memory as BoardIcon,
    Search as SearchIcon,
    Refresh as RefreshIcon,
    CloudUpload as UploadIcon,
    Visibility as ViewIcon,
    Delete as DeleteIcon,
} from "@mui/icons-material";
import { beeColors } from "../../Common/CustomButton";
import CustomButton from "../../Common/CustomButton";
import usePCBFlowStore from "../store/pcbFlowStore";

const BoardPage = () => {
    const navigate = useNavigate();
    const { boards, loading, filters, fetchBoards, setFilter, uploadBoard, deleteBoard } = usePCBFlowStore();

    const [uploadDialog, setUploadDialog] = useState(false);
    const [uploadData, setUploadData] = useState({
        name: "",
        description: "",
        version: "",
        file: null,
    });
    const [uploading, setUploading] = useState(false);
    const [deleteDialog, setDeleteDialog] = useState({ open: false, board: null });
    const [deleting, setDeleting] = useState(false);

    useEffect(() => {
        fetchBoards();
    }, [fetchBoards, filters.boards]);

    const handleSearch = (event) => {
        setFilter("boards", "search", event.target.value);
    };

    const handleFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            setUploadData((prev) => ({
                ...prev,
                file,
                name: prev.name || file.name.replace(".zip", ""),
            }));
        }
    };

    const handleUpload = async (event) => {
        event.preventDefault();
        if (!uploadData.file) return;

        setUploading(true);
        try {
            const formData = new FormData();
            formData.append("file", uploadData.file);
            formData.append("name", uploadData.name);
            formData.append("description", uploadData.description);
            formData.append("version", uploadData.version);

            await uploadBoard(formData);
            setUploadDialog(false);
            setUploadData({ name: "", description: "", version: "", file: null });
        } catch (error) {
            console.error("Error uploading board:", error);
        } finally {
            setUploading(false);
        }
    };

    const handleViewBoard = (boardId) => {
        navigate(`/pcbflow/boards/${boardId}`);
    };

    const handleDeleteBoard = async () => {
        if (!deleteDialog.board) return;

        setDeleting(true);
        try {
            await deleteBoard(deleteDialog.board.id);
            setDeleteDialog({ open: false, board: null });
        } catch (error) {
            console.error("Error deleting board:", error);
        } finally {
            setDeleting(false);
        }
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{ display: "flex", justifyContent: "space-between", alignItems: "center", mb: 4 }}>
                <Box>
                    <Typography variant="h4" fontWeight="bold" color="text.primary">
                        Board Management
                    </Typography>
                    <Typography variant="body1" color="text.secondary" sx={{ mt: 1 }}>
                        Manage your PCB board designs and files
                    </Typography>
                </Box>
                <Box sx={{ display: "flex", gap: 2 }}>
                    <Tooltip title="Refresh">
                        <IconButton onClick={fetchBoards} disabled={loading.boards} size="large">
                            <RefreshIcon />
                        </IconButton>
                    </Tooltip>
                    <CustomButton
                        variant="contained"
                        color="success"
                        startIcon={<UploadIcon />}
                        onClick={() => setUploadDialog(true)}
                    >
                        Upload Board
                    </CustomButton>
                </Box>
            </Box>

            {/* Search */}
            <Card sx={{ mb: 3 }}>
                <CardContent>
                    <TextField
                        fullWidth
                        placeholder="Search boards..."
                        value={filters.boards.search}
                        onChange={handleSearch}
                        InputProps={{
                            startAdornment: <SearchIcon sx={{ color: "text.secondary", mr: 1 }} />,
                        }}
                        sx={{
                            "& .MuiOutlinedInput-root": {
                                borderRadius: 2,
                            },
                        }}
                    />
                </CardContent>
            </Card>

            {/* Loading */}
            {loading.boards && <LinearProgress sx={{ mb: 2 }} />}

            {/* Boards Grid */}
            <Grid container spacing={3}>
                {boards.map((board) => (
                    <Grid size={{ xs: 12, md: 6, lg: 4, xl: 3 }} key={board.id}>
                        <Card
                            sx={{
                                height: "100%",
                                display: "flex",
                                flexDirection: "column",
                                transition: "all 0.3s ease",
                                "&:hover": {
                                    transform: "translateY(-4px)",
                                    boxShadow: `0 8px 25px ${beeColors.primary.main}20`,
                                },
                            }}
                        >
                            {/* Board Image */}
                            <CardMedia
                                sx={{
                                    height: 200,
                                    backgroundColor: beeColors.background.main,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                }}
                            >
                                {board.avatar ? (
                                    <img
                                        src={
                                            board.avatar.includes("http")
                                                ? board.avatar
                                                : `${import.meta.env.VITE_API_URL}${board.avatar}`
                                        }
                                        alt={board.name}
                                        style={{
                                            width: "100%",
                                            height: "100%",
                                            objectFit: "contain",
                                        }}
                                    />
                                ) : (
                                    <Avatar
                                        sx={{
                                            width: 80,
                                            height: 80,
                                            backgroundColor: beeColors.success.main,
                                        }}
                                    >
                                        <BoardIcon sx={{ fontSize: 40 }} />
                                    </Avatar>
                                )}
                            </CardMedia>

                            <CardContent sx={{ flexGrow: 1, display: "flex", flexDirection: "column" }}>
                                {/* Board Info */}
                                <Typography variant="h6" fontWeight="bold" sx={{ mb: 1 }}>
                                    {board.name} - v{board.version}
                                </Typography>

                                <Typography variant="body2" color="text.secondary" sx={{ mb: 2, flexGrow: 1 }}>
                                    {board.description || "No description available"}
                                </Typography>

                                {/* Stats */}
                                <Box sx={{ display: "flex", gap: 1, mb: 2 }}>
                                    <Chip
                                        label={`${board.total_components || 0} Components`}
                                        size="small"
                                        color="primary"
                                        variant="outlined"
                                    />
                                    <Chip
                                        label={`${board.total_parts_per_board || 0} Parts`}
                                        size="small"
                                        color="secondary"
                                        variant="outlined"
                                    />
                                </Box>

                                {/* Upload Info */}
                                <Box sx={{ mb: 2 }}>
                                    <Typography variant="caption" color="text.secondary">
                                        Uploaded by: {board.uploaded_by?.username || "Unknown"}
                                    </Typography>
                                    <br />
                                    <Typography variant="caption" color="text.secondary">
                                        {new Date(board.created_at).toLocaleDateString()}
                                    </Typography>
                                </Box>

                                {/* Actions */}
                                <Box sx={{ display: "flex", gap: 1, justifyContent: "space-between" }}>
                                    <CustomButton
                                        variant="contained"
                                        color="primary"
                                        size="small"
                                        startIcon={<ViewIcon />}
                                        onClick={() => handleViewBoard(board.id)}
                                        sx={{ flexGrow: 1, height: 40 }}
                                    >
                                        View Details
                                    </CustomButton>
                                    <Tooltip title="Delete">
                                        <IconButton onClick={() => setDeleteDialog({ open: true, board })}>
                                            <DeleteIcon />
                                        </IconButton>
                                    </Tooltip>
                                </Box>
                            </CardContent>
                        </Card>
                    </Grid>
                ))}
            </Grid>

            {/* Empty State */}
            {boards.length === 0 && !loading.boards && (
                <Box sx={{ py: 8, textAlign: "center" }}>
                    <BoardIcon sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" sx={{ mb: 1 }}>
                        No boards found
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                        Upload your first PCB board to get started
                    </Typography>
                    <CustomButton
                        variant="contained"
                        color="success"
                        startIcon={<UploadIcon />}
                        onClick={() => setUploadDialog(true)}
                    >
                        Upload Board
                    </CustomButton>
                </Box>
            )}

            {/* Upload Dialog */}
            <Dialog open={uploadDialog} onClose={() => setUploadDialog(false)} maxWidth="sm" fullWidth>
                <form onSubmit={handleUpload}>
                    <DialogTitle>
                        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                            <UploadIcon sx={{ color: beeColors.success.main }} />
                            <Typography variant="h6" fontWeight="bold">
                                Upload PCB Board
                            </Typography>
                        </Box>
                    </DialogTitle>

                    <DialogContent>
                        <Grid container spacing={3} sx={{ mt: 1 }}>
                            <Grid size={{ xs: 8 }}>
                                <TextField
                                    fullWidth
                                    label="Board Name"
                                    value={uploadData.name}
                                    onChange={(e) => setUploadData((prev) => ({ ...prev, name: e.target.value }))}
                                    required
                                />
                            </Grid>

                            <Grid size={{ xs: 4 }}>
                                <TextField
                                    fullWidth
                                    label="Version"
                                    value={uploadData.version}
                                    onChange={(e) => setUploadData((prev) => ({ ...prev, version: e.target.value }))}
                                />
                            </Grid>

                            <Grid size={{ xs: 12 }}>
                                <TextField
                                    fullWidth
                                    label="Description"
                                    value={uploadData.description}
                                    onChange={(e) =>
                                        setUploadData((prev) => ({ ...prev, description: e.target.value }))
                                    }
                                    multiline
                                    rows={3}
                                />
                            </Grid>

                            <Grid size={{ xs: 12 }}>
                                <Button
                                    variant="outlined"
                                    component="label"
                                    fullWidth
                                    sx={{
                                        height: 100,
                                        borderStyle: "dashed",
                                        borderWidth: 2,
                                        borderColor: beeColors.success.main,
                                        color: beeColors.success.main,
                                        "&:hover": {
                                            backgroundColor: `${beeColors.success.main}10`,
                                        },
                                    }}
                                >
                                    <Box sx={{ textAlign: "center" }}>
                                        <UploadIcon sx={{ fontSize: 32, mb: 1 }} />
                                        <Typography variant="body2">
                                            {uploadData.file ? uploadData.file.name : "Click to select ZIP file"}
                                        </Typography>
                                        <Typography variant="caption" color="text.secondary">
                                            Should contain: *.png, bom.csv, ibom.html, gerber files
                                        </Typography>
                                    </Box>
                                    <input type="file" accept=".zip" onChange={handleFileChange} hidden />
                                </Button>
                            </Grid>
                        </Grid>
                    </DialogContent>

                    <DialogActions sx={{ p: 3 }}>
                        <Button onClick={() => setUploadDialog(false)} disabled={uploading}>
                            Cancel
                        </Button>
                        <CustomButton
                            type="submit"
                            variant="contained"
                            color="success"
                            disabled={uploading || !uploadData.file || !uploadData.name}
                            startIcon={<UploadIcon />}
                        >
                            {uploading ? "Uploading..." : "Upload Board"}
                        </CustomButton>
                    </DialogActions>
                </form>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog open={deleteDialog.open} onClose={() => setDeleteDialog({ open: false, board: null })}>
                <DialogTitle>
                    <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                        <DeleteIcon sx={{ color: beeColors.error.main }} />
                        <Typography variant="h6" fontWeight="bold">
                            Delete Board
                        </Typography>
                    </Box>
                </DialogTitle>

                <DialogContent>
                    <Typography variant="body1" sx={{ mb: 2 }}>
                        Are you sure you want to delete the board "{deleteDialog.board?.name}"?
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        This action will permanently delete:
                    </Typography>
                    <Box component="ul" sx={{ mt: 1, pl: 2 }}>
                        <Typography component="li" variant="body2" color="text.secondary">
                            Board information and description
                        </Typography>
                        <Typography component="li" variant="body2" color="text.secondary">
                            All uploaded files (PNG, BOM, iBOM, Gerber)
                        </Typography>
                        <Typography component="li" variant="body2" color="text.secondary">
                            BOM component mappings
                        </Typography>
                    </Box>
                    <Typography variant="body2" color="error" sx={{ mt: 2, fontWeight: "bold" }}>
                        This action cannot be undone.
                    </Typography>
                </DialogContent>

                <DialogActions sx={{ p: 3 }}>
                    <Button onClick={() => setDeleteDialog({ open: false, board: null })} disabled={deleting}>
                        Cancel
                    </Button>
                    <Button
                        onClick={handleDeleteBoard}
                        color="error"
                        variant="contained"
                        disabled={deleting}
                        startIcon={<DeleteIcon />}
                    >
                        {deleting ? "Deleting..." : "Delete Board"}
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default BoardPage;
