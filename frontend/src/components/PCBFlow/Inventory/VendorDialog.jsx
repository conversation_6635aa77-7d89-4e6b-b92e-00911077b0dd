import React, { useState, useEffect } from "react";
import {
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Button,
    Typography,
    Box,
    Alert,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    IconButton,
    Chip,
    Tooltip,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Autocomplete,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import {
    Store as VendorIcon,
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Star as StarIcon,
    StarBorder as StarBorderIcon,
    Launch as LaunchIcon,
    ToggleOn as ToggleOnIcon,
    ToggleOff as ToggleOffIcon,
} from "@mui/icons-material";
import CustomButton from "../../Common/CustomButton";
import { beeColors } from "../../Common/CustomButton";
import axiosInstance from "../../../services/axiosInstance";
import usePCBFlowStore from "../store/pcbFlowStore";
import { grey } from "@mui/material/colors";

const VendorDialog = ({ open, component, onClose }) => {
    const { vendors: allVendors, fetchVendors, toggleComponentVendorActive } = usePCBFlowStore();
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState("");
    const [componentVendors, setComponentVendors] = useState([]);
    const [showAddForm, setShowAddForm] = useState(false);
    const [editingVendor, setEditingVendor] = useState(null);

    // Filter only active vendors for selection
    const activeVendors = allVendors.filter((vendor) => vendor.active);
    const [formData, setFormData] = useState({
        component_id: component?.id || "",
        component_part_number: component?.part_number || "",
        vendor_id: "",
        price: "",
        currency: "VND",
        link: "",
        moq: 1,
        is_preferred: false,
    });

    useEffect(() => {
        if (open && component) {
            fetchComponentVendors();
            fetchVendors(); // Load all vendors for selection
            setShowAddForm(false);
            setEditingVendor(null);
        }
    }, [open, component, fetchVendors]);

    // Reset form when componentVendors changes (after fetching)
    useEffect(() => {
        if (open && !showAddForm) {
            resetForm();
        }
    }, [componentVendors, open, showAddForm]);

    const resetForm = () => {
        // Set is_preferred to true if this is the first vendor for the component
        const isFirstVendor = componentVendors.length === 0;

        setFormData({
            vendor_id: "",
            price: "",
            currency: "VND",
            link: "",
            moq: 1,
            is_preferred: isFirstVendor,
        });
        setError("");
    };

    const fetchComponentVendors = async () => {
        if (!component) return;

        setLoading(true);
        try {
            const response = await axiosInstance.get(`/api/pcbflow/components/${component.id}/vendors/`);
            setComponentVendors(response.data);
        } catch (error) {
            console.error("Error fetching vendors:", error);
            setError("Failed to load vendor information");
        } finally {
            setLoading(false);
        }
    };

    const handleAddVendor = async (event) => {
        event.preventDefault();
        setLoading(true);
        setError("");

        try {
            // Find selected vendor
            const selectedVendor = activeVendors.find((v) => v.id === parseInt(formData.vendor_id));
            if (!selectedVendor) {
                setError("Please select a vendor");
                return;
            }

            const data = {
                component_part_number: component.part_number,
                vendor_name: selectedVendor.name,
                price: parseFloat(formData.price),
                currency: formData.currency,
                link: formData.link,
                moq: parseInt(formData.moq),
                is_preferred: formData.is_preferred,
            };

            await axiosInstance.post(`/api/pcbflow/components/${component.id}/add_vendor/`, data);
            await fetchComponentVendors();
            setShowAddForm(false);
            resetForm();
        } catch (error) {
            console.error("Error adding vendor:", error);
            setError(error.response?.data?.detail || "Failed to add vendor");
        } finally {
            setLoading(false);
        }
    };

    const handleChange = (field) => (event) => {
        setFormData((prev) => ({
            ...prev,
            [field]: event.target.value,
        }));
    };

    const handleSetPreferred = async (vendorId) => {
        setLoading(true);
        setError("");
        try {
            await axiosInstance.post(`/api/pcbflow/component-vendors/${vendorId}/set_preferred/`);
            await fetchComponentVendors(); // Refresh the vendor list
        } catch (error) {
            console.error("Error setting preferred vendor:", error);
            setError(error.response?.data?.detail || "Failed to set preferred vendor");
        } finally {
            setLoading(false);
        }
    };

    const handleEditVendor = (vendor) => {
        setEditingVendor(vendor);
        setFormData({
            vendor_id: vendor.vendor.id,
            price: vendor.price,
            currency: vendor.currency,
            link: vendor.link || "",
            moq: vendor.moq,
            is_preferred: vendor.is_preferred,
        });
        setShowAddForm(true);
    };

    const handleUpdateVendor = async (event) => {
        event.preventDefault();
        setLoading(true);
        setError("");

        try {
            const data = {
                price: parseFloat(formData.price),
                currency: formData.currency,
                link: formData.link,
                moq: parseInt(formData.moq),
                is_preferred: formData.is_preferred,
            };

            await axiosInstance.patch(`/api/pcbflow/component-vendors/${editingVendor.id}/`, data);
            await fetchComponentVendors();
            setShowAddForm(false);
            setEditingVendor(null);
            resetForm();
        } catch (error) {
            console.error("Error updating vendor:", error);
            setError(error.response?.data?.detail || "Failed to update vendor");
        } finally {
            setLoading(false);
        }
    };

    const handleToggleActive = async (componentVendor) => {
        if (!window.confirm("Are you sure you want to toggle status of vendor " + componentVendor.vendor.name + "?"))
            return;
        setLoading(true);
        setError("");
        try {
            await toggleComponentVendorActive(componentVendor.id);
            await fetchComponentVendors(); // Refresh the vendor list
        } catch (error) {
            console.error("Error toggling component vendor active status:", error);
            setError(error.response?.data?.detail || "Failed to toggle active status");
        } finally {
            setLoading(false);
        }
    };

    const formatPrice = (price, currency) => {
        return new Intl.NumberFormat("vi-VN", {
            style: "currency",
            currency: currency,
        }).format(price);
    };

    if (!component) return null;

    return (
        <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
            <DialogTitle>
                <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                    <VendorIcon sx={{ color: beeColors.secondary.main }} />
                    <Box>
                        <Typography variant="h6" fontWeight="bold">
                            Vendor Management
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            {component.part_number} - {component.name}
                        </Typography>
                    </Box>
                </Box>
            </DialogTitle>

            <DialogContent>
                {error && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                        {error}
                    </Alert>
                )}

                {/* Add Vendor Button */}
                {!showAddForm && (
                    <Box sx={{ mb: 3 }}>
                        <CustomButton
                            variant="contained"
                            color="secondary"
                            startIcon={<AddIcon />}
                            onClick={() => setShowAddForm(true)}
                        >
                            Add Vendor
                        </CustomButton>
                    </Box>
                )}

                {/* Add/Edit Vendor Form */}
                {showAddForm && (
                    <Paper sx={{ p: 3, mb: 3, backgroundColor: beeColors.background.main }}>
                        <Typography variant="h6" sx={{ mb: 2 }}>
                            {editingVendor ? "Edit Vendor Information" : "Add Vendor to Component"}
                        </Typography>
                        <form onSubmit={editingVendor ? handleUpdateVendor : handleAddVendor}>
                            <Grid container spacing={2}>
                                <Grid size={{ xs: 12, md: 6 }}>
                                    <Autocomplete
                                        autoHighlight
                                        fullWidth
                                        disablePortal
                                        disabled={editingVendor !== null}
                                        id="vendor-combobox"
                                        options={activeVendors}
                                        getOptionLabel={(option) => option.name}
                                        value={activeVendors.find((v) => v.id === formData.vendor_id) || null}
                                        onChange={(_, value) => {
                                            setFormData((prev) => ({
                                                ...prev,
                                                vendor_id: value ? value.id : "",
                                            }));
                                        }}
                                        renderInput={(params) => (
                                            <TextField
                                                {...params}
                                                label="Select Vendor"
                                                required={!editingVendor}
                                                helperText={
                                                    editingVendor ? "Vendor cannot be changed when editing" : ""
                                                }
                                            />
                                        )}
                                        renderOption={(props, option) => (
                                            <Box component="li" {...props} key={option.id}>
                                                <Box>
                                                    <Typography variant="body1">{option.name}</Typography>
                                                    {option.website && (
                                                        <Typography variant="caption" color="text.secondary">
                                                            {option.website}
                                                        </Typography>
                                                    )}
                                                </Box>
                                            </Box>
                                        )}
                                    />
                                </Grid>
                                <Grid size={{ xs: 12, md: 6 }}>
                                    <TextField
                                        fullWidth
                                        label="Product Link"
                                        value={formData.link}
                                        onChange={handleChange("link")}
                                        type="url"
                                    />
                                </Grid>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <TextField
                                        fullWidth
                                        label={"Price: " + formatPrice(formData.price, formData.currency)}
                                        value={formData.price}
                                        onChange={handleChange("price")}
                                        type="number"
                                        step="0.01"
                                        required
                                        slotProps={{
                                            htmlInput: { min: 0 },
                                        }}
                                    />
                                </Grid>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <FormControl fullWidth>
                                        <InputLabel>Currency</InputLabel>
                                        <Select
                                            value={formData.currency}
                                            onChange={handleChange("currency")}
                                            label="Currency"
                                        >
                                            <MenuItem value="VND">VND</MenuItem>
                                            <MenuItem value="USD">USD</MenuItem>
                                            <MenuItem value="EUR">EUR</MenuItem>
                                            <MenuItem value="CNY">CNY</MenuItem>
                                        </Select>
                                    </FormControl>
                                </Grid>
                                <Grid size={{ xs: 12, md: 4 }}>
                                    <TextField
                                        fullWidth
                                        label="Minimum Order Quantity"
                                        value={formData.moq}
                                        onChange={handleChange("moq")}
                                        type="number"
                                        step="1"
                                        required
                                        inputProps={{ min: 1 }}
                                    />
                                </Grid>
                                <Grid size={{ xs: 12 }}>
                                    <Box sx={{ display: "flex", gap: 2 }}>
                                        <Button
                                            onClick={() => {
                                                setShowAddForm(false);
                                                setEditingVendor(null);
                                                resetForm();
                                            }}
                                        >
                                            Cancel
                                        </Button>
                                        <CustomButton
                                            type="submit"
                                            variant="contained"
                                            color="secondary"
                                            disabled={loading}
                                        >
                                            {loading
                                                ? editingVendor
                                                    ? "Updating..."
                                                    : "Adding..."
                                                : editingVendor
                                                  ? "Update Vendor"
                                                  : "Add Vendor"}
                                        </CustomButton>
                                    </Box>
                                </Grid>
                            </Grid>
                        </form>
                    </Paper>
                )}

                {/* Vendors Table */}
                <TableContainer component={Paper}>
                    <Table>
                        <TableHead>
                            <TableRow sx={{ backgroundColor: beeColors.background.main }}>
                                <TableCell>Vendor</TableCell>
                                <TableCell align="right">Price</TableCell>
                                <TableCell align="center">Link</TableCell>
                                <TableCell align="center">Status</TableCell>
                                <TableCell align="center">Preferred</TableCell>
                                <TableCell align="center">Actions</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {componentVendors.map((vendor, index) => (
                                <TableRow key={index}>
                                    <TableCell>
                                        <Box>
                                            <Typography
                                                variant="subtitle2"
                                                fontWeight="bold"
                                                color={vendor.active ? "text.primary" : grey[500]}
                                            >
                                                {vendor.active ? vendor.vendor.name : <s>{vendor.vendor.name}</s>}
                                            </Typography>
                                            {vendor.vendor.website && (
                                                <Typography variant="caption" color="text.secondary">
                                                    {vendor.vendor.website}
                                                </Typography>
                                            )}
                                        </Box>
                                    </TableCell>
                                    <TableCell align="right">
                                        <Typography
                                            variant="h6"
                                            fontWeight="bold"
                                            color={vendor.active ? beeColors.success.main : grey[500]}
                                        >
                                            {vendor.active ? (
                                                formatPrice(vendor.price, vendor.currency)
                                            ) : (
                                                <s>{formatPrice(vendor.price, vendor.currency)}</s>
                                            )}
                                        </Typography>
                                    </TableCell>
                                    <TableCell align="center">
                                        {vendor.link ? (
                                            <Tooltip title="Open product page">
                                                <IconButton
                                                    size="small"
                                                    onClick={() => window.open(vendor.link, "_blank")}
                                                    sx={{ color: beeColors.primary.main }}
                                                >
                                                    <LaunchIcon />
                                                </IconButton>
                                            </Tooltip>
                                        ) : (
                                            <Typography variant="body2" color="text.secondary">
                                                No link
                                            </Typography>
                                        )}
                                    </TableCell>
                                    <TableCell align="center">
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "center",
                                                justifyContent: "center",
                                                gap: 1,
                                            }}
                                        >
                                            {/* <Chip
                                                label={vendor.active ? "Active" : "Inactive"}
                                                size="small"
                                                color={vendor.active ? "success" : "default"}
                                                variant={vendor.active ? "filled" : "outlined"}
                                            /> */}
                                            <Tooltip title={vendor.active ? "Deactivate" : "Activate"}>
                                                <IconButton
                                                    size="small"
                                                    onClick={() => handleToggleActive(vendor)}
                                                    disabled={loading}
                                                    sx={{
                                                        color: vendor.active
                                                            ? beeColors.success.main
                                                            : beeColors.neutral.main,
                                                    }}
                                                >
                                                    {vendor.active ? (
                                                        <ToggleOnIcon sx={{ fontSize: 40 }} />
                                                    ) : (
                                                        <ToggleOffIcon sx={{ fontSize: 40 }} />
                                                    )}
                                                </IconButton>
                                            </Tooltip>
                                        </Box>
                                    </TableCell>
                                    <TableCell align="center">
                                        {vendor.is_preferred ? (
                                            <Chip icon={<StarIcon />} label="Preferred" color="warning" size="small" />
                                        ) : (
                                            <Tooltip title="Set as preferred vendor">
                                                <IconButton
                                                    size="small"
                                                    onClick={() => handleSetPreferred(vendor.id)}
                                                    disabled={loading || !vendor.active}
                                                    sx={{ color: "text.secondary" }}
                                                >
                                                    <StarBorderIcon />
                                                </IconButton>
                                            </Tooltip>
                                        )}
                                    </TableCell>
                                    <TableCell align="center">
                                        <Box sx={{ display: "flex", gap: 1, justifyContent: "center" }}>
                                            <Tooltip title="Edit">
                                                <IconButton
                                                    size="small"
                                                    sx={{ color: beeColors.accent.main }}
                                                    onClick={() => handleEditVendor(vendor)}
                                                    disabled={loading}
                                                >
                                                    <EditIcon />
                                                </IconButton>
                                            </Tooltip>
                                            <Tooltip title="Delete">
                                                <IconButton
                                                    size="small"
                                                    sx={{ color: beeColors.error.main }}
                                                    disabled={loading}
                                                >
                                                    <DeleteIcon />
                                                </IconButton>
                                            </Tooltip>
                                        </Box>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>

                {componentVendors.length === 0 && !loading && (
                    <Box sx={{ py: 4, textAlign: "center" }}>
                        <VendorIcon sx={{ fontSize: 48, color: "text.secondary", mb: 2 }} />
                        <Typography variant="body1" color="text.secondary">
                            No vendors found for this component
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                            Add vendors to compare prices and manage suppliers
                        </Typography>
                    </Box>
                )}
            </DialogContent>

            <DialogActions sx={{ p: 3 }}>
                <Button onClick={onClose}>Close</Button>
            </DialogActions>
        </Dialog>
    );
};

export default VendorDialog;
