import { create } from "zustand";
import { devtools } from "zustand/middleware";
import axiosInstance from "../../../services/axiosInstance";

// PCB Flow Store
const usePCBFlowStore = create(
    devtools(
        (set, get) => ({
            // Loading states
            loading: {
                components: false,
                vendors: false,
                boards: false,
                transactions: false,
                dashboard: false,
            },

            // Data states
            components: [],
            vendors: [],
            componentVendors: [],
            boards: [],
            transactions: [],
            dashboardStats: {},

            // Current selections
            selectedComponent: null,
            selectedVendor: null,
            selectedBoard: null,

            // Filters and search
            filters: {
                components: {
                    search: "",
                    lowStock: false,
                },
                vendors: {
                    search: "",
                },
                boards: {
                    search: "",
                },
                transactions: {
                    component: "",
                    type: "",
                    board: "",
                },
            },

            // Actions
            setLoading: (key, value) =>
                set((state) => ({
                    loading: { ...state.loading, [key]: value },
                })),

            setFilter: (category, key, value) =>
                set((state) => ({
                    filters: {
                        ...state.filters,
                        [category]: {
                            ...state.filters[category],
                            [key]: value,
                        },
                    },
                })),

            // Component actions
            fetchComponents: async () => {
                set((state) => ({ loading: { ...state.loading, components: true } }));
                try {
                    const { filters } = get();
                    const params = new URLSearchParams();

                    if (filters.components.search) {
                        params.append("search", filters.components.search);
                    }
                    if (filters.components.lowStock) {
                        params.append("low_stock", "true");
                    }

                    const response = await axiosInstance.get(`/api/pcbflow/components/?${params}`);
                    set({ components: response.data.results || response.data });
                } catch (error) {
                    console.error("Error fetching components:", error);
                } finally {
                    set((state) => ({ loading: { ...state.loading, components: false } }));
                }
            },

            createComponent: async (componentData) => {
                try {
                    const response = await axiosInstance.post("/api/pcbflow/components/", componentData);
                    set((state) => ({
                        components: [...state.components, response.data],
                    }));
                    return response.data;
                } catch (error) {
                    console.error("Error creating component:", error);
                    throw error;
                }
            },

            updateComponent: async (componentId, componentData) => {
                try {
                    const response = await axiosInstance.patch(
                        `/api/pcbflow/components/${componentId}/`,
                        componentData
                    );
                    set((state) => ({
                        components: state.components.map((comp) => (comp.id === componentId ? response.data : comp)),
                    }));
                    return response.data;
                } catch (error) {
                    console.error("Error updating component:", error);
                    throw error;
                }
            },

            deleteComponent: async (componentId) => {
                try {
                    await axiosInstance.delete(`/api/pcbflow/components/${componentId}/`);
                    set((state) => ({
                        components: state.components.filter((comp) => comp.id !== componentId),
                    }));
                } catch (error) {
                    console.error("Error deleting component:", error);
                    throw error;
                }
            },

            adjustStock: async (componentId, adjustment, ref_link, note) => {
                try {
                    const response = await axiosInstance.post(`/api/pcbflow/components/${componentId}/adjust_stock/`, {
                        adjustment,
                        ref_link,
                        note,
                    });
                    // Refresh components to get updated stock
                    get().fetchComponents();
                    return response.data;
                } catch (error) {
                    console.error("Error adjusting stock:", error);
                    throw error;
                }
            },

            // Vendor actions
            fetchVendors: async () => {
                set((state) => ({ loading: { ...state.loading, vendors: true } }));
                try {
                    const { filters } = get();
                    const params = new URLSearchParams();

                    if (filters.vendors.search) {
                        params.append("search", filters.vendors.search);
                    }

                    const response = await axiosInstance.get(`/api/pcbflow/vendors/?${params}`);
                    set({ vendors: response.data.results || response.data });
                } catch (error) {
                    console.error("Error fetching vendors:", error);
                } finally {
                    set((state) => ({ loading: { ...state.loading, vendors: false } }));
                }
            },

            // Fetch ComponentVendor relationships
            fetchComponentVendors: async () => {
                try {
                    const { components } = get();
                    const allComponentVendors = [];

                    // Fetch vendors for each component
                    for (const component of components) {
                        try {
                            const response = await axiosInstance.get(
                                `/api/pcbflow/components/${component.id}/vendors/`
                            );
                            allComponentVendors.push(...response.data);
                        } catch (error) {
                            console.error(`Error fetching vendors for component ${component.part_number}:`, error);
                        }
                    }

                    set({ componentVendors: allComponentVendors });
                    return allComponentVendors; // Return the fetched data
                } catch (error) {
                    console.error("Error fetching component vendors:", error);
                    return []; // Return empty array on error
                }
            },

            createVendor: async (vendorData) => {
                try {
                    const response = await axiosInstance.post("/api/pcbflow/vendors/", vendorData);
                    set((state) => ({
                        vendors: [...state.vendors, response.data],
                    }));
                    return response.data;
                } catch (error) {
                    console.error("Error creating vendor:", error);
                    throw error;
                }
            },

            updateVendor: async (vendorId, vendorData) => {
                try {
                    const response = await axiosInstance.patch(`/api/pcbflow/vendors/${vendorId}/`, vendorData);
                    set((state) => ({
                        vendors: state.vendors.map((vendor) => (vendor.id === vendorId ? response.data : vendor)),
                    }));
                    return response.data;
                } catch (error) {
                    console.error("Error updating vendor:", error);
                    throw error;
                }
            },

            toggleVendorActive: async (vendorId) => {
                try {
                    const response = await axiosInstance.patch(`/api/pcbflow/vendors/${vendorId}/toggle_active/`);
                    set((state) => ({
                        vendors: state.vendors.map((vendor) => (vendor.id === vendorId ? response.data : vendor)),
                    }));
                    return response.data;
                } catch (error) {
                    console.error("Error toggling vendor active status:", error);
                    throw error;
                }
            },

            // Create ComponentVendor relationship
            createComponentVendor: async (component_target, vendorData) => {
                try {
                    const response = await axiosInstance.post(
                        `/api/pcbflow/components/${component_target.id}/add_vendor/`,
                        {
                            ...vendorData,
                            component_id: component_target.id,
                            component_part_number: component_target.part_number, // Not needed when sending component_id
                            currency: "VND", // Default to VND
                        }
                    );

                    // Refresh component vendors after creating
                    await get().fetchComponentVendors();
                    return response.data;
                } catch (error) {
                    console.error("Error creating component vendor:", error);
                    throw error;
                }
            },

            // Update ComponentVendor relationship
            updateComponentVendor: async (componentVendorId, vendorData) => {
                try {
                    const response = await axiosInstance.patch(
                        `/api/pcbflow/component-vendors/${componentVendorId}/`,
                        vendorData
                    );

                    // Refresh component vendors after updating
                    await get().fetchComponentVendors();
                    return response.data;
                } catch (error) {
                    console.error("Error updating component vendor:", error);
                    throw error;
                }
            },

            // Toggle ComponentVendor active status
            toggleComponentVendorActive: async (componentVendorId) => {
                try {
                    const response = await axiosInstance.patch(
                        `/api/pcbflow/component-vendors/${componentVendorId}/toggle_active/`
                    );

                    // Refresh component vendors after toggling
                    await get().fetchComponentVendors();
                    return response.data;
                } catch (error) {
                    console.error("Error toggling component vendor active status:", error);
                    throw error;
                }
            },

            deleteVendor: async (vendorId) => {
                try {
                    await axiosInstance.delete(`/api/pcbflow/vendors/${vendorId}/`);
                    set((state) => ({
                        vendors: state.vendors.filter((vendor) => vendor.id !== vendorId),
                    }));
                } catch (error) {
                    console.error("Error deleting vendor:", error);
                    throw error;
                }
            },

            // Board actions
            fetchBoards: async () => {
                set((state) => ({ loading: { ...state.loading, boards: true } }));
                try {
                    const { filters } = get();
                    const params = new URLSearchParams();

                    if (filters.boards.search) {
                        params.append("search", filters.boards.search);
                    }

                    const response = await axiosInstance.get(`/api/pcbflow/boards/?${params}`);
                    set({ boards: response.data.results || response.data });
                } catch (error) {
                    console.error("Error fetching boards:", error);
                } finally {
                    set((state) => ({ loading: { ...state.loading, boards: false } }));
                }
            },

            uploadBoard: async (formData) => {
                try {
                    const response = await axiosInstance.post("/api/pcbflow/boards/upload_zip/", formData, {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    });
                    set((state) => ({
                        boards: [response.data, ...state.boards],
                    }));
                    return response.data;
                } catch (error) {
                    console.error("Error uploading board:", error);
                    throw error;
                }
            },

            updateBoard: async (boardId, formData) => {
                try {
                    const response = await axiosInstance.patch(`/api/pcbflow/boards/${boardId}/`, formData, {
                        headers: {
                            "Content-Type": "multipart/form-data",
                        },
                    });
                    set((state) => ({
                        boards: state.boards.map((board) => (board.id === boardId ? response.data : board)),
                    }));
                    return response.data;
                } catch (error) {
                    console.error("Error updating board:", error);
                    throw error;
                }
            },

            deleteBoard: async (boardId) => {
                try {
                    await axiosInstance.delete(`/api/pcbflow/boards/${boardId}/`);
                    set((state) => ({
                        boards: state.boards.filter((board) => board.id !== boardId),
                    }));
                    return true;
                } catch (error) {
                    console.error("Error deleting board:", error);
                    throw error;
                }
            },

            // Dashboard actions
            fetchDashboardStats: async () => {
                set((state) => ({ loading: { ...state.loading, dashboard: true } }));
                try {
                    const response = await axiosInstance.get("/api/pcbflow/dashboard/stats/");
                    set({ dashboardStats: response.data });
                } catch (error) {
                    console.error("Error fetching dashboard stats:", error);
                } finally {
                    set((state) => ({ loading: { ...state.loading, dashboard: false } }));
                }
            },

            // Selection actions
            setSelectedComponent: (component) => set({ selectedComponent: component }),
            setSelectedVendor: (vendor) => set({ selectedVendor: vendor }),
            setSelectedBoard: (board) => set({ selectedBoard: board }),

            // Reset actions
            reset: () =>
                set({
                    components: [],
                    vendors: [],
                    boards: [],
                    transactions: [],
                    dashboardStats: {},
                    selectedComponent: null,
                    selectedVendor: null,
                    selectedBoard: null,
                }),
        }),
        {
            name: "pcb-flow-store",
        }
    )
);

export default usePCBFlowStore;
